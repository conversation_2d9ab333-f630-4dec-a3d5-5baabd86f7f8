// 面试状态
export enum InterviewStatus {
  WAIT_APPOINTMENT = 'WAIT_APPOINTMENT',
  WART_INTERVIEW = 'WART_INTERVIEW',
  WAIT_ENTRY = 'WAIT_ENTRY',
  ENTRYED = 'ENTRYED',
  INTERVIEW_FAILED = 'INTERVIEW_FAILED',
  APPOINTMENT_CANCELED = 'APPOINTMENT_CANCELED',
  ENTRY_CANCELED = 'ENTRY_CANCELED',
}

export const InterviewStatusCN: Record<InterviewStatus, string> = {
  [InterviewStatus.WAIT_APPOINTMENT]: '待约面',
  [InterviewStatus.WART_INTERVIEW]: '待面试',
  [InterviewStatus.WAIT_ENTRY]: '待入职',
  [InterviewStatus.ENTRYED]: '已入职',
  [InterviewStatus.INTERVIEW_FAILED]: '面试不通过',
  [InterviewStatus.APPOINTMENT_CANCELED]: '约面取消',
  [InterviewStatus.ENTRY_CANCELED]: '入职取消',
};

// 岗位类型
export enum JobType {
  DIAN_YUAN = 'DIAN_YUAN',
  DIAN_ZHU = 'DIAN_ZHU',
  XIAO_SHI_MEI = 'XIAO_SHI_MEI',
  YOU_SHI = 'YOU_SHI',
}

export const JobTypeCN: Record<JobType, string> = {
  [JobType.DIAN_YUAN]: '店员',
  [JobType.DIAN_ZHU]: '店助',
  [JobType.XIAO_SHI_MEI]: '小狮妹',
  [JobType.YOU_SHI]: '幼狮',
};

export enum BossAccountFunction {
  JOB_LIST = 'JOB_LIST',
  MESSAGE_LIST = 'MESSAGE_LIST',
}

export const BossAccountFunctionCN: Record<BossAccountFunction, string> = {
  [BossAccountFunction.JOB_LIST]: '自动打招呼',
  [BossAccountFunction.MESSAGE_LIST]: '自动回复',
};
