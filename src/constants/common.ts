export type PermissionScope = 'ALL' | 'BY_ROLE' | 'BY_ORGANIZATION';

export const PermissionScopeCNMap: Record<PermissionScope, string> = {
  ALL: '公开',
  BY_ROLE: '角色',
  BY_ORGANIZATION: '组织',
};

export enum StoreType {
  DIRECT = 'DIRECT',
  JOIN = 'JOIN',
}

export const StoreTypeCN: Record<StoreType, string> = {
  [StoreType.DIRECT]: '加盟T',
  [StoreType.JOIN]: '加盟M',
};

export const OldStoreTypeCN: Record<number, string> = {
  1: '加盟T',
  2: '加盟M',
};
export enum WeekDay {
  '一' = 1,
  '二',
  '三',
  '四',
  '五',
  '六',
  '日',
}

// table排序值与后端排序值的映射
export const SorterBEMap: any = {
  ascend: 'ASC',
  descend: 'DESC',
};

export enum TissueType {
  ORGANIZATION = 'ORGANIZATION',
  SHOP = 'SHOP',
}

export const TissueTypeCN: Record<TissueType, string> = {
  [TissueType.ORGANIZATION]: '组织',
  [TissueType.SHOP]: '门店',
};
