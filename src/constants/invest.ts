// 投流

export enum PlatformType {
  Meituan = 0,
  Eleme = 1,
}
export const PlatformTypeMap: Record<PlatformType, string> = {
  [PlatformType.Meituan]: '美团',
  [PlatformType.Eleme]: '饿了么',
};

// 计划状态：全部、待开始、进行中、已完成
export enum PlanStatus {
  NotStart = 0,
  Being = 1,
  Complete = 2,
}
export const PlanStatusMap: Record<PlanStatus, string> = {
  [PlanStatus.NotStart]: '待开始',
  [PlanStatus.Being]: '进行中',
  [PlanStatus.Complete]: '已完成',
};

// 计划状态：全部、待开始、进行中、已完成
export enum Status {
  待下发 = '待下发',
  待操作 = '待操作',
  下发失败 = '下发失败',
  已完成 = '已完成',
  超时未完成 = '超时未完成',
}
export const StatusMap: Record<Status, string> = {
  [Status.待下发]: '待下发',
  [Status.待操作]: '待操作',
  [Status.下发失败]: '下发失败',
  [Status.已完成]: '已完成',
  [Status.超时未完成]: '超时未完成',
};

// 统投确认 预算模式
export enum CostType {
  平均预算 = 2,
  动态预算 = 1,
}

export enum BidMode {
  PARTITION_BID = 'PARTITION_BID',
  ADD_BID = 'ADD_BID',
  SUB_BID = 'SUB_BID',
  ADD_SUB_BID = 'ADD_SUB_BID',
}

export const CostTypeMap = {
  [CostType.平均预算]: '平均预算',
  [CostType.动态预算]: '动态预算',
};

export const BidModeMap = {
  [BidMode.PARTITION_BID]: '分区出价',
  [BidMode.ADD_BID]: '动态出价（加价）',
  [BidMode.SUB_BID]: '动态出价（减价）',
  [BidMode.ADD_SUB_BID]: '动态出价（加价减价并行）',
};

export const CostTypeOptions = [
  {
    label: CostTypeMap[CostType.平均预算],
    value: CostType.平均预算,
  },
  {
    label: CostTypeMap[CostType.动态预算],
    value: CostType.动态预算,
  },
];

// 获取统投门店列表
export type GetInvestStoreListReq = {
  platform: PlatformType; // 平台类型 0-美团 1-饿了么
  period?: number; // 投放周期 1-日 2-周 3-月
  status?: PlanStatus; // 计划状态：全部、待开始、进行中、已完成
};

// 获取统投详情列表
export type GetShopPromotionIntentListReq = {
  batchId: number;
  shopIds?: string[];
  joinFlag?: number;
  statusDesc?: string;
  pushUser?: boolean;
  costType?: CostType;
  bidMode?: BidMode;
  experimentId?: string;
};

// 预算配置
export type BudgetConfig = {
  platformMinBudget: number;
  shopMinBudget: number;
  resetRate: number;
};

// 最低日预算配置
export type BudgetConfigParams = {
  brandId: number;
  userId: number;
  meituanMinBudgetConfig: BudgetConfig;
  elemeMinBudgetConfig: BudgetConfig;
};

export type JoinOrUnJoinParams = {
  intentId: number;
  optSource: number; // 操作场景 1 yyt 2 后台
  optMemberId?: number;
  joinFlag: number;
  transPreM1: number;
  costType: CostType;
  bidMode?: BidMode;
  dynamicDataList: {
    date: string;
    amtTransPreM1: number;
    transPreM1: number;
  }[];
};

export type ShopPromotionIntent = {
  shopId: string;
  shopName: string;
  areaFlag: string;
  amtTransPreM1: number;
  rateTraffic: number;
  amtOrderTransPreM1: number;
  balance: number;
  transPreM1: number;
  joinFlag: number;
  status: number;
};

export type ShopPromotionIntentListRes = {
  shopPromotionIntentList: ShopPromotionIntent[];
  totalShopNum: number;
  joinShopNum: number;
  noJoinShopNum: number;
  balanceLessShopNum: number;
};

// 获取统投计划列表
export type GetInvestProjectListReq = {
  pageNum: number;
  pageSize: number;
  planName: string;
  productTypeList: 'LOYALTY_PROMOTION' | 'ORDER_UP' | 'NEW_CUSTOMER';
  startDate: string;
  endDate: string;
  planType: number; // 1 :CREATE 2 :ADJUSTMENT 3 :DAILY_INITIALIZATION
};

/* 计划失败重试参数 */
export type FailedRetryParams = {
  userId: number;
  planId: number;
  platform: number;
};

/* 计划子项删除参数 */
export type ItemDeleteParams = {
  userId: number;
  platform: number;
  planId: number;
  planItemId: number;
};

// 获取统投管理员配置表
export type GetRefUserReq = {
  page: number;
  size: number;
  shopIds?: string[];
  userId?: string;
};
// 账号投流参数
export type GeAccountInvestParamsReq = {
  saveFlag: boolean; // true新增 false编辑
  platform: 0 | 1; // 0美团1饿了么
  brandId: number;
  accountGroupList: {
    account: string;
    groupList: {
      groupKeywordsId?: number;
      groupId: number;
      groupName: string;
      dayOfWeekKeywords: Record<string, string[]>;
    }[];
  }[];
};

// 账号投流参数
export type BatchAccountInvestParamsReq = {
  platform: 0 | 1; // 0美团1饿了么
  brandId: number;
  dayOfWeekKeywords: Record<string, string[]>;
};

export type DynamicBidConfig = {
  brandId: number;
  platform: number;
  userId: number;
  dynamicIntervalMinute: number;
  defaultResetRate: number;
  defaultInitBidRate: number;
  defaultDynamicStep: number;
  dynamicBidGroups: {
    groupId: number;
    resetRate: number;
    initBidRate: number;
    dynamicStep: number;
    groupName: string;
  }[];
};

/**
 * 加投计划管理列表查询 */
export type AddIntentBatchParams = {
  name?: number;
  shopIds?: number[];
  dateMonth?: string;
  status?: number;
};

/**
 * 门店费用配置 */
export type DetailDtos = {
  id?: number;
  shopId?: number;
  shopName?: string;
  date?: string;
  amount?: number;
  isEdit?: boolean;
};
/**
 * 加投计划管理 新增 & 编辑 */
export type AddIntentBatchAddOrEdit = {
  id?: number;
  name: string;
  platform: number;
  dateMonth: string;
  detailDtos: DetailDtos[];
  optUserId: string;
};

/**
 * 加投计划详情列表查询 */
export type AddIntentListParams = {
  batchId: number;
  shopIds?: number[];
  joinFlag?: boolean;
};

/**
 * 加投计划详情 新增 & 编辑 */
export type AddIntentAddOrEdit = {
  batchId: number;
  addIntentEditDetailDtos?: {
    id: number;
    amount: number;
    joinFlag?: boolean;
  }[];
};

export type CreateTimePeriodParams = {
  platform?: PlatformType; // 平台
  templateName?: string; // 模板名称
  timePeriodList?: string[]; // 投流时段列表
  shopList?: {
    // 关联门店列表
    shopId: string;
    shopName: string;
  }[];
};

export type TimePeriodListParams = {
  platform: PlatformType;
  shopIds: string[];
  timePeriodTemplateName: string;
  pageNum: number;
  pageSize: number;
};

export type TimePeriodListItem = {
  id: number;
  platform: PlatformType;
  templateName: string;
  defaultFlag?: number; // 是否是默认模板，0非默认，1默认模板。当值为1时，前端应对关联门店做特殊处理，显示为"所有门店
  timePeriodList: string[];
  simpleShopDTOList: {
    shopId: string;
    shopName: string;
  }[];
  operatorName: string;
  operatorDateTime: string;
};

export type ExperimentListParams = {
  brandId: number;
  platform: PlatformType;
  shopIds: string[];
  experimentTag?: string;
  experimentIds?: number[];
  pageNum: number;
  pageSize: number;
};

export type ExperimentListItem = {
  experimentId: number;
  experimentName: string;
  shops: {
    shopId: string;
    shopName: string;
  }[];
  createUserId: number;
  createUserName: string;
  createTime: string;
  updateUserId: number;
  updateUserName: string;
  updateTime: string;
};

export type ExperimentDetail = {
  experimentId: number;
  experimentName: string;
  relExperimentTags: string[];
  disableShopOperations: number;
  shops: {
    shopId: string;
    shopName: string;
  }[];
  experimentTags: string[];
  disableShopOperation: number;
};

export type CreateExperimentParams = {
  /** 更新时必传，传了就是更新 */
  experimentId: number;
  experimentName: string;
  /** 关联实验标签 */
  relExperimentTags: string[];
  /** 是否禁用门店操作 1-禁用 0-不禁用 */
  disableShopOperations: number;
  shops: {
    shopId: string;
    shopName: string;
  }[];
  userId: number;
  userName: string;
};
