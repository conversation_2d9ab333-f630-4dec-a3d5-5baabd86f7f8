export enum FeedbackType {
  BUSINESS = 'BUSINESS',
  ABILITY = 'ABILITY',
}

export const FeedbackTypeCN: Record<FeedbackType, string> = {
  [FeedbackType.BUSINESS]: '业务问题',
  [FeedbackType.ABILITY]: '功能问题',
};

export enum FeedbackModuleType {
  HOME = 'HOME',
  DATA = 'DATA',
  SELF_INSPECTION_TASK = 'SELF_INSPECTION_TASK',
  PATROL_INSPECTION_TASK = 'PATROL_INSPECTION_TASK',
  SELF_INSPECTION_REPORT = 'SELF_INSPECTION_REPORT',
  PATROL_INSPECTION_REPORT = 'PATROL_INSPECTION_REPORT',
  CAMERA = 'CAMERA',
  OTHERS = 'OTHERS',
}

export const FeedbackModuleTypeCN: Record<FeedbackModuleType, string> = {
  [FeedbackModuleType.HOME]: '首页',
  [FeedbackModuleType.DATA]: '数据',
  [FeedbackModuleType.SELF_INSPECTION_TASK]: '自检任务',
  [FeedbackModuleType.PATROL_INSPECTION_TASK]: '巡检任务',
  [FeedbackModuleType.SELF_INSPECTION_REPORT]: '自检报告',
  [FeedbackModuleType.PATROL_INSPECTION_REPORT]: '巡检报告',
  [FeedbackModuleType.CAMERA]: '摄像头',
  [FeedbackModuleType.OTHERS]: '其他',
};
