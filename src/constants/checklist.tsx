import { ReactNode } from 'react';
import { Tag } from 'antd';

export enum ChecklistStatus {
  ENABLE = 'ENABLE',
  DISABLE = 'DISABLE',
}

export const ChecklistStatusCN: Record<ChecklistStatus, string> = {
  [ChecklistStatus.DISABLE]: '未生效',
  [ChecklistStatus.ENABLE]: '生效中',
};

export enum CheckItemType {
  Judgement = 'JUDGE', // 判断型
  Scoring = 'SCORE', // 打分型
}

export const CheckItemTypeCN: Record<CheckItemType, string> = {
  [CheckItemType.Judgement]: '判断型',
  [CheckItemType.Scoring]: '打分型',
};

export enum FractionalType {
  Score = 'ADD_SCORE',
  Deduction = 'REDUCE_SCORE',
}
export const FractionalTypeCN: Record<FractionalType, string> = {
  [FractionalType.Score]: '得分项',
  [FractionalType.Deduction]: '扣分项',
};

export enum DeductType {
  ALL_CATEGORY_SCORE = 'ALL_CATEGORY_SCORE',
  WORKSHEET_SCORE = 'WORKSHEET_SCORE',
  REPORT_SCORE = 'REPORT_SCORE',
}

export const DeductTypeCN: Record<DeductType, string> = {
  [DeductType.ALL_CATEGORY_SCORE]: '整个分类的得分',
  [DeductType.WORKSHEET_SCORE]: '检查表得分',
  [DeductType.REPORT_SCORE]: '最终报告得分',
};

export enum ChecklistType {
  SELF = 'SELF',
  PATROL = 'PATROL',
  VALIDITY = 'VALIDITY',
  DISINFECTION = 'DISINFECTION',
  DIFFERENT_ITEM_PATROL = 'DIFFERENT_ITEM_PATROL',
}

export const ChecklistTypeCN: Record<ChecklistType, string> = {
  [ChecklistType.SELF]: '自检检查表',
  [ChecklistType.VALIDITY]: '效期检查表',
  [ChecklistType.PATROL]: '巡检检查表',
  [ChecklistType.DISINFECTION]: '消杀检查表',
  [ChecklistType.DIFFERENT_ITEM_PATROL]: '差异项巡检表',
};

export enum CheckItemAttribute {
  RED_LINE = 'RED_LINE',
  NECESSARY = 'NECESSARY',
  YELLOW = 'YELLOW',
  KEY = 'KEY',
  PENALTY = 'PENALTY',
  POSITIVE = 'POSITIVE', // 阳性指标项
  RAT_POSITIVE = 'RAT_POSITIVE', // 鼠阳指标项
  COCKROACH_POSITIVE = 'COCKROACH_POSITIVE', // 蟑阳指标项
}

export const CheckItemAttributeCN: Record<CheckItemAttribute, string> = {
  [CheckItemAttribute.RED_LINE]: 'S项',
  [CheckItemAttribute.NECESSARY]: '必检项',
  [CheckItemAttribute.YELLOW]: 'M项',
  [CheckItemAttribute.KEY]: '关键项',
  [CheckItemAttribute.PENALTY]: '罚款项',
  [CheckItemAttribute.POSITIVE]: '阳性指标项',
  [CheckItemAttribute.RAT_POSITIVE]: '鼠阳指标项',
  [CheckItemAttribute.COCKROACH_POSITIVE]: '蟑阳指标项',
};

export const CheckItemAttributeTag: Record<CheckItemAttribute, ReactNode> = {
  [CheckItemAttribute.RED_LINE]: (
    <Tag color="#e23c39" key={CheckItemAttribute.RED_LINE}>
      {CheckItemAttributeCN[CheckItemAttribute.RED_LINE]}
    </Tag>
  ),
  [CheckItemAttribute.NECESSARY]: (
    <Tag color="green" key={CheckItemAttribute.NECESSARY}>
      {CheckItemAttributeCN[CheckItemAttribute.NECESSARY]}
    </Tag>
  ),
  [CheckItemAttribute.YELLOW]: (
    <Tag color="warning" key={CheckItemAttribute.YELLOW}>
      {CheckItemAttributeCN[CheckItemAttribute.YELLOW]}
    </Tag>
  ),
  [CheckItemAttribute.KEY]: (
    <Tag color="blue" key={CheckItemAttribute.KEY}>
      {CheckItemAttributeCN[CheckItemAttribute.KEY]}
    </Tag>
  ),
  [CheckItemAttribute.PENALTY]: (
    <Tag color="volcano" key={CheckItemAttribute.PENALTY}>
      {CheckItemAttributeCN[CheckItemAttribute.PENALTY]}
    </Tag>
  ),
  [CheckItemAttribute.POSITIVE]: (
    <Tag color="pink" key={CheckItemAttribute.POSITIVE}>
      {CheckItemAttributeCN[CheckItemAttribute.POSITIVE]}
    </Tag>
  ),
  [CheckItemAttribute.RAT_POSITIVE]: (
    <Tag color="pink" key={CheckItemAttribute.RAT_POSITIVE}>
      {CheckItemAttributeCN[CheckItemAttribute.RAT_POSITIVE]}
    </Tag>
  ),
  [CheckItemAttribute.COCKROACH_POSITIVE]: (
    <Tag color="pink" key={CheckItemAttribute.COCKROACH_POSITIVE}>
      {CheckItemAttributeCN[CheckItemAttribute.COCKROACH_POSITIVE]}
    </Tag>
  ),
};

export const CheckItemAttributeDesc: Record<CheckItemAttribute, (type?: any, radio?: any) => ReactNode> = {
  [CheckItemAttribute.RED_LINE]: (type: any, radio: any) => (
    <p>{`若此项不合格，将对${DeductTypeCN[type]}扣除${radio}%`}</p>
  ),
  [CheckItemAttribute.NECESSARY]: () => <p>若此项未检查，则无法提交报告</p>,
  [CheckItemAttribute.YELLOW]: () => <p>若此项不合格，整个分类的得分扣除100%</p>,
  [CheckItemAttribute.KEY]: () => <p>若此项不合格，整个分类的得分扣除100%</p>,
  [CheckItemAttribute.PENALTY]: () => <p>若此项不合格要罚款，不影响报告得分和统计，仅作为检查项标签</p>,
  [CheckItemAttribute.POSITIVE]: () => <p>若此项不通过整个报告打【阳性报告】标签</p>,
  [CheckItemAttribute.RAT_POSITIVE]: () => <p>若此项不通过整个报告打【阳性报告】标签</p>,
  [CheckItemAttribute.COCKROACH_POSITIVE]: () => <p>若此项不通过整个报告打【阳性报告】标签</p>,
};

export enum ProcessingActionType {
  PHOTOGRAPH = 'PHOTOGRAPH',
  JUDGE = 'JUDGE',
  GAP_FILLING = 'GAP_FILLING',
}

export const ProcessingActionTypeCN: Record<ProcessingActionType, string> = {
  [ProcessingActionType.PHOTOGRAPH]: '拍照',
  [ProcessingActionType.JUDGE]: '判断',
  [ProcessingActionType.GAP_FILLING]: '填空',
};
