import React, { ReactNode } from 'react';
import { Tag } from 'antd';

export enum StoreType {
  DIRECT = 1,
  JOIN = 2,
}

export const StoreTypeCN: Record<StoreType, string> = {
  [StoreType.DIRECT]: '加盟T',
  [StoreType.JOIN]: '加盟M',
};

export enum StoreStatus {
  PREPARING = 1,
  OPEN = 2,
  OFFLINE = 3,
  CLOSE = 4,
  WAITOPENED = 8,
}

export enum ShopStatus {
  'PREPARING' = 'PREPARING',
  'OPEN' = 'OPEN',
  'OFFLINE' = 'OFFLINE',
  'CLOSE' = 'CLOSE',
  'TO_BE_OPENED' = 'TO_BE_OPENED',
}

export enum ShopStatusCN {
  'PREPARING' = '筹备中',
  'OPEN' = '营业中',
  'OFFLINE' = '歇业中',
  'CLOSE' = '闭店中',
  'TO_BE_OPENED' = '待营业',
}

export const StoreStatusCN: Record<StoreStatus, string> = {
  [StoreStatus.PREPARING]: '筹备中',
  [StoreStatus.OPEN]: '营业中',
  [StoreStatus.OFFLINE]: '歇业中',
  [StoreStatus.CLOSE]: '闭店中',
  [StoreStatus.WAITOPENED]: '待营业',
};

export const StoreStatusTag: Record<StoreStatus, ReactNode> = {
  [StoreStatus.PREPARING]: <Tag>{StoreStatusCN[StoreStatus.PREPARING]}</Tag>,
  [StoreStatus.OPEN]: <Tag color="success">{StoreStatusCN[StoreStatus.OPEN]}</Tag>,
  [StoreStatus.OFFLINE]: <Tag color="warning">{StoreStatusCN[StoreStatus.OFFLINE]}</Tag>,
  [StoreStatus.CLOSE]: <Tag color="error">{StoreStatusCN[StoreStatus.CLOSE]}</Tag>,
  [StoreStatus.WAITOPENED]: <Tag>{StoreStatusCN[StoreStatus.WAITOPENED]}</Tag>,
};

export enum BindCameraStatus {
  NotBound = 0,
  Bound = 1,
}

export const BindCameraStatusCN: Record<BindCameraStatus, string> = {
  [BindCameraStatus.NotBound]: '未绑定',
  [BindCameraStatus.Bound]: '已绑定',
};
