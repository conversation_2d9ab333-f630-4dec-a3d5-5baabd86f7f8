export enum DisinfectionType {
  FIRST_STRUCTURE = 'FIRST_STRUCTURE', // 一道结构消杀
  SECOND_STRUCTURE = 'SECOND_STRUCTURE', // 二道结构消杀
  DAILY = 'DAILY', // 日常消杀
  EXIGENCY = 'EXIGENCY', // 紧急消杀
}
export enum PositiveType {
  POSITIVE = 'POSITIVE', // 阳性指标
  RAT_POSITIVE = 'RAT_POSITIVE', // 鼠阳指标
  COCKROACH_POSITIVE = 'COCKROACH_POSITIVE', // 蟑阳指标
}

export const DisinfectionTypeCN: Record<DisinfectionType, string> = {
  [DisinfectionType.FIRST_STRUCTURE]: '一道结构消杀',
  [DisinfectionType.SECOND_STRUCTURE]: '二道结构消杀',
  [DisinfectionType.DAILY]: '日常消杀',
  [DisinfectionType.EXIGENCY]: '紧急消杀',
};
export const PositiveTypeCN: Record<PositiveType, string> = {
  [PositiveType.POSITIVE]: '阳性指标',
  [PositiveType.RAT_POSITIVE]: '鼠阳指标',
  [PositiveType.COCKROACH_POSITIVE]: '蟑阳指标',
};
export enum DisinfectionStatus {
  NEW = 'NEW',
  WAIT = 'WAIT',
  RUNNING = 'RUNNING',
  SUSPEND = 'SUSPEND',
  CLOSE = 'CLOSE',
  COMPLETE = 'COMPLETE',
  REJECT = 'REJECT',
  CANCEL = 'CANCEL',
}

export const DisinfectionStatusCN: Record<DisinfectionStatus, string> = {
  [DisinfectionStatus.NEW]: '待分配',
  [DisinfectionStatus.WAIT]: '待开始',
  [DisinfectionStatus.RUNNING]: '进行中',
  [DisinfectionStatus.SUSPEND]: '中止',
  [DisinfectionStatus.CLOSE]: '已关闭',
  [DisinfectionStatus.COMPLETE]: '已完成',
  [DisinfectionStatus.REJECT]: '已驳回',
  [DisinfectionStatus.CANCEL]: '已取消',
};

export enum DisinfectionRectificationStatus {
  WAIT_REFORM = 'WAIT_REFORM',
  WAIT_AUDIT = 'WAIT_AUDIT',
  EXPIRED = 'EXPIRED',
  REFORMED = 'REFORMED',
  CANCEL = 'CANCEL',
}

export const DisinfectionRectificationStatusCN: Record<DisinfectionRectificationStatus, string> = {
  [DisinfectionRectificationStatus.WAIT_REFORM]: '待整改',
  [DisinfectionRectificationStatus.WAIT_AUDIT]: '待审核',
  [DisinfectionRectificationStatus.EXPIRED]: '逾期',
  [DisinfectionRectificationStatus.REFORMED]: '已整改',
  [DisinfectionRectificationStatus.CANCEL]: '已取消',
};

export enum DisinfectionTaskProcess {
  ASSIGN_SUPERVISION = 'ASSIGN_SUPERVISION',
  ASSIGN_COMPANY = 'ASSIGN_COMPANY',
  MANAGER_SIGN = 'MANAGER_SIGN',
  DISINFECTION_SIGN = 'DISINFECTION_SIGN',
  DISINFECTION_SIGN_OUT = 'DISINFECTION_SIGN_OUT',
  RESET_TASK = 'RESET_TASK',
  SUBMIT_TASK = 'SUBMIT_TASK',
  SUBMIT_REPORT = 'SUBMIT_REPORT',
  NEW_TASK = 'NEW_TASK',
  START_TASK = 'START_TASK',
  SUSPEND_TASK = 'SUSPEND_TASK',
  CLOSE_TASK = 'CLOSE_TASK',
  UPDATE_TASK_ARRIVE_TIME = 'UPDATE_TASK_ARRIVE_TIME',
  CHANGE_MANAGER = 'CHANGE_MANAGER',
  REJECT_TASK = 'REJECT_TASK',
  CANCEL_TASK = 'CANCEL_TASK',
  RE_ASSIGN_COMPANY = 'RE_ASSIGN_COMPANY',
}

export const DisinfectionTaskProcessCN: Record<DisinfectionTaskProcess, string> = {
  [DisinfectionTaskProcess.ASSIGN_SUPERVISION]: '分配食安主管',
  [DisinfectionTaskProcess.ASSIGN_COMPANY]: '分配消杀公司',
  [DisinfectionTaskProcess.RE_ASSIGN_COMPANY]: '重新分配了消杀公司',
  [DisinfectionTaskProcess.MANAGER_SIGN]: '项目经理打卡',
  [DisinfectionTaskProcess.DISINFECTION_SIGN]: '消杀人员打卡',
  [DisinfectionTaskProcess.DISINFECTION_SIGN_OUT]: '消杀人员签离',
  [DisinfectionTaskProcess.RESET_TASK]: '重启任务',
  [DisinfectionTaskProcess.SUBMIT_TASK]: '提交任务',
  [DisinfectionTaskProcess.SUBMIT_REPORT]: '提交报告',
  [DisinfectionTaskProcess.NEW_TASK]: '新建任务',
  [DisinfectionTaskProcess.START_TASK]: '开始执行任务',
  [DisinfectionTaskProcess.SUSPEND_TASK]: '中止任务',
  [DisinfectionTaskProcess.CLOSE_TASK]: '关闭任务',
  [DisinfectionTaskProcess.UPDATE_TASK_ARRIVE_TIME]: '修改任务期望到店时间',
  [DisinfectionTaskProcess.CHANGE_MANAGER]: '项目经理变更',
  [DisinfectionTaskProcess.REJECT_TASK]: '驳回了消杀申请',
  [DisinfectionTaskProcess.CANCEL_TASK]: '取消了消杀申请',
};
