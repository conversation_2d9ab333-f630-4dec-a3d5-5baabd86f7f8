import { ReactNode } from 'react';
import { Badge } from 'antd';

export enum TaskType {
  ONCE = 'ONCE', // 单次任务
  CIRCULATION = 'CIRCULATION', // 循环任务
}

export const TaskTypeCN: Record<TaskType, string> = {
  [TaskType.ONCE]: '单次任务',
  [TaskType.CIRCULATION]: '循环任务',
};

export enum RectificationStatus {
  UNRECTIFIED = 'UNRECTIFIED', //  待整改
  UNAUDITED = 'UNAUDITED', //  待审核
  RECTIFIED = 'RECTIFIED', //  已过期
  OVERDUE = 'OVERDUE', //  已整改
}

export enum StudySituation {
  WAITING_COMPLETE = 'WAITING_COMPLETE', // 未学习
  COMPLETED = 'COMPLETED', // 已学习
}
export enum RoutineType {
  NORMAL = 'NORMAL', // 到店巡检
  VIDEO = 'VIDEO', // 视频云巡检
  FOOD_SAFETY_NORMAL = 'FOOD_SAFETY_NORMAL', // 食安线下稽核
  FOOD_SAFETY_VIDEO = 'FOOD_SAFETY_VIDEO', // 食安视频云巡检
}
export const RectificationStatusCN: Record<RectificationStatus, string> = {
  [RectificationStatus.UNRECTIFIED]: '待整改',
  [RectificationStatus.UNAUDITED]: '待审核',
  [RectificationStatus.OVERDUE]: '已过期',
  [RectificationStatus.RECTIFIED]: '已整改',
};
export const StudySituationCN: Record<StudySituation, string> = {
  [StudySituation.WAITING_COMPLETE]: '未学习',
  [StudySituation.COMPLETED]: '已学习',
};
export const RoutineTypeCN: Record<RoutineType, string> = {
  [RoutineType.NORMAL]: '到店巡检',
  [RoutineType.VIDEO]: '视频云巡检',
  [RoutineType.FOOD_SAFETY_NORMAL]: '食安线下稽核',
  [RoutineType.FOOD_SAFETY_VIDEO]: '食安线上稽核',
};
export const RectificationStatusEnum: Record<RectificationStatus, { text: string; status: string }> = {
  [RectificationStatus.UNRECTIFIED]: {
    text: RectificationStatusCN[RectificationStatus.UNRECTIFIED],
    status: 'processing',
  },
  [RectificationStatus.UNAUDITED]: {
    text: RectificationStatusCN[RectificationStatus.UNAUDITED],
    status: 'default',
  },
  [RectificationStatus.OVERDUE]: {
    text: RectificationStatusCN[RectificationStatus.OVERDUE],
    status: 'success',
  },
  [RectificationStatus.RECTIFIED]: {
    text: RectificationStatusCN[RectificationStatus.RECTIFIED],
    status: 'error',
  },
};

// 自检整改状态
export enum SelfRectifyStatus {
  CREATED = 'CREATED',
  REFORM_SUBMITTED = 'REFORM_SUBMITTED',
  SUPERVISOR_REJECTED = 'SUPERVISOR_REJECTED',
  COMPLETED = 'COMPLETED',
  PAST_DUE = 'PAST_DUE',
}

export const SelfRectifyStatusCN: Record<SelfRectifyStatus, string> = {
  [SelfRectifyStatus.CREATED]: '待整改',
  [SelfRectifyStatus.REFORM_SUBMITTED]: '已提交',
  [SelfRectifyStatus.SUPERVISOR_REJECTED]: '驳回',
  [SelfRectifyStatus.COMPLETED]: '已整改',
  [SelfRectifyStatus.PAST_DUE]: '逾期',
};

export const SelfRectifyStatusEnum: Record<SelfRectifyStatus, { text: string; status: string }> = {
  [SelfRectifyStatus.CREATED]: {
    text: SelfRectifyStatusCN[SelfRectifyStatus.CREATED],
    status: 'processing',
  },
  [SelfRectifyStatus.REFORM_SUBMITTED]: {
    text: SelfRectifyStatusCN[SelfRectifyStatus.REFORM_SUBMITTED],
    status: 'default',
  },
  [SelfRectifyStatus.SUPERVISOR_REJECTED]: {
    text: SelfRectifyStatusCN[SelfRectifyStatus.SUPERVISOR_REJECTED],
    status: 'warning',
  },
  [SelfRectifyStatus.COMPLETED]: {
    text: SelfRectifyStatusCN[SelfRectifyStatus.COMPLETED],
    status: 'success',
  },
  [SelfRectifyStatus.PAST_DUE]: {
    text: SelfRectifyStatusCN[SelfRectifyStatus.PAST_DUE],
    status: 'error',
  },
};

// 自检状态
export enum TaskStatus {
  START = 'CREATED', // 待开始
  UNDERWAY = 'IN_PROGRESS', // 进行中
  ENDED = 'FINISHED', // 已结束
  CANCELLED = 'CANCELLED', // 已取消
}

// 巡检状态
export enum RoutineTaskStatus {
  WAIT_START = 'WAIT_START',
  PROCESS = 'PROCESS',
  FINISHED = 'FINISHED',
  CANCELED = 'CANCELED',
}
// 转办任务状态
export enum TransferTaskStatus {
  CREATED = 'CREATED',
  APPROVED = 'APPROVED',
  REJECTED = 'REJECTED',
  CANCELED = 'CANCELED',
  WAITING_CONFIRM = 'WAITING_CONFIRM',
}
export enum TransferType {
  NORMAL = 'NORMAL',
  DIAGNOSTIC = 'DIAGNOSTIC',
}
export const TaskStatusCN: Record<TaskStatus, string> = {
  [TaskStatus.START]: '待开始',
  [TaskStatus.UNDERWAY]: '进行中',
  [TaskStatus.ENDED]: '已结束',
  [TaskStatus.CANCELLED]: '已取消',
};

export const ConfirmStatusesCN = {
  WAIT_ACCEPT: '待确认',
  ACCEPT: '已同意',
  REFUSED: '已驳回',
  CANCELED: '已取消',
};

export const RoutineTaskStatusCN: Record<RoutineTaskStatus, string> = {
  [RoutineTaskStatus.WAIT_START]: '待开始',
  [RoutineTaskStatus.PROCESS]: '进行中',
  [RoutineTaskStatus.FINISHED]: '已结束',
  [RoutineTaskStatus.CANCELED]: '已取消',
};

export const TransferTaskStatusCN: Record<TransferTaskStatus, string> = {
  [TransferTaskStatus.CREATED]: '待审核',
  [TransferTaskStatus.APPROVED]: '已处理',
  [TransferTaskStatus.REJECTED]: '驳回',
  [TransferTaskStatus.CANCELED]: '已取消',
  [TransferTaskStatus.WAITING_CONFIRM]: '执行时段确认中',
};

export const TransferTypeCN: Record<TransferType, string> = {
  [TransferType.NORMAL]: '到店巡检(权限内)',
  [TransferType.DIAGNOSTIC]: '诊断巡检',
};

export enum InspectionType {
  NORMAL = 'NORMAL',
  VIDEO = 'VIDEO',
  CROSS = 'CROSS',
  FOOD_SAFETY_NORMAL = 'FOOD_SAFETY_NORMAL',
  FOOD_SAFETY_VIDEO = 'FOOD_SAFETY_VIDEO',
  DIAGNOSTIC = 'DIAGNOSTIC',
  FOOD_SAFETY_ARRIVE_SHOP = 'FOOD_SAFETY_ARRIVE_SHOP',
  DIFFERENCE_ITEM_ARRIVE_SHOP = 'DIFFERENCE_ITEM_ARRIVE_SHOP',
}

export const InspectionTypeCN: Record<InspectionType, string> = {
  [InspectionType.NORMAL]: '到店巡检(权限内)',
  [InspectionType.VIDEO]: '视频云巡检',
  [InspectionType.CROSS]: '到店巡检(权限外)',
  [InspectionType.FOOD_SAFETY_NORMAL]: '食安线下稽核',
  [InspectionType.FOOD_SAFETY_VIDEO]: '食安线上稽核',
  [InspectionType.DIAGNOSTIC]: '诊断巡检',
  [InspectionType.FOOD_SAFETY_ARRIVE_SHOP]: '食安稽核到店辅导',
  [InspectionType.DIFFERENCE_ITEM_ARRIVE_SHOP]: '差异项到店巡检',
};

export const ShortInspectionTypeCN: Record<any, string> = {
  [InspectionType.NORMAL]: '到店巡检',
  [InspectionType.VIDEO]: '视频云巡检',
  [InspectionType.FOOD_SAFETY_NORMAL]: '食安线下稽核',
  [InspectionType.FOOD_SAFETY_VIDEO]: '食安线上稽核',
  [InspectionType.DIAGNOSTIC]: '诊断巡检',
  [InspectionType.FOOD_SAFETY_ARRIVE_SHOP]: '食安稽核到店辅导',
  [InspectionType.DIFFERENCE_ITEM_ARRIVE_SHOP]: '差异项到店巡检',
};

export enum ReportStatus {
  NOT_STARTED = 'NOT_STARTED', // 待提交
  SUBMITTED = 'SUBMITTED', // 已提交
  CONFIRMED = 'CONFIRMED', // 已确认
}

export const ReportStatusCN: Record<ReportStatus, string> = {
  [ReportStatus.NOT_STARTED]: '待提交',
  [ReportStatus.SUBMITTED]: '已提交',
  [ReportStatus.CONFIRMED]: '已确认',
};

export const ReportStatusEnum: Record<ReportStatus, { text: string; status: string }> = {
  [ReportStatus.NOT_STARTED]: {
    text: ReportStatusCN[ReportStatus.NOT_STARTED],
    status: 'default',
  },
  [ReportStatus.SUBMITTED]: {
    text: ReportStatusCN[ReportStatus.SUBMITTED],
    status: 'warning',
  },
  [ReportStatus.CONFIRMED]: {
    text: ReportStatusCN[ReportStatus.CONFIRMED],
    status: 'success',
  },
};

export enum ReviewStatus {
  WAIT_REVIEW = 'WAIT_REVIEW', // 待提交
  REVIEWED = 'REVIEWED', // 已提交
}
export const ReviewStatusCN: Record<ReviewStatus, string> = {
  [ReviewStatus.WAIT_REVIEW]: '待点评',
  [ReviewStatus.REVIEWED]: '已点评',
};

export const TaskStatusBadge: Record<TaskStatus, ReactNode> = {
  [TaskStatus.START]: <Badge status="warning" text={TaskStatusCN[TaskStatus.START]} />,
  [TaskStatus.UNDERWAY]: <Badge status="processing" text={TaskStatusCN[TaskStatus.UNDERWAY]} />,
  [TaskStatus.ENDED]: <Badge status="default" text={TaskStatusCN[TaskStatus.ENDED]} />,
  [TaskStatus.CANCELLED]: <Badge status="error" text={TaskStatusCN[TaskStatus.CANCELLED]} />,
};
export const NormalTaskStatusBadge: Record<RoutineTaskStatus, ReactNode> = {
  [RoutineTaskStatus.WAIT_START]: <Badge status="warning" text={RoutineTaskStatusCN[RoutineTaskStatus.WAIT_START]} />,
  [RoutineTaskStatus.PROCESS]: <Badge status="processing" text={RoutineTaskStatusCN[RoutineTaskStatus.PROCESS]} />,
  [RoutineTaskStatus.FINISHED]: <Badge status="default" text={RoutineTaskStatusCN[RoutineTaskStatus.FINISHED]} />,
  [RoutineTaskStatus.CANCELED]: <Badge status="error" text={RoutineTaskStatusCN[RoutineTaskStatus.CANCELED]} />,
};

export enum TaskRepeatType {
  /**
   * @abstract 每日一次
   */
  DAY = 'DAY',
  /**
   * @abstract 每周一次
   */
  WEEK = 'WEEK',
  /**
   * @abstract 每月一次
   */
  MONTH = 'MONTH',
}

export const TaskRepeatTypeCN: Record<TaskRepeatType, string> = {
  [TaskRepeatType.DAY]: '每日',
  [TaskRepeatType.WEEK]: '每周',
  [TaskRepeatType.MONTH]: '每月',
};

export const TaskTimeFormat: Record<TaskType, string> = {
  [TaskType.ONCE]: 'YYYY-MM-DD HH:mm:ss',
  [TaskType.CIRCULATION]: 'YYYY-MM-DD',
};

// 巡检报告状态
export enum InspectionReportStatus {
  NOT_STARTED = 'NOT_STARTED',
  SUBMITTED = 'SUBMITTED',
  CONFIRMED = 'CONFIRMED',
}

export const InspectionReportStatusCN: Record<InspectionReportStatus, string> = {
  [InspectionReportStatus.NOT_STARTED]: '待提交',
  [InspectionReportStatus.SUBMITTED]: '已提交',
  [InspectionReportStatus.CONFIRMED]: ' 已确认',
};

export const InspectionReportStatusEnum: Record<InspectionReportStatus, { text: string; status: string }> = {
  [InspectionReportStatus.NOT_STARTED]: {
    text: InspectionReportStatusCN[InspectionReportStatus.NOT_STARTED],
    status: 'default',
  },
  [InspectionReportStatus.SUBMITTED]: {
    text: InspectionReportStatusCN[InspectionReportStatus.SUBMITTED],
    status: 'processing',
  },
  [InspectionReportStatus.CONFIRMED]: {
    text: InspectionReportStatusCN[InspectionReportStatus.CONFIRMED],
    status: 'success',
  },
};

// 巡检点评状态
export enum InspectionReviewStatus {
  REVIEWED = 'REVIEWED',
  NOT_REVIEWED = 'NOT_REVIEWED',
  NO_NEEDED_REVIEWS = 'NO_NEEDED_REVIEWS',
  REVIEWED_EXPIRE = 'REVIEWED_EXPIRE',
}

export const InspectionReviewStatusCN: Record<InspectionReviewStatus, string> = {
  [InspectionReviewStatus.REVIEWED]: '已点评',
  [InspectionReviewStatus.NOT_REVIEWED]: '待点评',
  [InspectionReviewStatus.NO_NEEDED_REVIEWS]: '无需点评',
  [InspectionReviewStatus.REVIEWED_EXPIRE]: '点评超时',
};

export const InspectionReviewStatusEnum: Record<InspectionReviewStatus, { text: string; status: string }> = {
  [InspectionReviewStatus.NOT_REVIEWED]: {
    text: InspectionReviewStatusCN[InspectionReviewStatus.NOT_REVIEWED],
    status: 'default',
  },
  [InspectionReviewStatus.REVIEWED]: {
    text: InspectionReviewStatusCN[InspectionReviewStatus.REVIEWED],
    status: 'success',
  },
  [InspectionReviewStatus.NO_NEEDED_REVIEWS]: {
    text: InspectionReviewStatusCN[InspectionReviewStatus.NO_NEEDED_REVIEWS],
    status: 'default',
  },
  [InspectionReviewStatus.REVIEWED_EXPIRE]: {
    text: InspectionReviewStatusCN[InspectionReviewStatus.REVIEWED_EXPIRE],
    status: 'default',
  },
};

// 诊断类型
export enum DiagnosisType {
  RED = 'RED',
  ORANGE = 'ORANGE',
  YELLOW = 'YELLOW',
}

export const DiagnosisTypeCN: Record<DiagnosisType, string> = {
  [DiagnosisType.RED]: '秩序白银',
  [DiagnosisType.ORANGE]: '倔强青铜',
  [DiagnosisType.YELLOW]: '尊贵铂金',
};

// 诊断策略枚举
export enum DiagnosisTacticsEnum {
  RED = 'RED',
  ORANGE = 'ORANGE',
  YELLOW = 'YELLOW',
  GREEN = 'GREEN',
  WHITE = 'WHITE',
}

export const DiagnosisTacticsCN: Record<DiagnosisTacticsEnum, string> = {
  [DiagnosisTacticsEnum.RED]: '秩序白银',
  [DiagnosisTacticsEnum.ORANGE]: '倔强青铜',
  [DiagnosisTacticsEnum.YELLOW]: '尊贵铂金',
  [DiagnosisTacticsEnum.GREEN]: '至尊星耀',
  [DiagnosisTacticsEnum.WHITE]: '传奇王者',
};

// 学习状态
export enum StudyStatus {
  WAITING_SEND = 'WAITING_SEND',
  WAITING_COMPLETE = 'WAITING_COMPLETE',
  COMPLETED = 'COMPLETED',
  EXPIRED_WAITING_COMPLETE = 'EXPIRED_WAITING_COMPLETE',
  EXPIRED_COMPLETE = 'EXPIRED_COMPLETE',
  NO_COMPLETED = 'NO_COMPLETED',
}

export const StudyStatusCN: Record<StudyStatus, string> = {
  [StudyStatus.WAITING_SEND]: '待下发',
  [StudyStatus.WAITING_COMPLETE]: '待完成',
  [StudyStatus.COMPLETED]: '已完成',
  [StudyStatus.EXPIRED_WAITING_COMPLETE]: '逾期未完成',
  [StudyStatus.EXPIRED_COMPLETE]: '逾期已完成',
  [StudyStatus.NO_COMPLETED]: '未完成',
};

// 食安稽核到店辅导任务类型
export enum TutorTaskType {
  UNQUALIFIED_SHOP = 'UNQUALIFIED_SHOP',
  RETEST_SHOP = 'RETEST_SHOP',
}

export const TutorTaskTypeCN: Record<TutorTaskType, string> = {
  [TutorTaskType.UNQUALIFIED_SHOP]: '不合格门店',
  [TutorTaskType.RETEST_SHOP]: '复检门店',
};

// 食安稽核到店辅导任务状态
export enum TutorTaskStatus {
  CANCELED = 'CANCELED',
  COMPLETED = 'COMPLETED',
  EXPIRED = 'EXPIRED',
  NOT_READY = 'NOT_READY',
  PROCESS = 'PROCESS',
  TRANSFERRING = 'TRANSFERRING',
  WAIT_AUDIT = 'WAIT_AUDIT',
  WAIT_CONFIRM = 'WAIT_CONFIRM',
}

export const TutorTaskStatusCN: Record<TutorTaskStatus, string> = {
  [TutorTaskStatus.CANCELED]: '已取消',
  [TutorTaskStatus.COMPLETED]: '已完成',
  [TutorTaskStatus.EXPIRED]: '已过期',
  [TutorTaskStatus.NOT_READY]: '待开始',
  [TutorTaskStatus.PROCESS]: '进行中',
  [TutorTaskStatus.TRANSFERRING]: '转派中',
  [TutorTaskStatus.WAIT_AUDIT]: '待审核',
  [TutorTaskStatus.WAIT_CONFIRM]: '待确认',
};

// 食安稽核到店辅导任务报告状态
export enum TutorReportStatus {
  NOT_STARTED = 'NOT_STARTED',
  SUBMITTED = 'SUBMITTED',
  CONFIRMED = 'CONFIRMED',
  WAIT_REVIEW = 'WAIT_REVIEW',
  REVIEWED = 'REVIEWED',
}

export const TutorReportStatusCN: Record<TutorReportStatus, string> = {
  [TutorReportStatus.NOT_STARTED]: '待提交',
  [TutorReportStatus.SUBMITTED]: '已提交',
  [TutorReportStatus.CONFIRMED]: '已确认',
  [TutorReportStatus.WAIT_REVIEW]: '待点评',
  [TutorReportStatus.REVIEWED]: '已点评',
};

// 食安稽核到店辅导任务报告点评状态
export enum TutorReviewStatus {
  REVIEWED = 'REVIEWED',
  NOT_REVIEWED = 'NOT_REVIEWED',
  NO_NEEDED_REVIEWS = 'NO_NEEDED_REVIEWS',
}

export const TutorReviewStatusCN: Record<TutorReviewStatus, string> = {
  [TutorReviewStatus.REVIEWED]: '已点评',
  [TutorReviewStatus.NOT_REVIEWED]: '未点评',
  [TutorReviewStatus.NO_NEEDED_REVIEWS]: '无需点评',
};

// 食安稽核到店整改类型
export enum TutorArriveShopType {
  ARRIVED = 'ARRIVED',
  VIDEO_NOT_SEE = 'VIDEO_NOT_SEE',
  NOT_ARRIVED = 'NOT_ARRIVED',
}

export const TutorArriveShopTypeCN: Record<TutorArriveShopType, string> = {
  [TutorArriveShopType.ARRIVED]: '已到店',
  [TutorArriveShopType.VIDEO_NOT_SEE]: '监控无法查看',
  [TutorArriveShopType.NOT_ARRIVED]: '未到店',
};

// 食安稽核到店任务进度类型
export enum TutorProcessShopType {
  CREATED = 'CREATED',
  FOOD_SAFETY_ARRIVE_SHOP_CONFIRM = 'FOOD_SAFETY_ARRIVE_SHOP_CONFIRM',
  FOOD_SAFETY_ARRIVE_SHOP_EXE_PERIOD_AUDIT = 'FOOD_SAFETY_ARRIVE_SHOP_EXE_PERIOD_AUDIT',
  TASK_SIGN_IN = 'TASK_SIGN_IN',
  FOOD_SAFETY_ARRIVE_SHOP_CONFIRM_EXAM_PASS = 'FOOD_SAFETY_ARRIVE_SHOP_CONFIRM_EXAM_PASS',
  SUBMIT = 'SUBMIT',
  FOOD_SAFETY_ARRIVE_SHOP_OVERDUE_NOT_EXE_TASK_TRANSFER = 'FOOD_SAFETY_ARRIVE_SHOP_OVERDUE_NOT_EXE_TASK_TRANSFER',
  TASK_EXPIRED = 'TASK_EXPIRED',
  FOOD_SAFETY_ARRIVE_SHOP_CANCEL_CONFIRM_EXAM_PASS = 'FOOD_SAFETY_ARRIVE_SHOP_CANCEL_CONFIRM_EXAM_PASS',
}

// 食安稽核到店任务进度类型
export enum TutorArrivalTimeType {
  _24_HOURS_ARRIVE_SHOP = '_24_HOURS_ARRIVE_SHOP',
  _48_HOURS_ARRIVE_SHOP = '_48_HOURS_ARRIVE_SHOP',
  OVER_48_HOURS_ARRIVE_SHOP = 'OVER_48_HOURS_ARRIVE_SHOP',
  _72_HOURS_ARRIVE_SHOP = '_72_HOURS_ARRIVE_SHOP',
  OVER_72_HOURS_ARRIVE_SHOP = 'OVER_72_HOURS_ARRIVE_SHOP',
}

// 食安稽核到店任务进度审核状态
export enum TutorAuditStatus {
  AGREE = 'AGREE',
  REJECTED = 'REJECTED',
}
