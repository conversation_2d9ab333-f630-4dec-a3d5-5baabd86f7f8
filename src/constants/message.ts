export enum MessageModule {
  SELF_TASK = 'SELF_TASK',
  PATROL_TASK = 'PATROL_TASK',
  DISINFECTION_TASK = 'DISINFECTION_TASK',
  PATROL_FOOD_SAFETY = 'PATROL_FOOD_SAFETY',
  THIRD_PARTY_SYSTEM = 'THIRD_PARTY_SYSTEM',
  DAILY_DISINFECTION = 'DAILY_DISINFECTION',
}
export enum MessageStatus {
  ON = 'ON',
  OFF = 'OFF',
}
export enum MessageChannel {
  FEI_SHU = 'FEI_SHU',
  APP = 'APP',
}

export const MessageModuleCN: any = {
  [MessageModule.SELF_TASK]: '自检任务',
  [MessageModule.PATROL_TASK]: '巡检任务',
  [MessageModule.DISINFECTION_TASK]: '消杀任务',
  [MessageModule.PATROL_FOOD_SAFETY]: '食安稽核',
  [MessageModule.DAILY_DISINFECTION]: '日常消杀',
  [MessageModule.THIRD_PARTY_SYSTEM]: '第三方系统',
};
export const MessageChannelCN: any = {
  [MessageChannel.FEI_SHU]: '飞书',
  [MessageChannel.APP]: 'APP',
};

export enum MessageScene {
  SELF_TASK_START_REMIND_REMIND = 'SELF_TASK_START_REMIND_REMIND',
  SELF_TASK_UN_DONE_REMIND_REMIND = 'SELF_TASK_UN_DONE_REMIND_REMIND',
  SELF_REPORT_WAIT_RECTIFIED_NOTIFY = 'SELF_REPORT_WAIT_RECTIFIED_NOTIFY',
  SELF_REPORT_RECTIFIED_CC_NOTIFY = 'SELF_REPORT_RECTIFIED_CC_NOTIFY',
  RECTIFICATION_ITEM_REJECTION_NOTIFY = 'RECTIFICATION_ITEM_REJECTION_NOTIFY',
  RECTIFICATION_ITEM_EXPIRATION_REMIND = 'RECTIFICATION_ITEM_EXPIRATION_REMIND',
}
export const MessageSceneCN: any = {
  [MessageScene.SELF_TASK_START_REMIND_REMIND]: '自检任务开始提醒',
  [MessageScene.SELF_TASK_UN_DONE_REMIND_REMIND]: '自检任务未完成提醒',
  [MessageScene.SELF_REPORT_WAIT_RECTIFIED_NOTIFY]: '自检报告点评及整改情况推送',
  [MessageScene.SELF_REPORT_RECTIFIED_CC_NOTIFY]: '自检报告不合格项整改抄送',
  [MessageScene.RECTIFICATION_ITEM_REJECTION_NOTIFY]: '自检整改审核驳回',
  [MessageScene.RECTIFICATION_ITEM_EXPIRATION_REMIND]: '自检报告不合格项整改到期提醒',
};
