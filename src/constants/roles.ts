import { convertEnumToOptions } from '@/utils/convert';

export enum RoleTypes {
  /** 超级管理员 */
  SUPER_ADMIN = 'SUPER_ADMIN',
  /** 管理员 */
  ADMIN = 'ADMIN',
  /** 普通用户 */
  MEMBER = 'MEMBER'
}

export const RoleTypeNames = {
  [RoleTypes.SUPER_ADMIN]: '超级管理员',
  [RoleTypes.ADMIN]: '管理员',
  [RoleTypes.MEMBER]: '普通用户'
};

export const RoleTypeOptions = convertEnumToOptions(RoleTypes, RoleTypeNames);

/** 系统保留角色的id */
export const RESERVED_ROLE_IDS = [1, 2, 3];
