export enum PatrolType {
  NORMAL = 'NORMAL',
  VIDEO = 'VIDEO',
  FOOD_SAFETY_NORMAL = 'FOOD_SAFETY_NORMAL',
  FOOD_SAFETY_VIDEO = 'FOOD_SAFETY_VIDEO',
  DIAGNOSTIC = 'DIAGNOSTIC',
}

export const CreatePatrolTypeCN: Record<PatrolType, string> = {
  [PatrolType.NORMAL]: '到店巡检',
  [PatrolType.VIDEO]: '视频云巡检（无需进行签到）',
  [PatrolType.FOOD_SAFETY_NORMAL]: '食安线下稽核',
  [PatrolType.FOOD_SAFETY_VIDEO]: '食安线上稽核',
  [PatrolType.DIAGNOSTIC]: '诊断任务',
};

export enum SignType {
  SIGN_IN = 'SIGN_IN',
  SIGN_IN_OUT = 'SIGN_IN_OUT',
  SIGN_NEITHER = 'SIGN_NEITHER',
  SYSTEM = 'SYSTEM',
}

export const SignTypeCN: Record<SignType, string> = {
  [SignType.SIGN_IN]: '需要签到',
  [SignType.SIGN_IN_OUT]: '需要签到和签离',
  [SignType.SIGN_NEITHER]: '不需要',
  [SignType.SYSTEM]: '跟随系统设置',
};

export enum PackConfigType {
  NEED_PACKAGE = 'NEED_PACKAGE',
  NOT_NEED_PACKAGE = 'NOT_NEED_PACKAGE',
  FOLLOWING_SYSTEM = 'FOLLOWING_SYSTEM',
}

export const PackConfigTypeCN: Record<PackConfigType, string> = {
  [PackConfigType.NEED_PACKAGE]: '需要',
  [PackConfigType.NOT_NEED_PACKAGE]: '不需要',
  [PackConfigType.FOLLOWING_SYSTEM]: '跟随系统设置',
};
