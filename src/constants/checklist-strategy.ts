export enum ChecklistStrategyType {
  SELF = 'SELF',
  PATROL = 'PATROL',
  VALIDITY = 'VALIDITY',
  DISINFECTION = 'DISINFECTION',
  DIFFERENT_ITEM_PATROL = 'DIFFERENT_ITEM_PATROL',
}

export const ChecklistStrategyTypeCN: Record<ChecklistStrategyType, string> = {
  [ChecklistStrategyType.SELF]: '自检检查表',
  [ChecklistStrategyType.VALIDITY]: '效期检查表',
  [ChecklistStrategyType.PATROL]: '巡检检查表',
  [ChecklistStrategyType.DISINFECTION]: '消杀检查表',
  [ChecklistStrategyType.DIFFERENT_ITEM_PATROL]: '差异项巡检表',
};

export enum ChecklistStrategyStatus {
  ENABLE = 'ENABLE',
  DISABLE = 'DISABLE',
}

export const ChecklistStrategyStatusCN: Record<ChecklistStrategyStatus, string> = {
  [ChecklistStrategyStatus.ENABLE]: '生效中',
  [ChecklistStrategyStatus.DISABLE]: '未生效',
};
