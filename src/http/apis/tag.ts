import { del, get, post, put } from '..';
import { URLRequestPrefix } from '../config';

//根据类型获取标签列表
export const getTags = async (group: 'WORKSHEET' | 'SOP') =>
  await get(`${URLRequestPrefix.OM}/corp/label/list/simple`, { params: { group } });

//获取标签列表-分页
export const getTagList = async (params: {
  pageNo: number;
  pageSize: number;
  group: 'WORKSHEET' | 'SOP';
  orderType?: string | 'ASC' | 'DESC';
}) => await get(`${URLRequestPrefix.OM}/corp/label/page`, { params });

//新增标签
export const createTag = async (data: { group: 'WORKSHEET' | 'SOP'; name: string }) =>
  await post(`${URLRequestPrefix.OM}/corp/label`, data);
//删除标签
export const deleteTag = async (id: number) => await del(`${URLRequestPrefix.OM}/corp/label/${id}`);

//编辑更新标签
export const updateTag = async (data: { id: number; name: string }) =>
  await put(`${URLRequestPrefix.OM}/corp/label`, data);

/**
 * 权限标签管理
 */

//获取权限标签列表
export const getPermissionTagList = async (params: { name: string; role: number[] }) => {
  return await get(`${URLRequestPrefix.OM}/corp/permission-label/list?${params}`);
};

//获取权限标签下拉列表
export const getPermissionTagSelectList = async () =>
  await get(`${URLRequestPrefix.OM}/corp/permission-label/list/by-permission`, {});
//新增权限标签
export const createPermissionTag = async (data: { roles: number[]; name: string }) =>
  await post(`${URLRequestPrefix.OM}/corp/permission-label`, data);
//删除权限标签
export const deletePermissionTag = async (data: { id: number }) =>
  await post(`${URLRequestPrefix.OM}/corp/permission-label/delete`, data);

//编辑更新权限标签
export const updatePermissionTag = async (data: { id: number; roles: number[] }) =>
  await post(`${URLRequestPrefix.OM}/corp/permission-label/update-role`, data);
