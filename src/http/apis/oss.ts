import { get, post } from '..';
import { URLRequestPrefix } from '../config';

export type TOSSToken = {
  accessKeyId: string;
  accessKeySecret: string;
  bucket: string;
  expiration: number;
  prefix: string;
  securityToken: string;
};

type TUploadParams = {
  bucket: string;
  key: string;
  originName: string;
  remark?: string;
  userId?: number;
};

// type TUpload = {
//   contentType: string;
//   fileType: string;
//   id: string;
//   name: string;
//   url: string;
// };

export enum TWatermark {
  none = 'none',
  default = 'default',
  company = 'company',
  extra = 'extra',
}

export type FileBusinessType = {
  fileBusinessType:
    | 'SELF'
    | 'PATROL'
    | 'ROUTINE_TASK'
    | 'USER_PHOTO'
    | 'USER_PHOTO_CHECK'
    | 'SOP'
    | 'DISINFECTION'
    | 'ANNOUNCEMENT'
    | 'TMP';
};
export const getOSSToken = async (data?: FileBusinessType) =>
  await get(
    `${URLRequestPrefix.OM}/common/sts${data?.fileBusinessType ? `?fileBusinessType=${data?.fileBusinessType}` : ''}`,
  );

export const uploadXlxs = async (data) => await post(`${URLRequestPrefix.OM}/common/oss/getFileUrl`, data);

export const uploadImage = async (data: TUploadParams, watermark: TWatermark) => {
  const url = {
    none: `${URLRequestPrefix.OM}/common/file/upload/image`,
    default: `${URLRequestPrefix.OM}/common/file/upload/image/watermark`,
    company: `${URLRequestPrefix.OM}/common/file/upload/image/watermark/company`,
    extra: `${URLRequestPrefix.OM}/common/file/upload/image/watermarkOfShop`,
  }[watermark];

  return post(url, data);
};

export const uploadVideo = async (data: TUploadParams) =>
  post(`${URLRequestPrefix.OM}/common/file/upload/video/watermark`, data);
