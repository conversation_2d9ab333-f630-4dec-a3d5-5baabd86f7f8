import { get, post } from '..';
import { URLRequestPrefix } from '../config';

export type DisinfectionListParams = {
  companyName?: string;
  status?: string;
};
export type EmployeeListParams = {
  hasAlive?: boolean;
  id: number;
  name?: string;
  phone?: string;
  roleId?: number;
};
// 获取消杀公司档案列表
export const getDisinfectionList = async (data: DisinfectionListParams) =>
  await post(`${URLRequestPrefix.OM}/common/disinfection/company/page`, data);

export const getDisinfectionDetail = async (id: number) =>
  await get(`${URLRequestPrefix.OM}/common/disinfection/company/detail`, { params: { id } });
export const getDisinfectionScope = async (id: number) =>
  await get(`${URLRequestPrefix.OM}/common/disinfection/company/service-scope`, { params: { id } });

// 获取消杀公司详情-公司员工列表
export const getEmployeeList = async (data: EmployeeListParams) =>
  await post(`${URLRequestPrefix.OM}/common/disinfection/company/employee/list`, data);

export const getGroupLink = async (id: number) =>
  await get(`${URLRequestPrefix.OM}/common/disinfection/company/group-link`, { params: { id } });

// ?id=37289
