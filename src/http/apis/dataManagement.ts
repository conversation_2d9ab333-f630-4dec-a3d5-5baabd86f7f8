import { Key } from 'react';
import { get, post } from '..';
import { URLRequestPrefix } from '../config';

export interface DataManagementListReq {
  accountName?: string; // 招聘专员名称，可选
  city?: string; // 市，可选
  createAtEnd?: string; // 提交时间结束，ISO 8601 格式，可选
  createAtStart?: string; // 创建时间开始，ISO 8601 格式，可选
  district?: string; // 区(县)，可选
  entryDateEnd?: string; // 入职时间结束，ISO 8601 格式，可选
  entryDateStart?: string; // 入职时间开始，ISO 8601格式，可选
  fullTime?: boolean; // 是否全职，可选
  groupId?: number; // 组织id，64位整数，可选
  jobType?: string; // 岗位类型，可选
  offerDateEnd?: string; // offer发放时间结束，ISO 8601 格式，可选
  offerDateStart?: string; // offer发放时间开始，ISO 8601格式，可选
  pageNum?: number; // 分页页页码，64位整数，可选
  pageSize?: number; // 每页大小，64位整数，可选
  phone?: string; // 手机号，可选
  province?: string; // 省，可选
  shopId?: string; // 门店id（门店编号），可选
  shopName?: string; // 门店名称，可选
  status?: string; // 状态，可选
  weixinNo?: string; // 微信号，可选
}

export interface DataManagementListItem {
  accountName?: string; // 招聘专员名称，可选
  candidateName?: string; // 候选人名称，可选
  city?: string; // 市，可选
  createAt?: string; // 创建时间，ISO 8601 格式，可选
  createBy?: string; // 创建人，可选
  createByName?: string; // 创建人姓名，可选
  degreeName?: string; // 学历，可选
  deleted?: number; // 删除状态，0或1，可选
  district?: string; // 区，可选
  entryDate?: string; // 入职日期，ISO 8601 格式，可选
  fullTime?: boolean; // 是否全职，可选
  gender?: boolean; // 性别，可选
  id?: number; // 主键，64位整数，可选
  jobType?: string; // 岗位类型，可选
  jobTypeName?: string; // 岗位类型名称，可选
  offerDate?: string; // OFFER日期，ISO 8601 格式，可选
  phone?: string; // 候选人电话，可选
  province?: string; // 省，可选
  shopId?: string; // 门店编码，可选
  shopName?: string; // 门店名称，可选
  source?: string; // 招聘渠道，可选
  status?: string; // 状态，可选
  statusName?: string; // 状态名称，可选
  updateAt?: string; // 更新时间，ISO 8601 格式，可选
  updateBy?: string; // 更新人，可选
  updateByName?: string; // 更新人姓名，可选
  weixinNo?: string; // 微信号，可选
}

export interface addDataManagementListItemReq {
  accountName?: string; // 招聘专员名称，可选
  candidateName?: string; // 候选人姓名，可选
  degreeName?: string; // 学历，可选
  entryDate?: string; // 入职日期，ISO 8601 格式，可选
  fullTime?: boolean; // 是否全职，可选
  gender?: boolean; // 性别，可选
  jobType?: string; // 岗位类型，可选
  offerDate?: string; // offer发放日期，ISO 8601 格式，可选
  phone?: string; // 手机号，可选
  shopId?: string; // 门店编码，可选
  shopName?: string; // 门店名称，可选
  source?: string; // 招聘渠道，可选
  status?: string; // 状态，可选
  weixinNo?: string; // 微信号，可选
}
export interface editDataManagementListItemReq extends addDataManagementListItemReq {
  id: string; // 候选人创建结束时间，ISO 8601 格式
}
export interface ShopInfo {
  address?: string; // 地址，可选
  city?: string; // 城市，可选
  district?: string; // 区县/区，可选
  fightNameFullPath?: string; // 全路径名称，可选
  province?: string; // 省，可选
  shopId?: string; // 门店ID，可选
  shopManagerNickname?: string; // 门店经理昵称，可选
  shopManagerUserId?: number; // 门店经理用户ID，32位整数，可选
  shopName?: string; // 门店名称，可选
  shopStatus?: number; // 门店状态，32位整数，可选
  supervisorUserId?: number; // 监督者用户ID，32位整数，可选
  type?: number; // 类型，32位整数，可选
}

/** 获取人工招聘数据管理列表 */
export function getDataManagementList(data: DataManagementListReq) {
  return post<PageRes<DataManagementListItem>>(`${URLRequestPrefix.RM}/manualRecruitCandidate/getPage`, data);
}
// 新增
export function addDataManagementListItem(data: addDataManagementListItemReq) {
  return post(`${URLRequestPrefix.RM}/manualRecruitCandidate/add`, data);
}

// 编辑
export function editDataManagementListItem(data: editDataManagementListItemReq) {
  return post(`${URLRequestPrefix.RM}/manualRecruitCandidate/edit`, data);
}

// 删除
export function deleteDataManagementListItem(data: { ids: Key[] }) {
  return post(`${URLRequestPrefix.RM}/manualRecruitCandidate/delete`, data);
}

// 导入
export function importExcelDataManagementListItem(data: any) {
  return post(`${URLRequestPrefix.RM}/manualRecruitCandidate/importExcel`, data);
}

// 下载导入模版
export function getTemplateUrl() {
  return post(`${URLRequestPrefix.RM}/manualRecruitCandidate/getTemplateUrl`, undefined, {
    responseType: 'blob',
  });
}

/** 根据门店编号返回门店信息 */
export function getShopInfoByShopId({ shopId }: { shopId: string }) {
  return get(`${URLRequestPrefix.RM}/manualRecruitCandidate/getShopInfoByShopId?shopId=${shopId}`) as Promise<ShopInfo>;
}
