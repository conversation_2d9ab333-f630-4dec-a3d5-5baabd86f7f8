import { get, post } from '..';
import { URLRequestPrefix } from '../config';
import { StoreType } from '@/constants/common';
import { DisinfectionType } from '@/constants/disinfection';
import { TutorArriveShopType } from '@/constants/task';

// 获取消杀报告列表
export type GetDisinfectionReportListParams = {
  groupId?: number;
  shopIds?: string[];
  pageNum: number;
  pageSize: number;
  shopType?: StoreType;
  disinfectionType?: DisinfectionType;
  passed?: boolean;
  disinfectionCompanyId?: number;
  disinfectionUserId?: number;
  reportCreateTimeEnd?: string;
  reportCreateTimeStart?: string;
};
export interface IWorksheetDetail {
  actualScore: number;
  categoryCount: number;
  checkItemCount: number;
  fullScore: number;
  reportScore: number;
  qualifiedCategoryCount: number;
  qualifiedItemCount: number;
  statistics: Statistic;
  worksheetList: WorksheetList[];
}
export interface CategoryStatistic {
  fullScore: number;
  id: number;
  itemCount: number;
  name: string;
  qualifiedItemCount: number;
  score: number;
}
export interface TagStatistic {
  configType: string;
  quantity: number;
  rate: number;
  tag: string;
}
export interface Statistic {
  categoryStatistics: CategoryStatistic[];
  tagStatistics: TagStatistic[];
}

interface ResourceImage {
  id: string;
  type: string;
  url: string;
}

export interface Item {
  accentedTermTags: string[];
  allowAlbum: string;
  copyRecipients: string;
  createTime: string;
  deductRatio: number;
  hasApply: boolean;
  hasFillItem: boolean;
  id: number;
  itemImages: string[];
  itemScore: number;
  itemType: string;
  mustChooseNonconformityReasons: boolean;
  nonconformityReasons: string[];
  notQualifiedButtonName: string;
  notQualifiedMustUpload: boolean;
  qualified: boolean;
  qualifiedButtonName: string;
  qualifiedMustUpload: boolean;
  reformMustUpload: boolean;
  reportDisplayScore: boolean;
  resourceImages: ResourceImage[];
  scoreMax: number;
  scoreMin: number;
  scoreType: string;
  scoringStandard: string;
  shopId: string;
  sopId: number;
  sort: number;
  stermDeductionConfigType: string;
  taskDate: string;
  taskId: number;
  updateTime: string;
  worksheetCategoryId: number;
  worksheetCategoryName: string;
  worksheetId: number;
  worksheetItemContent: string;
  worksheetItemId: number;
  worksheetItemName: string;
  worksheetName: string;
}
export interface categoriesItems {
  accentedTermTags: string[];
  allowAlbum: string;
  copyRecipients: null;
  createTime: string;
  deductRatio: number;
  hasApply: boolean;
  hasFillItem: boolean;
  id: number;
  itemImages: null | [];
  selectedNonconformityReasons: string[];
  otherReason: string;
  itemRemark: string;
  itemScore: null;
  itemType: 'JUDGE' | 'SCORE';
  mustChooseNonconformityReasons: boolean;
  nonconformityReasons: [];
  notQualifiedButtonName: string;
  notQualifiedMustUpload: boolean;
  qualified: null | boolean;
  qualifiedButtonName: string;
  qualifiedMustUpload: boolean;
  reformMustUpload: boolean;
  reportDisplayScore: boolean;
  resourceImages: any;
  scoreMax: number;
  scoreMin: number;
  scoreType: string;
  scoringStandard: string;
  shopId: string;
  sopId: number;
  sort: number;
  stermDeductionConfigType: string;
  taskDate: null;
  taskId: number;
  updateTime: string;
  worksheetCategoryId: number;
  worksheetCategoryName: string;
  worksheetId: number;
  worksheetItemContent: string;
  worksheetItemId: number;
  worksheetItemName: string;
  worksheetName: string;
}
export interface Category {
  filledCount: number;
  itemCount: number;
  items: categoriesItems[];
  sort: number;
  worksheetCategoryId: number;
  worksheetCategoryName: string;
  worksheetId: number;
}
export interface WorksheetList {
  categories: Category[];
  createTime: string;
  filledCount: number;
  fullScore: number;
  id: number;
  importance: number;
  itemCount: number;
  passScore: number;
  sort: number;
  taskId: number;
  updateTime: string;
  worksheetId: number;
  worksheetName: string;
}
export interface IReportInfo {
  arriveTime: string;
  createTime: string;
  createUserId: number;
  createUserName: string;
  createUserPhone: string;
  disinfectionCompanyId: number;
  disinfectionCompanyName: string;
  disinfectionType: string;
  id: number;
  reportIssueCount: number;
  reportNoReformCount: number;
  reportSubmitTime: string;
  reportSubmitUserId: number;
  reportSubmitUserName: string;
  reportSummary: string;
  shopId: string;
  shopName: string;
  reportPositive: boolean;
  reportSubmitTaskProcess: { resourceImages: ResourceImage[] };
}
export const getDisinfectionReportList = async (data: GetDisinfectionReportListParams) =>
  await post(`${URLRequestPrefix.OM}/common/disinfection/report/pc-page`, data);

// 导出消杀报告列表
export const exportDisinfectionReportList = async (data: GetDisinfectionReportListParams) =>
  await post(`${URLRequestPrefix.OM}/common/disinfection/report/export`, data);

// 消杀报告门店状态信息
export const getDisinfectionShopStatus = async (id: number) =>
  await get<IReportInfo>(`${URLRequestPrefix.OM}/common/disinfection/report/reportDetail`, {
    params: { id },
  });

// 消杀报告详情
export const getDisinfectionDetail = async (taskId: number) =>
  await get<IWorksheetDetail>(`${URLRequestPrefix.OM}/common/disinfection/report/worksheetDetail`, {
    params: { taskId },
  });

// 食安稽核到店辅导报告列表
export interface GetTutorReportListReq {
  endDate: string;
  groupId?: number | null;
  inspectorIds?: number[] | null;
  pageNo: number | null;
  pageSize: number | null;
  passed?: boolean | null;
  reportReviewPassed?: boolean | null;
  reviewStatus?: string;
  roleIdList?: number[] | null;
  shopIdList?: string[] | null;
  shopType?: string;
  startDate: string;
  worksheetId?: number | null;
  [property: string]: any;
}
export const getTutorReportList = async (data: GetTutorReportListReq) =>
  await post(`${URLRequestPrefix.OM}/corp/patrol/report/food-safety/arrive-shop/list-page`, data);

// 导出食安稽核到店辅导报告
export const exportTutorReport = async (data: GetTutorReportListReq) =>
  await post(`${URLRequestPrefix.OM}/corp/patrol/report/food-safety/arrive-shop/lis-page/export`, data);

// 食安稽核确认
export interface ConfirmTutorAuditReq {
  arriveShopRectifyRemark?: null | string;
  arriveShopRectifyType?: TutorArriveShopType;
  examSituationConfirm: boolean | null;
  examSituationConfirmRemark?: null | string;
  taskId: number | null;
  [property: string]: any;
}

export const confirmTutorAudit = async (data: ConfirmTutorAuditReq) =>
  await post(`${URLRequestPrefix.OM}/corp/patrol/report/food-safety/arrive-shop/online-audit-confirm`, data);

// 获取食安稽核确认详情
export const getConfirmTutorAuditInfo = async (taskId: string) =>
  await get(`${URLRequestPrefix.OM}/corp/patrol/report/food-safety/arrive-shop/${taskId}/online-audit-confirm`);
