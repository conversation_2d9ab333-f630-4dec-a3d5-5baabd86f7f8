import { get, post, put } from '..';
import { URLRequestPrefix } from '../config';
import { HttpResult } from '../result';

export const getSystemConfig = async () => await get(`${URLRequestPrefix.OM}/common/system/config/all`);

export const saveSystemConfig = async (data) =>
  await post(`${URLRequestPrefix.OM}/common/system/config/save-batch`, data);
export const getPatrolTransferConfig = async () =>
  await get(`${URLRequestPrefix.OM}/common/system/config/patrol-transfer/list
`);

export const savePatrolTransferConfig = async (data) =>
  await post(`${URLRequestPrefix.OM}/common/system/config/patrol-transfer/save`, data);

// 获取消杀基础配置
export type DisinfectionSystemConfigData = {
  bpmInfo?: {
    deleted: boolean;
    id: number;
    name: string;
  };
  firstTaskWorksheetId?: {
    deleted: boolean;
    id: number;
    name: string;
  };
  secondTaskWorksheetId?: {
    deleted: boolean;
    id: number;
    name: string;
  };
  suspendResonList?: string[];
};
export const getDisinfectionSystemConfig = async (): Promise<HttpResult<DisinfectionSystemConfigData>> =>
  await get(`${URLRequestPrefix.OM}/common/disinfection/config`);

// 保存消杀基础配置
export type DisinfectionSystemConfigParams = {
  bpmId?: number;
  firstTaskWorksheetId?: number;
  secondTaskWorksheetId?: number;
  suspendResonList?: string[];
  exigencyDisinfectionWorksheetConfig?: any[];
};
export const saveDisinfectionSystemConfig = async (data: DisinfectionSystemConfigParams) =>
  await post(`${URLRequestPrefix.OM}/common/disinfection/config/save`, data);

export const localRectify = async (data: { id: number; reason: string; images: number[] }) =>
  await post(`${URLRequestPrefix.OM}/corp/patrol/report/item/local-rectify`, data);

// 查询加盟商处罚流程抄送对象配置
export const getRecipientConfig = async () =>
  await get(`${URLRequestPrefix.OM}/common/system/config/partner-punish/routine/recipient/list`);

// 保存加盟商处罚流程抄送对象配置
export const saveRecipientConfig = async (data: any) =>
  await post(`${URLRequestPrefix.OM}/common/system/config/partner-punish/routine/recipient/save`, { data });

// 获取消杀紧急配置
export const getDisinfectionEmergencyConfig = async () =>
  (await get(`${URLRequestPrefix.OM}/common/system/config`, {
    params: {
      key: 'EXIGENCY_DISINFECTION_TASK_REJECT_REASONS',
    },
  })) as unknown as Promise<{ value: string }>;

// 驳回紧急消杀请求
export const rejectEmergencyDisinfection = async (data: {
  taskId: number;
  reasonInfo?: { reason: string; remark?: string };
}) => await put(`${URLRequestPrefix.OM}/corp/disinfection/reject`, data);

// 获取人员异常兜底配置
export const getGuaranteeRole = async () =>
  await get(`${URLRequestPrefix.OM}/common/system/config/distribute-user-default-role/list`);
