import type { PaginationParams } from '@/utils/pagination';
import { del, post } from '..';
import { URLRequestPrefix } from '../config';

//查询品牌分页列表
export const getBrandsPage = async (data: PaginationParams<{ brandsName?: string }>) =>
  await post(`${URLRequestPrefix.OM}/common/partner/brand/page`, data);

// 添加品牌
export const addBrand = async (data: { brandName: string }) =>
  await post(`${URLRequestPrefix.OM}/common/partner/brand/add`, data);
// 修改品牌
export const updateBrand = async (data: { brandName: string }) =>
  await post(`${URLRequestPrefix.OM}/common/partner/brand/edit`, data);
// 删除品牌
export const delBrand = async (id: number) => await del(`${URLRequestPrefix.OM}/common/partner/brand/delete/${id}`);
