import { del, get, post, put } from '..';
import { URLRequestPrefix } from '../config';
import { getTags } from './tag';

// 查询sop分类列表
export const getSopCategoryList = async () => await post(`${URLRequestPrefix.OM}/corp/sop/getAllCategoryId`);

// 新增sop分类
export const createSopCategory = async (categoryName: string) =>
  await post(`${URLRequestPrefix.OM}/corp/sop/addCategory`, { categoryName });

// 删除sop分类
export const deleteSopCategory = async (id: number) =>
  await post(`${URLRequestPrefix.OM}/corp/sop/category/delete`, { id });

//获取sop标签列表
export const getSopTags = () => getTags('SOP');

export type CreateSopParams = {
  categoryId: number;
  name: string;
  fileUrl: string;
  fileName: string;
  fileEnum: 'PDF' | 'IMG';
  usePermissionScope?: string;
  usePermissionIds?: number[];
};

// 新增sop
export const createSop = async (data: CreateSopParams) =>
  await post(`${URLRequestPrefix.OM}/corp/sop/addSopContent`, data);

// 编辑sop
export const updateSop = async (
  data: Partial<CreateSopParams> & {
    id: number;
    sopStatusEnum?: 'PUBLISH' | 'UNPUBLISH';
    removeUsePermissionIds?: number[];
    addUsePermissionIds?: number[];
  }
) => await post(`${URLRequestPrefix.OM}/corp/sop/updateSopContent`, data);

//获取sop列表（分页）
export const getSopList = async (data: any) => await post(`${URLRequestPrefix.OM}/corp/sop/page`, data);

//批量设置SOP权限
export const batchSetSopPermission = async (data: {
  ids: number[];
  usePermissionIds: number[];
  usePermissionScope: string;
}) => await put(`${URLRequestPrefix.OM}/corp/sop/batch/usePermission`, data);

//批量设置SOP标签
export const batchSetSopTag = async (data: { bindMethod: 'COVER' | 'APPEND'; ids: number[]; labelIds: number[] }) =>
  await put(`${URLRequestPrefix.OM}/corp/sop/batch/labels`, data);

//更新sop状态
export const updateSopStatus = async (id: number, status?: 'PUBLISH' | 'UNPUBLISH') =>
  await put(`${URLRequestPrefix.OM}/corp/sop/status`, { id, status });

//发布Sop
export const publishSop = async (id: number) => await updateSopStatus(id, 'PUBLISH');

//获取sop列表  权限过滤
export const getSopListWithPermission = async (data?: any) =>
  await post(`${URLRequestPrefix.OM}/corp/sop/v2/getAllContentDto`, data);

//删除sop
export const deleteSop = async (id: number) => await del(`${URLRequestPrefix.OM}/corp/sop/${id}`);

//获取sop引用量
export const getSopRef = async (sopId: number, params) =>
  await get(`${URLRequestPrefix.OM}/corp/sop/${sopId}/ref/detail/page`, { params });

//获取sop引用的常规任务类型
export const getTaskTypeRef = async (data) => await post(`${URLRequestPrefix.OM}/common/task/type/getListBySopId`, data);
