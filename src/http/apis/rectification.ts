import { get, post } from '..';
import { URLRequestPrefix } from '../config';

// 获取整改跟踪AI列表
export const getRectificationList = async (data: any) =>
  await post(`${URLRequestPrefix.OM}/ai/patrol/report/pc-list`, data);

// AI巡检跟踪详情
export const getRectificationDetailByAI = async (id: any) =>
  await get(`${URLRequestPrefix.OM}/ai/patrol/report/detail?id=${id}`);

// AI驳回
export const aiRectifyReject = async (data: any) =>
  await post(`${URLRequestPrefix.OM}/ai/patrol/report/reject-rectification`, data);

// AI整改通过
export const aiConfirm = async (data: { reportId: number }) =>
  await post(`${URLRequestPrefix.OM}/ai/patrol/report/confirm`, data);

// AI识别错误督导驳回
export const aiRecognitionReject = async (data: any) =>
  await post(`${URLRequestPrefix.OM}/ai/patrol/report/reject-anomaly-identification`, data);
// AI整改下一份
export const queryPatrolAiNext = async (data: any) => await post(`${URLRequestPrefix.OM}/ai/patrol/report/next`, data);

export const getRectificationDetailByRoutine = async (data: { reportId: number }) =>
  await post(`${URLRequestPrefix.OM}/patrol/issues/detail`, data);

// 食安稽核整改任务-学习情况
export const getPatrolUserStudyInfo = async (id: number) =>
  await get(`${URLRequestPrefix.OM}/corp/patrol/issue/user-study-info`, { params: { id } });

// 常规巡检驳回、审核通过。两个操作同一个接口      传参：（ 驳回status：1 通过status:2）
export const rejectByRoutine = async (data: any) =>
  await post(`${URLRequestPrefix.OM}/patrol/issues/confirmIssuesItem`, data);

// 常规巡检整改下一份
export const queryRoutineNext = async (data: any) =>
  await post(`${URLRequestPrefix.OM}/patrol/issues/items/next`, data);

export const getRectificationDetailBySelf = async (data: number) =>
  await get(`${URLRequestPrefix.OM}/common/selfReport/issue/${data}/detail`);

// 自检下一份
export const querySelfNext = async (data: any) =>
  await post(`${URLRequestPrefix.OM}/common/selfReport/issue/next`, data);

// 自检驳回
export const rejectBySelf = async (data: any) =>
  await post(`${URLRequestPrefix.OM}/common/patrol/self/reject-rectification`, data);

// 自检审核通过
export const confirmBySelf = async (data: any) => await post(`${URLRequestPrefix.OM}/common/patrol/self/confirm`, data);

export const unckeckByRoutine = async (itemId: number | undefined) =>
  await get(`${URLRequestPrefix.OM}/patrol/issues/check/item?id=${itemId}`);

// 获取消杀整改跟踪列表
// export type GetDisinfectionRectificationListParams={
//     // accentedTermTag?:
// }
// export const getDisinfectionRectificationList=async (data:any)=>await post(`${URLRequestPrefix.OM}/disinfection/issue/pc-page`,data);

export const getDisinfectDetail = async (reportId: number) =>
  await get(`${URLRequestPrefix.OM}/common/disinfection/issue/detail?id=${reportId}`);

// 结构消杀整改下一份
export const queryDisinfectNext = async (data: any) =>
  await post(`${URLRequestPrefix.OM}/common/disinfection/issue/next`, data);

// 消杀审核通过
export const confirmByDisinfect = async (data: any) =>
  await post(`${URLRequestPrefix.OM}/common/disinfection/issue/passRectification`, data);

// 消杀驳回
export const rejectByDisinfect = async (data: any) =>
  await post(`${URLRequestPrefix.OM}/common/disinfection/issue/rejectRectification`, data);
export type GetDisinfectionRectificationListParams = {
  // accentedTermTag?:
};
export const getDisinfectionRectificationList = async (data: GetDisinfectionRectificationListParams) =>
  await post(`${URLRequestPrefix.OM}/common/disinfection/issue/pc-page`, data);

// 获取报告纬度整改问题列表
export const getDisinfectionIssueListByReport = async (taskId: number) =>
  await get(`${URLRequestPrefix.OM}/common/disinfection/issue/listByReport`, { params: { taskId } });

// 导出消杀整改跟踪列表

export const exportDisinfectionRectificationList = async (data: GetDisinfectionRectificationListParams) =>
  await post(`${URLRequestPrefix.OM}/common/disinfection/issue/export`, data);

// 巡检整改学习明细列表
export type GetUserStudyDetailListReq = {
  groupId?: number;
  shopIdList: string[];
  studyProjectId?: string;
  planId?: number;
  reportSubmitStartTime: string;
  reportSubmitEndTime: string;
  worksheetId?: number;
  studyStatus?: string;
};
export const getUserStudyDetailList = async (data: GetUserStudyDetailListReq) =>
  await post(`${URLRequestPrefix.OM}/corp/patrol/issue/study/user-study-detail-list`, data);

// 导出巡检整改学习明细列表
export const exportUserStudyDetailList = async (data: GetUserStudyDetailListReq) =>
  await post(`${URLRequestPrefix.OM}/corp/patrol/issue/study/user-study-detail-list-export`, data);

// 重新发起整个学习
export const resetUserStudy = async (id: number) =>
  await post(`${URLRequestPrefix.OM}/patrol/issue/report/user-study-reset`, { id });

export const getUserStudyDetailInfo = async (id: number) =>
  await get(`${URLRequestPrefix.OM}/corp/patrol/issue/study/user-study-detail-info`, { params: { id } });

export const getCycleJobCount = async (taskId: string) =>
  await get(`${URLRequestPrefix.OM}/common/patrol/issue/cycle-job-count?id=${taskId}`);

export const getCycleJobList = async (data: { pageNo: number; pageSize: number; reportId: number }) =>
  await post(`${URLRequestPrefix.OM}/patrol/issues/items/submitted/cycleItemList`, data);

export const getCycleJobDetail = async (cycleJobId: number) =>
  await get(`${URLRequestPrefix.OM}/patrol/issues/items/submitted/cycleItemDetail/${cycleJobId}`);

export const suspendCycleJob = async (data: { id: number; reason: string }) =>
  await post(`${URLRequestPrefix.OM}/corp/patrol/issue/cycle-job/suspend`, data);
