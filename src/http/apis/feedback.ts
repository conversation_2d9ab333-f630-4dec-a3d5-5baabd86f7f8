import { get, post } from '..';
import { URLRequestPrefix } from '../config';

// 获取问题反馈列表
export const getFeedbackList = async (data: any) => await post(`${URLRequestPrefix.OM}/user/feedback/list`, data);

// 获取问题反馈详情
export const getFeedbackInfo = async (id: number) => await post(`${URLRequestPrefix.OM}/user/feedback/detail/${id}`);

// 获取问题反馈模块
export const getFeedbackModule = async () =>
  await get(`${URLRequestPrefix.OM}/common/attribute/dict/findByGroup?group=feedback_module`);

export const exportFeedback = async (data: any) => await post(`${URLRequestPrefix.OM}/user/feedback/export`, data);
