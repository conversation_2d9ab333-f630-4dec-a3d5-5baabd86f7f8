import { get, post } from '..';
import { URLRequestPrefix } from '../config';
import { InterviewStatus, JobType } from '@/constants/interview';

// 候选人信息看板列表
export type GetShopRecruitCandidateListParams = {
  province?: string;
  city?: string;
  district?: string;
  shopId?: string;
  shopName?: string;
  shopOwnerName?: string;
  jobType?: JobType;
  candidateName?: string;
  candidatePhone?: string;
  status?: InterviewStatus;
  activted?: boolean;
  fullTime?: boolean;
  pageNo: number;
  pageSize: number;
};
export const getShopRecruitCandidateList = async (data: GetShopRecruitCandidateListParams) =>
  await post(`${URLRequestPrefix.RM}/shop-recruit-candidate`, data);

// 导出候选人信息看板列表

export const exportShopRecruitCandidateList = async (data: GetShopRecruitCandidateListParams) =>
  await post(`${URLRequestPrefix.RM}/shop-recruit-candidate/export`, data);

// 岗位打招呼设置列表
export type GetAccountJobListParams = {
  accountId?: string;
  accountName?: string;
  jobName?: string;
  locationName?: string;
  enable?: boolean;
  pageSize: number;
  pageNo: number;
};
export const getAccountJobList = async (data: GetAccountJobListParams) =>
  await post(`${URLRequestPrefix.RM}/account/job/list`, data);

// 配置每日打招呼数
export const updateGreetCount = async (data) =>
  await post(`${URLRequestPrefix.RM}/account/job/update/greekCount`, data);

// 配置今日剩余次数
export const updateRemainCount = async (data) =>
  await post(`${URLRequestPrefix.RM}/account/job/update/remindCount`, data);

// 配置岗位类型
export const updateJobType = async (data) => await post(`${URLRequestPrefix.RM}/account/job/update/jobType`, data);

// 配置筛选条件
export const updateScreeningCondition = async (data) =>
  await post(`${URLRequestPrefix.RM}/account/job/update/condition`, data);

// 配置筛选条件
export const updateEnable = async (data) => await post(`${URLRequestPrefix.RM}/account/job/update/enable`, data);

// 配置紧急需求岗位
export const updateUrgentJob = async (data) => await post(`${URLRequestPrefix.RM}/account/job/update/urgentJob`, data);

// 查询设备列表
export type GetDeviceListParams = {
  deviceId?: string;
  operatorName?: string;
  pageNo: number;
  pageSize: number;
};
export const getDeviceList = async (data: GetDeviceListParams) =>
  await post(`${URLRequestPrefix.RM}/device/list`, data);

export type EditDeviceParams = {
  deviceId: string;
  operator: string;
};

// 新增设备
export const createDevice = async (data: EditDeviceParams) => await post(`${URLRequestPrefix.RM}/device/add`, data);

// 编辑设备
export const updateDevice = async (data: EditDeviceParams) => await post(`${URLRequestPrefix.RM}/device/edit`, data);

// 删除设备
export const deleteDevice = async (id: number) => await post(`${URLRequestPrefix.RM}/device/delete/${id}`);

// 获取招聘角色用户列表
export const getUsersOfRecruit = async () => await get(`${URLRequestPrefix.RM}/common/listByRoleIds`);

// 获取BOSS账号列表
export type GetBossAccountListParams = {
  accountId?: number;
  accountName?: string;
  status?: string;
  pageNo: number;
  pageSize: number;
};

export const getBossAccountList = async (data: GetBossAccountListParams) =>
  await post(`${URLRequestPrefix.RM}/account/list`, data);

export interface BossAccountParams {
  /**
   * 账号id(boss直聘的accountId)
   */
  accountId: null | string;
  /**
   * 招聘人名称
   */
  baseName?: null | string;
  /**
   * 品牌id
   */
  brandId?: number | null;
  /**
   * 品牌名称
   */
  brandName?: null | string;
  /**
   * 招聘人id
   */
  comId?: number | null;
  /**
   * 邮箱
   */
  email?: null | string;
  /**
   * 性别
   */
  gender?: boolean | null;
  /**
   * 手机号
   */
  phone?: null | string;
  /**
   * 密码(加密的)
   */
  pwd: null | string;
  /**
   * @ApiModelProperty(value = "账号状态")
   * 权益个数
   */
  remindCount: number | null;
  /**
   * 权益个数(总额度)
   */
  totalCount?: number | null;
  /**
   * 权益个数(已用打招呼)
   */
  usedCount?: number | null;
  /**
   * 微信账号
   */
  wx?: null | string;
  [property: string]: any;
}
export const createBossAccount = async (data: BossAccountParams) =>
  await post(`${URLRequestPrefix.RM}/account/add`, data);

export const updateBossAccount = async (data: BossAccountParams) =>
  await post(`${URLRequestPrefix.RM}/account/edit`, data);

// 获取约面跟踪列表
export interface GetAppointListReq {
  city?: null | string;
  district?: null | string;
  endDate: null | string;
  groupId?: number | null;
  pageNo: number | null;
  pageSize: number | null;
  province?: null | string;
  shopId?: null | string;
  shopName?: null | string;
  shopOwnerName?: null | string;
  startDate: null | string;
  [property: string]: any;
}

export const getAppointList = async (data: GetAppointListReq) =>
  await post(`${URLRequestPrefix.RM}/shop-recruit/interview/static`, data);

export interface ApifoxModel {
  city?: null | string;
  district?: null | string;
  endDate: null | string;
  groupId?: number | null;
  pageNo: number | null;
  pageSize: number | null;
  province?: null | string;
  shopId?: null | string;
  shopName?: null | string;
  shopOwnerName?: null | string;
  startDate: null | string;
  [property: string]: any;
}

export const getFeedbackList = async (data: GetAppointListReq) =>
  await post(`${URLRequestPrefix.RM}/shop-recruit/interview-feedback/static`, data);

export interface getAccountRunningListDto {
  pageSize: number;
  pageNum: number;
  startDate: number;
  endDate: number;
  accountId?: number;
  accountName?: number;
}
export const getAccountRunningList = async (data: GetAppointListReq) =>
  await post(`${URLRequestPrefix.RM}/corp/data/getAccountCountDataList`, data);

// 导出
export const exportAccountCountData = async (data: GetAppointListReq) =>
  await post(`${URLRequestPrefix.RM}/corp/data/accountCountData/export`, data);

// 更新
export const refreshAccountCountData = async () =>
  await get(`${URLRequestPrefix.RM}/corp/data/accountCountData/refresh`);

export const getRecruitStatisticList = async (data: GetAppointListReq) =>
  await post(`${URLRequestPrefix.RM}/getRecruitAccountInfo`, data);

export const getRecruitStatisticInterviewList = async (data: GetAppointListReq) =>
  await post(`${URLRequestPrefix.RM}/getEntryDetail`, data);

export const getRecruitStatisticDemandList = async (data: GetAppointListReq) =>
  await post(`${URLRequestPrefix.RM}/getShopRecruitRequireDetail`, data);

export const getBossMaintainerList = async (data: any) => await post(`${URLRequestPrefix.RM}/getList`, data);

export const saveMaintainer = async (data: any) =>
  await post(`${URLRequestPrefix.RM}/addOrUpdateAccountManageInfo`, data);

export const deleteMaintainer = async (data: any) =>
  await post(`${URLRequestPrefix.RM}/batchDelAccountManageInfo`, data);

/**
 * @description 更新面试人信息
 * @param data
 * @returns
 */
export function updateInterviewInfo(data) {
  return post(`${URLRequestPrefix.RM}/shop-recruit-candidate/update`, data);
}

/**
 * @description 删除面试人信息
 * @param data
 * @returns
 */
export function deleteInterviewInfo(data) {
  return post(`${URLRequestPrefix.RM}/shop-recruit-candidate/delete`, data);
}
/**
 * @description 城市-岗位-需求
 * @param data
 * @returns
 */
export function getChengShiGangWeiXuQiuList(data) {
  return post(`${URLRequestPrefix.RM}/accountCityJobCount/getPage`, data);
}
/**
 * @description 城市-岗位-需求-导出
 * @param data
 * @returns
 */
export function exportChengShiGangWeiXuQiuList(data) {
  return post(`${URLRequestPrefix.RM}/accountCityJobCount/export`, data);
}
/**
 * @description 城市-岗位-需求-更新 & 城市-需求-岗位-更新
 * @param data
 * @returns
 */
export function refreshChengShiGangWeiXuQiuList() {
  return get(`${URLRequestPrefix.RM}/accountCityJobCount/updateData`);
}
/**
 * @description 城市-需求-岗位
 * @param data
 * @returns
 */
export function getChengShiXuQiuGangWeiList(data) {
  return post(`${URLRequestPrefix.RM}/cityRequireNumCount/getPage`, data);
}
/**
 * @description 城市-需求-岗位-导出
 * @param data
 * @returns
 */
export function exportChengShiXuQiuGangWeiList(data) {
  return post(`${URLRequestPrefix.RM}/cityRequireNumCount/export`, data);
}

/**
 * @description 城市-岗位-需求
 * @param data
 * @returns
 */
export function getZhangHaoChengShiGangWeiList(data) {
  return post(`${URLRequestPrefix.RM}/accountLoginStatusData/getPage`, data);
}
/**
 * @description 城市-岗位-需求-导出
 * @param data
 * @returns
 */
export function exportZhangHaoChengShiGangWeiList(data) {
  return post(`${URLRequestPrefix.RM}/accountLoginStatusData/export`, data);
}
/**
 * @description 城市-岗位-需求-更新
 * @param data
 * @returns
 */
export function refreshZhangHaoChengShiGangWeiList() {
  return get(`${URLRequestPrefix.RM}/accountLoginStatusData/updateData`);
}
