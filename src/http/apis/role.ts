import { get } from '..';
import { URLRequestPrefix } from '../config';

// 按分类获取角色列表
export const getRoleClassifyList = async (roleCategory: number) =>
  await get(`${URLRequestPrefix.OM}/corp/role/classify-list`, { params: { roleCategory } });

// 多个分类获取角色列表,如roleCategory=1,5
export const getMultiRoleClassifyList = async (roleCategorys: string) =>
  await get(`${URLRequestPrefix.OM}/corp/role/classify-by-list`, { params: { roleCategorys } });
