import { get, post } from '..';
import { URLRequestPrefix } from '../config';

// 获取门店定位地址不准确的总数量
export const getShopAddressErrorCount = async () => await post(`${URLRequestPrefix.OM}/address/error/shop/count`);

// 获取门店定位地址不准确的总数量
export const getShopAddressErrorList = async (data: any) =>
  await post(`${URLRequestPrefix.OM}/query/address/error/shopList`, data);

// 更新门店地址相关数据（支持批量）
export type updateShopAddressProps = {
  shopIds?: string[];
  batchUpdate: 'BATCH_APPROVED' | 'BATCH_IGNORE'; // BATCH_APPROVED通过  BATCH_IGNORE忽略
};
export const updateShopAddress = async (data: updateShopAddressProps) =>
  await post(`${URLRequestPrefix.OM}/address/batch/update`, data);

// 获取运营通组织树数据
export const getOMGroupTreeList = async () =>
  await get(`${URLRequestPrefix.OM}/corp/organization/tree/by-user-permission`);

// 获取运营通组织树全部数据（无权限过滤）
export async function getOMAllTreeList() {
  return get(`${URLRequestPrefix.OM}/corp/organization/tree`);
}

// 获取运营通组织树数据

export const getOMStoreList = async (organizationId?: number) =>
  await get(`${URLRequestPrefix.OM}/corp/organization/tree/shop-list/by-user-permission`, {
    params: { organizationId },
  });

export const getShopLicenseAuditRecords = async (data: any) =>
  await get(`${URLRequestPrefix.OM}/corp/shop-license/audit/records`, {
    params: data,
  });
export const cancelShopLicense = async (data: number) =>
  await post(`${URLRequestPrefix.OM}/corp/shop-license/cancel`, { data });
