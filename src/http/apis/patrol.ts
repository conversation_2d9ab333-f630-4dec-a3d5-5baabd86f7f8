import { get, post, put } from '..';
import { URLRequestPrefix } from '../config';

//获取巡检计划列表
export const getPatrolPlanList = async (data: any) => await post(`${URLRequestPrefix.OM}/corp/patrol/plan/list`, data);

/** 获取报告点评 */
export const getPatrolReportReviewDetail = async (reportId: number) =>
  await get(`${URLRequestPrefix.OM}/common/patrol/report/${reportId}/review/detail`);

/** 报告点评提交 */
export const submitPatrolReportReview = async (taskReportId: number) =>
  await post(`${URLRequestPrefix.OM}/common/patrol/report/${taskReportId}/review/submit`);

/** 报告点评保存 */
export const savePatrolReportReview = async (data: any) =>
  await post(`${URLRequestPrefix.OM}/common/patrol/report/review/save`, data);

/** 巡检报告点评项 */
export const savePatrolReviewItem = async (data: {
  content: string;
  itemId: number;
  qualified: boolean;
  taskId: number;
}) => await post(`${URLRequestPrefix.OM}/corp/patrol/report/review/item`, data);

/** 巡检报告第二点评项 */
export const savePatrolSecondReviewItem = async (data: {
  secondContent: string;
  itemId: number;
  secondQualified: boolean;
  taskId: number;
}) =>
  await post(
    `${URLRequestPrefix.OM}/corp/patrol/report/second/review/item
`,
    data,
  );

/** 巡检报告点评提交 */
export const patrolReportReviewSubmit = async (data: { overallReviewContent: string; taskId: number }) =>
  await put(`${URLRequestPrefix.OM}/corp/patrol/report/review/submit`, data);

/** 巡检报告第二点评提交 */
export const patrolReportSecondReviewSubmit = async (data: { secondOverallReviewContent: string; taskId: number }) =>
  await post(`${URLRequestPrefix.OM}/corp/patrol/report/second/review/submit`, data);
