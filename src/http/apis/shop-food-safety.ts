import { get, post } from '..';
import { URLRequestPrefix } from '../config';

// 绑定诊断任务检查表
export type BindAuditChecklistConfigParams = {
  params?: {
    worksheetId: number;
    worksheetItemId: number;
    worksheetType: string;
    bindWorksheetItemIds: number[];
  };
};
export const bindAuditChecklistConfig = async (data: BindAuditChecklistConfigParams) =>
  await post(`${URLRequestPrefix.OM}/corp/food-safety/mapping/bind`, data);

export const getAuditChecklistConfig = async () => await get(`${URLRequestPrefix.OM}/corp/food-safety/mapping/list`);
