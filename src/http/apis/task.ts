import { useRequest } from 'ahooks';
import { Result } from 'ahooks/lib/useRequest/src/types';
import dayjs from 'dayjs';
import { secondUpdateItemParams } from './checklist';
import { get, post, put } from '..';
import { URLRequestPrefix } from '../config';
import { ERreformStatus, Report_STATUS_TEXT } from '@/pages/report/enum';
import { SheetStatusEnum } from '@/pages/task/disinfection/schedulingDetail';
import { SelfReportDetail } from '@/services/selfCheck';
import { ReportDetailInfo } from '@/services/supervisor';

// 创建自检单次任务
export const createSelfTask = async (data: any) => await post(`${URLRequestPrefix.OM}/corp/self-plans/once`, data);

// 更新自检单次任务
export const updateSelfTask = async (data: any) =>
  await post(`${URLRequestPrefix.OM}/corp/self-plans/once/update`, data);

// 更新自检进行中单次任务
export const updateSelfTaskProgress = async (data: any) =>
  await post(`${URLRequestPrefix.OM}/corp/self-plans/once/update/extension`, data);

// 创建自检循环任务
export const createSelfPlan = async (data: any) => await post(`${URLRequestPrefix.OM}/corp/self-plans/cyclical`, data);

// 创建自检循环任务
export const updateSelfPlan = async (data: any) =>
  await post(`${URLRequestPrefix.OM}/corp/self-plans/cyclical/update`, data);

export const saveCirculation = async (data: any) =>
  await post(`${URLRequestPrefix.OM}/corp/routine/patrol/plan/saveCirculation`, data);

export const updateCirculation = async (data: any) =>
  await post(`${URLRequestPrefix.OM}/corp/routine/patrol/plan/updateCirculation`, data);

// 获取自检任务分页列表数据
export const getSelfTaskListByPage = async (data: any) =>
  await post(`${URLRequestPrefix.OM}/corp/self-plans/queryPlanTaskListByPage`, data);

export const getNormalTaskList = async (data: any) =>
  await post(`${URLRequestPrefix.OM}/corp/routine/patrol/plan/list`, data);

// 取消自检任务
export const cancelSelfTask = async (taskId: any) =>
  await post(`${URLRequestPrefix.OM}/corp/self-plans/cyclical/cancel`, { data: taskId });

// 暂停循环自检任务
export const pauseLoopSelfTask = async (taskId: any) =>
  await post(`${URLRequestPrefix.OM}/corp/self-plans/cyclical/disable`, { data: taskId });

// 启用循环自检任务
export const enableLoopSelfTask = async (taskId: any) =>
  await post(`${URLRequestPrefix.OM}/corp/self-plans/cyclical/enable`, { data: taskId });

export const cancelNormalTask = async (taskId: any) =>
  await post(`${URLRequestPrefix.OM}/corp/routine/patrol/plan/cancel`, { id: taskId });

// 删除自检任务
export const deleteSelfTask = async (taskId: any) =>
  await post(`${URLRequestPrefix.OM}/corp/self-plans/delete`, { data: taskId });

// 获取自检任务详情
export const getSelfTaskDetail = async (planTaskId: any) =>
  await get(`${URLRequestPrefix.OM}/corp/self-plans/queryPlanTaskDetail/${planTaskId}`);

// 获取自检报告列表
export const getSelfReportList = async (data: any) =>
  await post(`${URLRequestPrefix.OM}/corp/self-task/selfReport/list`, data);

export const getSelfReportDetailButtonEnums = (detail: SelfReportDetail, isSuper: boolean) => {
  const {
    status,
    unreviewed,
    autoReview,
    hasSecondReview,
    secondReview,
    reformStatus,
    latestReviewTime,
    issuesCount,
    noReformCount,
    hasRectificationOperation,
    reviewTimeExpired,
  } = detail;

  let buttonEnums: string[] = [];

  /** 是否展示撤回点评按钮 */
  const showRevokeReportComment = (() => {
    if (
      status === Report_STATUS_TEXT.已点评 &&
      reformStatus !== ERreformStatus.已整改 &&
      dayjs()?.diff(dayjs(latestReviewTime), 'hour') < 3
    ) {
      // 没有问题项
      if (!issuesCount) {
        return true;
      } else {
        // 有问题项，门店还未操作时
        if (!hasRectificationOperation) {
          return true;
        } else if (!noReformCount) {
          // 有问题项，已全部整改完毕
          return false;
        } else {
          // 有问题项，门店已有操作
          return !!isSuper;
        }
      }
    }
  })();

  if ((status === Report_STATUS_TEXT.已提交 && unreviewed) || (hasSecondReview && !secondReview)) {
    if (!hasSecondReview && reviewTimeExpired) {
      buttonEnums = ['SUBMIT_REPORT_COMMENT_DISABLE'];
    } else {
      buttonEnums = ['SUBMIT_REPORT_COMMENT'];
    }
  }

  if (!autoReview && showRevokeReportComment && !hasSecondReview) {
    // 自动提交的报告没有撤回按钮and第二点评人没有撤回按钮
    if (reviewTimeExpired) {
      buttonEnums = ['REVOKE_REPORT_COMMENT_DISABLE'];
    } else {
      // 自动提交的报告没有撤回按钮and第二点评人没有撤回按钮
      buttonEnums = ['REVOKE_REPORT_COMMENT'];
    }
  }

  return buttonEnums;
};

// 获取自检报告详情
export const querySelfReportDetail = async (data: any) => {
  const detail = await get<SelfReportDetail>(
    `${URLRequestPrefix.OM}/common/self-task/detail?taskId=${data?.taskId}${data?.notFilledItemHandleType ? `&notFilledItemHandleType=${data?.notFilledItemHandleType}` : ''}`,
    data,
  );

  return {
    ...detail,
    // buttonEnums: getSelfReportDetailButtonEnums(detail!)
  };
};
// 获取自检检报告操作日志

export const getSelfReportLogs = async (data: any) => {
  return await get(`${URLRequestPrefix.OM}/common/self/task/report/${data?.taskId}/logs`, {});
};
// 提交点评
export const submitSelfComment = (data: { taskId: number; notFilledItemHandleType: string }) => {
  return post(`${URLRequestPrefix.OM}/corp/self-task/review-submit`, data);
};
// 撤回点评
export const revoleSelfReport = (data: { data: number }) => {
  return post(`${URLRequestPrefix.OM}/corp/self-task/revocation-review`, data);
};
// 撤回单个检查表点评
export const revoleSingleSheet = (data: { taskId: number; worksheetId: number }) => {
  return post(`${URLRequestPrefix.OM}/corp/self-task/worksheet/revocation-review`, data);
};
// 自检点评总结
export const commentReport = async (data: any) =>
  await post(`${URLRequestPrefix.OM}/corp/self-task/corp-summary`, data);
// 单个自检检查表点评总结
export const commentSingleSheet = (data: { taskId: number; worksheetId: number; summary: string }) => {
  return post(`${URLRequestPrefix.OM}/corp/self-task/worksheet/corp-summary`, data);
};
// 第二点评人项
export const secondUpdateCheckItem = (data: secondUpdateItemParams) => {
  return post(`${URLRequestPrefix.OM}/corp/self-task/second/item/save`, data);
};
// 第二点评人提交点评
export const secondSubmitComment = (data: { taskId: number; notFilledItemHandleType?: string }) => {
  return post(`${URLRequestPrefix.OM}/corp/self-task/second/report-review`, data);
};

// 第二点评人点评总结
export const secondCommentReport = (data: { taskId: number; summary: string }) => {
  return post(`${URLRequestPrefix.OM}/corp/self-task/second/report-summary`, data);
};

// 获取自检报告整改分页
export const getSelfReportRectifyList = async (data: any) =>
  await post(`${URLRequestPrefix.OM}/common/selfReport/issue/web/page`, data);

// 获取巡检报告列表
export const getRoutineReportList = async (data: any) =>
  await post(`${URLRequestPrefix.OM}/corp/patrol/report/list`, data);

// 获取巡检报告详情列表
export const getRoutineReportDetailList = async (data: any) =>
  await post(`${URLRequestPrefix.OM}/patrol/issues/issues/detail`, data);

// 导出巡检报告列表
export const exportRoutineReportList = async (data: any) =>
  await post(`${URLRequestPrefix.OM}/corp/patrol/report/list/export`, data);

// 获取巡检报告详情
export const queryPatrolReportDetail = async (data: any) =>
  await post<ReportDetailInfo>(`${URLRequestPrefix.OM}/common/patrol/report/detail`, data);

// 获取巡检报告整改分页
export const getRoutineReportRectifyList = async (data: any) =>
  await post(`${URLRequestPrefix.OM}/patrol/issues/list`, data);

// 获取巡检报告问题跟踪列表
export const getRoutineReportTrackList = async (reportIds: any) =>
  await get(`${URLRequestPrefix.OM}/patrol/issues/items/submitted`, { params: { reportIds } });

// 导出巡检报告整改分页
export const exportRoutineReportRectifyList = async (data: any) =>
  await post(`${URLRequestPrefix.OM}/patrol/issues/items/export`, data);

// 获取巡检人完成情况列表
export const getInspectorCompletionList = async (data: any) =>
  await post(`${URLRequestPrefix.OM}/supervision/list`, data);

// 导出巡检人完成情况列表
export const exportInspectorCompletionList = async (data: any) =>
  await post(`${URLRequestPrefix.OM}/supervision/list/export`, data);

// 获取自检门店完成情况
export const getSelfCompletionList = async (data: any) =>
  await post(`${URLRequestPrefix.OM}/statistic/shop/self/list`, data);

// 获取策略-门店自检实时情况
export async function getTacticsSelfCompletionList(data: any) {
  return await post(`${URLRequestPrefix.TM}/statistic/shop/self/list`, data);
}

// 导出策略-门店自检实时情况列表
export const exportTacticsSelfCompletionList = async (data: any) =>
  await post(`${URLRequestPrefix.TM}/statistic/shop/self/list/export`, data);

// 查询门店得分分布
export const queryShopScore = async (data: any) => post(`${URLRequestPrefix.OM}/rank/shop/score/count`, data);

// 查询报告得分走势
export const queryReportScore = async (data: any) => post(`${URLRequestPrefix.OM}/statistic/shop/patrol/detail`, data);
// 获取组织的趋势分析
export const queryPatrolDataChart = async (data: any) =>
  post(`${URLRequestPrefix.OM}/statistic/shop/app/queryPatrolDataChart`, data);

// 获取巡检门店完成情况
export const getRoutineCompletionList = async (data: any) =>
  await post(`${URLRequestPrefix.OM}/statistic/shop/patrol/list`, data);

// 获取巡检门店完成情况统计
export const getRoutineCompletionSummary = async (data: any) =>
  await post(`${URLRequestPrefix.OM}/statistic/shop/patrol/summary`, data);

// 获取自检不合格情况列表
export const getSelfDisqualificationList = async (data: any) =>
  await post(`${URLRequestPrefix.OM}/selfReport/issues/list`, data);

// 获取巡检不合格情况列表
export const getRoutineDisqualificationList = async (data: any) =>
  await post(`${URLRequestPrefix.OM}/v2/statistic/issue-report-list`, data);

// 导出巡检不合格情况列表
export const exportRoutineDisqualificationList = async (data: any) =>
  await post(`${URLRequestPrefix.OM}/v2/statistic/issue-report-list/export`, data);

// 导出策略-巡检不合格情况列表
export function exportTacticsRoutineDisqualificationList(data: any) {
  return post(`${URLRequestPrefix.TM}/v2/statistic/issue-report-list/export`, data);
}

// 获取策略-巡检不合格情况列表
export function getTacticsRoutineDisqualificationList(data) {
  return post(`${URLRequestPrefix.TM}/v2/statistic/issue-report-list`, data);
}

// 导出自检不合格情况列表
export const exporSelfDisqualificationList = async (data: any) =>
  await post(`${URLRequestPrefix.OM}/selfReport/issues/list/export`, data);

// 创建快捷巡检计划
export const createFastPatrolPlan = async (data: any) =>
  await post(`${URLRequestPrefix.OM}/corp/patrol/plan/save-quick`, data);

// 创建快捷巡检计划
export const createBatchPatrolPlan = async (data: any) =>
  await post(`${URLRequestPrefix.OM}/corp/patrol/plan/save-batch`, data);

export const createOnceNormalTask = async (data: any) =>
  await post(`${URLRequestPrefix.OM}/corp/routine/patrol/plan/saveOnce`, data);

export const updataOnceNormalTask = async (data: any) =>
  await post(`${URLRequestPrefix.OM}/corp/routine/patrol/plan/updateOnce`, data);

export const getNormalTask = async (planId: any) =>
  await get(`${URLRequestPrefix.OM}/corp/routine/patrol/plan/info`, { params: { planId } });

// 获取巡检任务计划列表

export const getPatrolPlanList = async (data: any) => await post(`${URLRequestPrefix.OM}/corp/patrol/plan/list`, data);

// 获取APP巡检任务计划列表

export const getAppPatrolPlanList = async (data: any) =>
  await post(`${URLRequestPrefix.OM}/corp/app/patrol/plan/list`, data);

// 查询自检任务名称是否已存在
export const checkNameExist = async (planName: string, planId?: any) =>
  await get(`${URLRequestPrefix.OM}/corp/self-plans/exist`, { params: { planName, planId } });

export const checkNormalName = async (planName: string, planId?: any) =>
  await get(`${URLRequestPrefix.OM}/common/routine/patrol/exist`, { params: { planName, planId } });

// 查询自检任务名称是否已存在
export const getMandatoryInspectionStore = async (data: any) =>
  await post(`${URLRequestPrefix.OM}/corp/patrol/plan/shop-report_info`, data);

// 取消巡检任务计划
export const cancelPatrolPlan = async (planId: number) =>
  await post(`${URLRequestPrefix.OM}/corp/patrol/plan/cancel`, { id: planId });

// 取消巡检任务计划
export const deletePatrolPlan = async (planId: number) =>
  await post(`${URLRequestPrefix.OM}/corp/patrol/plan/delete`, { id: planId });

// 获取批量巡检计划详情
export const getBatchPatrolPlan = async (planId: any) =>
  await get(`${URLRequestPrefix.OM}/corp/patrol/plan/batch-info`, { params: { planId } });

// 获取快捷巡检计划详情
export const getFastPatrolPlan = async (planId: any) =>
  await get(`${URLRequestPrefix.OM}/corp/patrol/plan/quick-info`, { params: { planId } });

// 添加巡检任务
export const addPatrolTask = async (data: any) => await post(`${URLRequestPrefix.OM}/corp/patrol/task/add`, data);

// 提交巡检报告
export const submitPatrolReport = async (data: any) =>
  await post(`${URLRequestPrefix.OM}/corp/patrol/report/submit`, data);

// 提交巡检报告
export const submitPatrolReportSummary = async (data: any) =>
  await post(`${URLRequestPrefix.OM}/corp/patrol/report/summary`, data);

// 撤回报告
export const revokePatrolReport = async (data: any) =>
  await post(`${URLRequestPrefix.OM}/corp/patrol/report/revocation`, data);
// 查询巡检计划统计信息
export const getPatrolPlanStatistics = async (planId: any) =>
  await get(`${URLRequestPrefix.OM}/corp/patrol/plan/statistics`, { params: { planId } });

// 巡检任务详细排班
export const getPatrolTaskList = async (data: any) =>
  await post(`${URLRequestPrefix.OM}/corp/patrol/task/schedule/list`, data);

// 巡检任务详细排班
export const createPatrolTask = async (data: any) => await post(`${URLRequestPrefix.OM}/corp/patrol/task/add`, data);

// 取消巡检任务
export const cancelPatrolTask = async (taskId: any) =>
  await post(`${URLRequestPrefix.OM}/corp/patrol/task/cancel`, { taskId });

// 删除巡检任务
export const deletePatrolTask = async (taskId: any) =>
  await post(`${URLRequestPrefix.OM}/corp/patrol/task/delete`, { taskId });

// 查询巡检计划信息
export const getPatrolPlanInfo = async (planId: any) =>
  await get(`${URLRequestPrefix.OM}/corp/app/patrol/plan/statistic-info`, { params: { planId } });

// 巡检任务门店列表-提前排班
export const getPatrolTaskShopList = async (data: any) =>
  await post(`${URLRequestPrefix.OM}/corp/patrol/task/shop/list`, data);

// 根据planId添加巡检任务
export const addTaskByPlan = async (data: any) =>
  await post(`${URLRequestPrefix.OM}/corp/patrol/task/add-by-plan`, data);

// 获取巡检不合格原因列表
export const getDisqualificationReasonList = async (data: any) =>
  await post(`${URLRequestPrefix.OM}/v2/statistic/issue-report/unqualified-reason`, data);

// 导出巡检不合格原因列表
export const exportDisqualificationReasonList = async (data: any) =>
  await post(`${URLRequestPrefix.OM}/v2/statistic/issue-report/unqualified-reason/export`, data);

// 获取策略-巡检不合格原因列表
export const getTacticsDisqualificationReasonList = async (data: any) =>
  await post(`${URLRequestPrefix.TM}/v2/statistic/issue-report/unqualified-reason`, data);

// 导出策略-巡检不合格原因列表
export const exportTacticsDisqualificationReasonList = async (data: any) =>
  await post(`${URLRequestPrefix.TM}/v2/statistic/issue-report/unqualified-reason/export`, data);

// 获取巡检不合格门店列表
export const getDisqualificationShopList = async (data: any) =>
  await post(`${URLRequestPrefix.OM}/v2/statistic/issue-report/unqualified-shop-list`, data);

// 获取策略-巡检不合格门店列表
export const getTacticsDisqualificationShopList = async (data: any) =>
  await post(`${URLRequestPrefix.TM}/v2/statistic/issue-report/unqualified-shop-list`, data);

// 导出巡检不合格门店列表
export const exportDisqualificationShopList = async (data: any) =>
  await post(`${URLRequestPrefix.OM}/v2/statistic/issue-report/shop-list/export`, data);

// 导出策略-巡检不合格门店列表
export const exportTacticsDisqualificationShopList = async (data: any) =>
  await post(`${URLRequestPrefix.TM}/v2/statistic/issue-report/shop-list/export`, data);

// 获取自检不合格原因列表
export const getSelfDisqualificationReasonList = async (data: any) =>
  await post(`${URLRequestPrefix.OM}/selfReport/issues/nonconformityReason`, data);

// 导出自检不合格原因列表
export const exportSelfDisqualificationReasonList = async (data: any) =>
  await post(`${URLRequestPrefix.OM}/selfReport/issues/nonconformityReason/export`, data);

// 获取自检不合格门店列表
export const getSelfDisqualificationShopList = async (data: any) =>
  await post(`${URLRequestPrefix.OM}/selfReport/issues/shopList`, data);

// 导出自检不合格门店列表
export const exportSelfDisqualificationShopList = async (data: any) =>
  await post(`${URLRequestPrefix.OM}/selfReport/issues/shopList/export`, data);

// 名称查询巡检任务列表（查询筛选项）
export const getRoutineTaskListByName = async (word: string) =>
  await get(`${URLRequestPrefix.OM}/common/patrol/plan/name`, { params: { word } });

// 名称查询巡检任务列表（查询筛选项）
export const getRoutineTaskListName = async (word: string) =>
  await get(`${URLRequestPrefix.OM}/common/routine/patrol/plan/name`, { params: { word } });

// 获取自检门店完成情况汇总
export const getSelfCompletionSummary = async (data: any) =>
  await post(`${URLRequestPrefix.OM}/statistic/shop/self/summary`, data);

// 获取策略-自检门店完成情况汇总
export const getTacticsSelfCompletionSummary = async (data: any) =>
  await post(`${URLRequestPrefix.TM}/statistic/shop/self/summary`, data);

// 自检任务门店完成情况下载明细
export const exportSelfCompletionList = async (data: any) =>
  await post(`${URLRequestPrefix.OM}/statistic/shop/self/list/export`, data);

// 获取自检门店完成情况汇总
export const getSelfUnfinishedTaskList = async (data: any) =>
  await post(`${URLRequestPrefix.OM}/statistic/uncompletedTask/list`, data);
// 导出自检门店完成情况汇总
export const exportSelfUnfinishedTaskList = async (data: any) =>
  await post(`${URLRequestPrefix.OM}/statistic/uncompletedTask/list/export`, data);

// 获取策略-自检门店完成情况汇总
export function getTacticsSelfUnfinishedTaskList(data) {
  return post(`${URLRequestPrefix.TM}/statistic/uncompletedTask/list`, data);
}

// 导出策略-自检门店完成情况汇总
export function exportTacticsSelfUnfinishedTaskList(data) {
  return post(`${URLRequestPrefix.TM}/statistic/uncompletedTask/list/export`, data);
}

// 根据任务时间查询自检任务列表（列表筛选项）
export const getSelfTaskSimpleInfoList = async (data: any) =>
  await post(`${URLRequestPrefix.OM}/corp/self-task/selfTask/queryTaskSimpleInfo`, data);

// 巡检门店完成情况下载明细
export const exportRoutineCompletionList = async (data: any) =>
  await post(`${URLRequestPrefix.OM}/statistic/shop/patrol/export`, data);

// 常规巡检完成情况 导出到店辅导任务
export async function exportArriveShopCoachTask(data: any) {
  return await post(`${URLRequestPrefix.OM}/statistic/shop/patrol/arrive-shop/export`, data);
}

// 常规任务类型列表
export const commonTaskDetailPage = async (data: any) =>
  await post(`${URLRequestPrefix.OM}/common/manage/task/detailPage`, data);
// 导出
export const exportCommonTask = async (data: any) =>
  await post(`${URLRequestPrefix.OM}/common/manage/task/exportDetailTaskData`, data);
// 导出
export const asyncExportCommonTask = async (data: any) =>
  await post(`${URLRequestPrefix.OM}/common/manage/task/asyncExportDetailTaskData`, data);
/** 查询任务详情 */
export const getTaskDetail = async (taskUserId: string) =>
  get(`/om-api/common/manage/task/getTaskDetail/${taskUserId}`);

// 获取消杀任务列表(公司员工端)
export const getDisinfectionTaskListByCORP = async (data: any) =>
  await post(`${URLRequestPrefix.OM}/corp/disinfection/pc-page`, data);

// 获取消杀任务列表(消杀公司端)
export const getDisinfectionTaskListByCompany = async (data: any) =>
  await post(`${URLRequestPrefix.OM}/disinfection/task/pc-page`, data);

// 获取日常消杀任务列表(公司员工端)
export const getDisinfectionDailyTaskListByCORP = async (data: any) =>
  await post(`${URLRequestPrefix.OM}/corp/disinfection/pc-daily-page`, data);

// 获取日常消杀任务列表(消杀公司端)
export const getDisinfectionDailyTaskListByCompany = async (data: any) =>
  await post(`${URLRequestPrefix.OM}/disinfection/task/pc-daily-page`, data);

// 获取日常消杀计划操作日志
export const getLogs = async (planId: number) =>
  await get(`${URLRequestPrefix.OM}/common/disinfection/dailyPlan/log/getLogs/${planId}`);

// 获取日常消杀计划任务列表-督导
export const queryPlanTaskListByCORP = async (data: any) =>
  await post(`${URLRequestPrefix.OM}/corp/dailyPlanTask/queryPlanTaskListByPage`, data);

// 获取日常消杀计划任务列表-消杀
export const queryPlanTaskListByCompany = async (data: any) =>
  await post(`${URLRequestPrefix.OM}/disinfection/dailyPlanTask/queryPlanTaskListByPage`, data);

// 获取排班模版文件
export const getTemplateUrl = async () =>
  await post(`${URLRequestPrefix.OM}/common/disinfection/dailyPlanTask/getTemplateUrl`, undefined, {
    responseType: 'blob',
  });

// 驳回任务
export const rejectTask = async (data: { taskId: number; remark: string }) =>
  await post(`${URLRequestPrefix.OM}/corp/disinfection/dailyPlanTask/rejectTask`, data);

// 通过任务
export const passTask = async (taskId: number) =>
  await post(`${URLRequestPrefix.OM}/corp/disinfection/dailyPlanTask/${taskId}/passTask`);

// 校验excel
export const validTashShop = async (data: {
  taskId: number;
  files: {
    url: string;
    fileName: string;
  };
}) => await post(`${URLRequestPrefix.OM}/disinfection/dailyPlanTaskShop/validTashShop`, data);

// 提交排班
export const submitExcel = async (data: { taskId: number }) =>
  await post(`${URLRequestPrefix.OM}/disinfection/dailyPlanTaskShop/submitExcel`, data);

// 查询排班任务详情
export const getTaskShopListByTaskId = async (taskId: number) =>
  await get(`${URLRequestPrefix.OM}/common/dailyPlanTaskShop/getTaskShopListByTaskId`, { params: { taskId } });

// 删除任务排班下的详情
export const deleteTaskShopByIds = async (data: { taskId: number; taskShopIds: number[] }) =>
  await post(`${URLRequestPrefix.OM}/common/dailyPlanTaskShop/deleteTaskShopByIds`, data);

// 导出任务排班详情
export const exportDetail = async (data: { taskId: number }) =>
  await post(`${URLRequestPrefix.OM}/common/dailyPlanTaskShop/exportDetail`, data);

// 获取日常消杀排班任务操作记录
export const getProcessList = async (taskId: number) =>
  await get(`${URLRequestPrefix.OM}/common/disinfection/dailyPlan/taskShopProcess/getProcessList`, {
    params: { taskId },
  });

// 分配消杀任务
export type AssignDisinfectionTaskParams = {
  taskId: number;
  disinfectionCompanyId: number;
};
export const assignDisinfectionTask = async (data: AssignDisinfectionTaskParams) =>
  await put(`${URLRequestPrefix.OM}/corp/disinfection/assignTask`, data);

// 获取消杀任务详情
export const getDisinfectionTaskDetail = async (id: number) =>
  await get(`${URLRequestPrefix.OM}/common/disinfection/task/detail`, { params: { id } });

// 获取消杀任务进度列表
export type GetDisinfectionTaskProcessListParams = {
  taskId: number;
  operateAction?: string;
};
export const getDisinfectionTaskProcessList = async (data: GetDisinfectionTaskProcessListParams) =>
  await post(`${URLRequestPrefix.OM}/common/disinfection/processList`, data);

// 获取应急消杀任务详情
export const getEmergencyDetail = async (taskId: number) =>
  await get(`${URLRequestPrefix.OM}/common/disinfection/task/exigency-reported/${taskId}/detail`);

// 获取消杀任务分配消杀公司的简单信息列表
export type GetDisinfectionCompanySimpleListParams = {
  enable?: boolean; // 公司状态是否启用
  filterNoEmployee?: boolean; // 是否过滤没有员工的公司
};
export const getDisinfectionCompanySimpleList = async (params: GetDisinfectionCompanySimpleListParams) =>
  await get(`${URLRequestPrefix.OM}/common/disinfection/company/list/simple`, { params });

export const getDisinfectionCompanySimpleListNotLimit = async (params: GetDisinfectionCompanySimpleListParams) =>
  await get(`${URLRequestPrefix.OM}/common/disinfection/company/list/simpleNotLimit`, { params });

// 修改消杀任务到店时间
export type EditDisinfectionTaskParams = {
  taskId: number;
  arriveTime: string;
};

export const editDisinfectionTask = async (data: EditDisinfectionTaskParams) =>
  await put(`${URLRequestPrefix.OM}/disinfection/task/arrive-time`, data);

// 中止消杀任务
export type SuspendDisinfectionTaskParams = {
  taskId: number;
  reasonInfo: {
    reason: string;
    otherReason?: string;
  };
};
export const suspendDisinfectionTask = async (data: SuspendDisinfectionTaskParams) =>
  await put(`${URLRequestPrefix.OM}/disinfection/task/suspend`, data);

// 重启消杀任务
export const restartDisinfectionTask = async (data: EditDisinfectionTaskParams) =>
  await put(`${URLRequestPrefix.OM}/disinfection/task/restart`, data);

// 取消消杀任务
export const cancelDisinfectionTask = async (data: {
  taskId?: number;
  reasonInfo: {
    reason: string;
    remark?: string;
  };
}) => await post(`${URLRequestPrefix.OM}/corp/disinfection/cancel`, data);
// 重新分配消杀任务
export const reAssignTask = async (data: {
  taskId?: number;
  newDisinfectionCompanyId?: number;
  oldDisinfectionCompanyId?: number;
}) => await post(`${URLRequestPrefix.OM}/corp/disinfection/reAssignTask`, data);
// 获取消杀任务中止原因列表
export const getDisinfectionSuspendReasonList = async () =>
  await get(`${URLRequestPrefix.OM}/common/disinfection/config/suspend-reason`);

// 获取当前用户有权限消杀公司的员工简单信息列表
export const getDisinfectionEmployeeSimpleList = async () =>
  await get(`${URLRequestPrefix.OM}/common/disinfection/company/employee/list/simple`);

// 获取自检任务列表扩展字段数据
export const getSelfTaskListExpanded = async (planId: number) =>
  await get(`${URLRequestPrefix.OM}/corp/self-plans/statistics`, { params: { planId } });

// 门店学习情况列表（按组织）
export type StudySituationReq = {
  node: {
    id: string;
    type: string;
  };
  startDate: string;
  endDate: string;
  patrolUserIds: number[];
  planId: number;
  roleIdList: number;
  shopType: string;
  taskTypeList: string[];
  worksheetId: number;
};

export const getStudySituationByOrg = async (data: StudySituationReq) =>
  await post(`${URLRequestPrefix.OM}/common/statistic/shop/study-by-group`, data);

export const getStudySituationByShop = async (data: StudySituationReq) =>
  await post(`${URLRequestPrefix.OM}/common/statistic/shop/study-by-shop`, data);

export const getStudyUncompletedByOrg = async (data: StudySituationReq) =>
  await post(`${URLRequestPrefix.OM}/common/statistic/shop/study/unCompleted/list/byGroup`, data);

export const getStudyUncompletedByShop = async (data: StudySituationReq) =>
  await post(`${URLRequestPrefix.OM}/common/statistic/shop/study/unCompleted/list`, data);

export const exportStudyUncompletedByOrg = async (data: StudySituationReq) =>
  await post(`${URLRequestPrefix.OM}/common/statistic/shop/study/unCompleted/list/byGroup/export`, data);

export const exportStudyUncompletedByShop = async (data: StudySituationReq) =>
  await post(`${URLRequestPrefix.OM}/common/statistic/shop/study/unCompleted/list/export`, data);

// 食安稽核到店辅导任务列表
export interface GetTutorTaskListReq {
  createTimeBegin: string;
  createTimeEnd: string;
  groupId?: number | null;
  inspectorIds?: number[] | null;
  pageNo: number | null;
  pageSize: number | null;
  shopIdList?: string[] | null;
  [property: string]: any;
}

export const getTutorTaskList = async (data: GetTutorTaskListReq) =>
  await post(`${URLRequestPrefix.OM}/corp/patrol/task/food-safety/arrive-shop/list-page`, data);

// 导出食安稽核到店辅导任务列表
export const exportTutorTaskList = async (data: GetTutorTaskListReq) =>
  await post(`${URLRequestPrefix.OM}/corp/patrol/task/food-safety/arrive-shop/list/export`, data);

// 获取食安稽核到店辅导任务详情
export const getTutorTaskDetail = async (taskId: number) =>
  await get(`${URLRequestPrefix.OM}/corp/patrol/task/food-safety/arrive-shop/${taskId}/info`);

// 获取食安稽核到店辅导任务进度
export const getTutorTaskProcess = async (taskId: number) =>
  await get(`${URLRequestPrefix.OM}/corp/patrol/task/food-safety/arrive-shop/${taskId}/progress-list`);

// 判断食安稽核到店辅导任务是否有报告
export const getTutorTaskParentReport = async (taskId: number) =>
  await get(`${URLRequestPrefix.OM}/corp/patrol/task/food-safety/arrive-shop/${taskId}/parent-report-id`);

// 考试情况详情接口
export const getTutorTaskExamDetail = async (taskId: number) =>
  await get(`${URLRequestPrefix.OM}/corp/patrol/task/food-safety/arrive-shop/${taskId}/exam-situation/detail`);

// 临时闭店门店处理
export interface GetCloseOffListReq {
  shopIds?: number[];
  closeEndTime?: string;
  closeStarTime?: string;
  pageNo: number | null;
  pageSize: number | null;
}

// 导出任务转办申请管理
export const exportTransferApplicationList = async (data: any) =>
  await post(`${URLRequestPrefix.OM}/corp/patrol/transfer-application/manage-page/export`, data);

// 分页查询任务转办申请管理
export const getTransferApplicationList = async (data: any) =>
  await post(`${URLRequestPrefix.OM}/corp/patrol/transfer-application/manage-page`, data);

// 获取巡检是否有转办申请and审核权限

export const getTransferIsSelectPermiss = async () =>
  await get(`${URLRequestPrefix.OM}/corp/patrol/transfer-application/has-apply-select-transfer`);
// 转办任务详情
export const getTransferTaskDetail = async (id: number) =>
  await get(`${URLRequestPrefix.OM}/corp/patrol/transfer-application/detail`, { params: id });

// 查询可选转办人列表

export const getTransferUserList = async () =>
  await get(`${URLRequestPrefix.OM}/corp/patrol/transfer-application/inspector-by-user`, {});

export const getTransferUserListStrategy = async (taskId: number) => {
  return await get(`${URLRequestPrefix.TM}/corp/task/transfer/inspector-by-task?taskId=${taskId}`);
};
export const getOutPermissionTransferUserList = async (params: { taskId: number; keyword?: string }) => {
  return await get(
    `${URLRequestPrefix.TM}/corp/task/transfer/out-permission/inspector-by-task?taskId=${params?.taskId}${params?.keyword ? `&keyword=${params?.keyword}` : ''}`,
  );
};
export interface IPatrolITransferUserListItem {
  userId: number;
  nickName: string;
  phone: string;
  staffCode?: string;
}

// 获取权限外转办人列表
function getPermissionOutTransferUserList(params: { keyword: string }) {
  return get<IPatrolITransferUserListItem[]>('/om-api/corp/patrol/transfer-application/inspector', { params });
}

/** 获取权限外转办人列表 hook */
export function usePermissionOutTransferUserList() {
  return useRequest(
    async (keyword) => {
      const res = await getPermissionOutTransferUserList({ keyword });

      return res;
    },
    {
      manual: true,
    },
  ) as unknown as Result<IPatrolITransferUserListItem[], [keyword: string]>;
}

// 同意转让
export const saveApproveTransfer = async (data: {
  applicationId: number;
  newInspector: number;
  taskId: number;
  /** 是否为权限外 不传 默认为 IN（权限内） */
  newInspectorPermissionType?: 'IN' | 'OUT';
}) => await post(`${URLRequestPrefix.OM}/corp/patrol/transfer-application/approve`, data);
// 驳回转让
export const saveRejectTransfer = async (data: { applicationId: number; taskId: number; reason: string }) =>
  await post(`${URLRequestPrefix.OM}/corp/patrol/transfer-application/reject`, data);
// 临时闭店门店处理
export interface GetCloseOffListReq {
  shopIds?: number[];
  closeEndTime?: string;
  closeStarTime?: string;
  pageNo: number | null;
  pageSize: number | null;
}
export const getCloseOffList = async (data: GetCloseOffListReq) =>
  await post(`${URLRequestPrefix.OM}/common/temp/shop/closeInfo/page`, data);

// 新增临时闭店
export const addCloseOffShop = async (data: { shopId: number; closeEndTime: string; closeStarTime: string }) =>
  await post(`${URLRequestPrefix.OM}/corp/temp/shop/closeInfo/create`, data);

export interface GetPatrolOnlineTaskListReq {
  searchUserId?: number[];
  startDate?: string;
  endDate?: string;
  pageNo: number | null;
  pageSize: number | null;
}
// 线上稽核门店评分列表
export const getPatrolOnlineTaskList = async (data: GetPatrolOnlineTaskListReq) =>
  await post(`${URLRequestPrefix.TM}/corp/datacenter/patrol-online-task`, data);

// 线上专员门店评分列表
export const getPatrolOnlineSpecialistTaskList = async (data: GetPatrolOnlineTaskListReq) =>
  await post(`${URLRequestPrefix.TM}/corp/datacenter/patrol-online-specialist-task`, data);

// 线上专员到店人员监管任务列表
export const getPatrolTaskNormalList = async (data: GetPatrolOnlineTaskListReq) =>
  await post(`${URLRequestPrefix.TM}/corp/datacenter/patrol-task-normal`, data);

// 线上稽核门店评分列表 导出
export const asyncExportPatrolOnlineTask = async (data: any) =>
  await post(`${URLRequestPrefix.TM}/corp/datacenter/patrol-online-task/export`, data);

// 线上专员门店评分列表 导出
export const asyncExportPatrolOnlineSpecialistTask = async (data: any) =>
  await post(`${URLRequestPrefix.TM}/corp/datacenter/patrol-online-specialist-task/export`, data);

// 线上专员到店人员监管任务列表 导出
export const asyncExportPatrolTaskNormal = async (data: any) =>
  await post(`${URLRequestPrefix.TM}/corp/datacenter/patrol-task-normal/export`, data);

// 第二点评列表
export interface GetSecondReviewListReq {
  pageNo: number;
  pageSize: number;
}
export const getSecondReviewList = async (params: GetSecondReviewListReq) =>
  await get(`${URLRequestPrefix.OM}/corp/self-task/second-review-list`, { params });

// 第一点评列表
export const getSelfTaskReviewList = async (params: GetSecondReviewListReq) =>
  await get(`${URLRequestPrefix.OM}/corp/self-task/review-list`, { params });

// 根据角色获取人员
export const getUserByRole = async (params: { role: number }) =>
  await get(`${URLRequestPrefix.OM}/common/user/getUserByRole`, { params });

// 查询常规巡检任务批次号
export const getRoutinePatrolPlanBatchList = async (data: { planId: number; pageNo: number; pageSize: number }) =>
  await post(`${URLRequestPrefix.OM}/common/routine/patrol/plan/batchList`, data);
// 查询自检任务批次号
export const getSelfTaskBatchList = async (params: { planId: number; pageNo: number; pageSize: number }) =>
  await get(`${URLRequestPrefix.OM}/corp/self/batches`, { params });

// 作废批次相关任务
export const invalidRoutinePatrolPlanBatch = async (data: { id: number }) =>
  await post(`${URLRequestPrefix.OM}/corp/routine/patrol/plan/batch/invalid/${data.id}`);

// 作废自检批次相关任务
export const invalidSelfTaskBatch = async (data: { planId: number; taskDate: string }) =>
  await post(`${URLRequestPrefix.OM}/corp/self/invalidate`, data);

// 作废批次明细
export const invalidRoutinePatrolPlanBatchDetail = async (data: {
  batchId: number;
  pageNo: number;
  pageSize: number;
}) => await post(`${URLRequestPrefix.OM}/common/routine/patrol/plan/batch/invalid/info`, data);

// 提交任务审批结果
export const submitFileApproveResult = async (data: {
  taskId: number;
  fileApprovedRequest: {
    taskShopFileId: string;
    fileStatus: SheetStatusEnum;
  }[];
}) => await post(`${URLRequestPrefix.OM}/corp/disinfection/dailyPlanTaskShopFile/submitFileApproveResult`, data);
