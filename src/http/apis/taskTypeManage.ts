import { get, post } from '..';
import { URLRequestPrefix } from '../config';

export type taskTypeTreeParams = {
  pageNo: number;
  pageSize: number;
  sortBy: 'asc' | 'desc';
};
//查询任务类型树列表
export const getTaskTypeTree = (data: taskTypeTreeParams) =>
  post(`${URLRequestPrefix.OM}/common/task/type/queryTreePage`, data);
//修改任务类型状态
export const changeStatus = ({ id, status }: { id: string; status: string }) =>
  get(`${URLRequestPrefix.OM}/common/task/type/modStatus/${id}/${status}`);
//保存任务类型
export const saveTaskType = (data) => post(`${URLRequestPrefix.OM}/common/task/type/save`, data);
