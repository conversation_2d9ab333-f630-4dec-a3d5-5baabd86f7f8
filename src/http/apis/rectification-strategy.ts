import { get, post } from '..';
import { URLRequestPrefix } from '../config';
import { StoreType } from '@/constants/common';
import { TaskSubType } from '@/constants/strategy';

// 整改列表
export interface GetRectificationStrategyListReq {
  /**
   * 开始时间
   */
  endDate: null | string;
  /**
   * 组织id
   */
  groupId?: number | null;
  /**
   * 上一个问题id，下一份的时候使用
   */
  lastId?: number | null;
  /**
   * 常规巡检学校情况
   */
  learned?: boolean | null;
  pageNo: number | null;
  pageSize: number | null;
  /**
   * 门店id
   */
  shopIds?: string[] | null;
  /**
   * 门店类型
   */
  shopType?: StoreType;
  /**
   * 开始时间
   */
  startDate: null | string;
  /**
   * 整改状态
   */
  status?: string;
  /**
   * 检查项属性
   */
  tag?: string;
  /**
   * 任务Id
   */
  taskId?: number | null;
  /**
   * 任务类型
   */
  taskType?: TaskSubType;
  /**
   * 模版id
   */
  templateId?: number | null;
  /**
   * 检查表
   */
  worksheetId?: number | null;
  [property: string]: any;
}
export const getRectificationStrategyList = async (data: GetRectificationStrategyListReq) =>
  await post(`${URLRequestPrefix.TM}/corp/issue/pc-page`, data);
export const getPatrolRectificationStrategyList = async (data: GetRectificationStrategyListReq) =>
  await post(`${URLRequestPrefix.TM}/corp/issue/patrol/pc-page`, data);

export const getRectificationDetailBySelf = async (data: { id: number }) =>
  await post(`${URLRequestPrefix.TM}/common/issue/detail`, data);
// 整改审核通过
export const savePassRectification = async (data: { id: number; remark?: string; images?: string[] }) =>
  await post(`${URLRequestPrefix.TM}/common/issue/passRectification`, data);

// 整改审核驳回
export const saveRejectRectification = async (data: { id: number; remark?: string; images?: string[] }) =>
  await post(`${URLRequestPrefix.TM}/common/issue/rejectRectification`, data);
// 常规巡检整改跟踪导出
export const exportRectifyList = async (data: any) =>
  await post(`${URLRequestPrefix.TM}/corp/issue/pc-page/export`, data);

export const exportPatrolRectifyList = async (data: any) =>
  await post(`${URLRequestPrefix.TM}/corp/issue/patrol/pc-page/export`, data);

// 中止循环任务
export const suspendLoopIssue = async (data: any) =>
  await post(`${URLRequestPrefix.TM}/common/loop/issue/task/close`, data);

export const getStrategyStudyDetailList = async (data: any) =>
  await post(`${URLRequestPrefix.TM}/corp/patrol/issue/study/user-study-detail-list`, data);

export const exportStrategyStudyDetailList = async (data: any) =>
  await post(`${URLRequestPrefix.TM}/corp/patrol/issue/study/user-study-detail-list-export`, data);

export const getStrategyStudyDetailInfo = async (id: number) =>
  await get(`${URLRequestPrefix.TM}/corp/patrol/issue/study/user-study-detail-info`, { params: { id } });

export const resetStrategyStudy = async (data: any) =>
  await post(`${URLRequestPrefix.TM}/patrol/issue/report/user-study-reset`, data);
// 食安稽核整改任务-学习情况（策略）
export const getPatrolUserStudyInfo = async (id: number) =>
  await get(`${URLRequestPrefix.TM}/corp/patrol/issue/user-study-info`, { params: { id } });
