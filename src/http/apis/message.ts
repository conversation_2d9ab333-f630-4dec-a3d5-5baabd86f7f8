import { get, post, put } from '..';
import { URLRequestPrefix } from '../config';

// 获取消息模板列表
export const getMessageTemplateList = async (params: any) =>
  await get(`${URLRequestPrefix.OM}/message-push/template/page`, { params });

// 获取消息模板详情
export const getMessageTemplateInfo = (id: number) => {
  return get(`${URLRequestPrefix.OM}/message-push/template/${id}`);
};

// 更新消息模板
export const updateMessageTemplate = ({ id, messageContent, status, channels }: any) => {
  return put(`${URLRequestPrefix.OM}/message-push/template/${id}`, { messageContent, status, channels });
};

// 获取消息模板变量关系列表
export const getMessageTemplateVariables = (id: number) => {
  return get(`${URLRequestPrefix.OM}/message-push/template/${id}/variables`);
};

//
export const getTemplateModules = () => get(`${URLRequestPrefix.OM}/common/message-push/template/business-module`);

// 获取消息模板列表
export const getMessageList = (params: any) => get(`${URLRequestPrefix.TM}/message/templates`, { params });

// 获取消息模板变量关系
export const getMessageVariables = (id: number) => get(`${URLRequestPrefix.TM}/message/templates/${id}/variables`);

// 更新消息模块
export const updateMessageModule = (data: any) => post(`${URLRequestPrefix.TM}/message/template/content`, data);

// 获取消息模块
export const getMessageModules = () => get(`${URLRequestPrefix.TM}/message/modules`);

// 获取消息业务场景
export const getMessageBusinessScenarios = () => get(`${URLRequestPrefix.TM}/message/scenarios`);
