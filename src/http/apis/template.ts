import { get, post } from '..';
import { URLRequestPrefix } from '../config';
import { SignType } from '@/constants/patrol';
import { TaskType } from '@/constants/task';
import { TemplateStatus } from '@/constants/template';

// 自检模板列表
export interface GetSelfTemplateListReq {
  createUser?: number | null;
  pageNo: number | null;
  pageSize: number | null;
  status?: TemplateStatus;
  templateName?: null | string;
  [property: string]: any;
}
export const getSelfTemplateList = async (data: GetSelfTemplateListReq) =>
  await post(`${URLRequestPrefix.TM}/corp/template-self/queryPage`, data);

// 启用禁用自检模板
export interface UpdateTemplateStatusReq {
  id: number;
  status: string;
  [property: string]: any;
}

// 保存自检模板
export interface ApifoxModel {
  executeDuration: string;
  id?: number | null;
  templateName: null | string;

  worksheetIds: any[] | null;
  [property: string]: any;
}

export const saveSelfTemplate = async (data: SaveRoutineTemplateReq) =>
  await post(`${URLRequestPrefix.TM}/corp/template-self/save`, data);

// 获取自检模板详情
export const getSelfTemplateDetail = async (id: number) =>
  await get(`${URLRequestPrefix.TM}/corp/template-self/detail`, { params: { id } });

// 启用禁用巡检模板
export const updateSelfTemplateStatus = async (params: UpdateTemplateStatusReq) =>
  await get(`${URLRequestPrefix.TM}/corp/template-self/updateStatus`, { params });

// 巡检模板列表
export interface GetRoutineTemplateListReq {
  createTimeEnd?: string;
  createTimeStart?: string;
  createUser?: number | null;
  pageNo: number | null;
  pageSize: number | null;
  signType?: SignType;
  status?: TemplateStatus;
  taskType?: TaskType;
  templateName?: null | string;
  [property: string]: any;
}
export const getRoutineTemplateList = async (data: GetRoutineTemplateListReq) =>
  await post(`${URLRequestPrefix.TM}/corp/template-patrol/queryPage`, data);

// 启用禁用巡检模板
export const updateRoutineTemplateStatus = async (params: UpdateTemplateStatusReq) =>
  await get(`${URLRequestPrefix.TM}/corp/template-patrol/updateStatus`, { params });

// 保存巡检模板
export interface SaveRoutineTemplateReq {
  executeDuration: string;
  id?: number | null;
  passScore?: number | null;
  signType?: SignType;
  taskType: TaskType;
  templateName: null | string;
  worksheetIds: number[] | null;
  [property: string]: any;
}

export const saveRoutineTemplate = async (data: SaveRoutineTemplateReq) =>
  await post(`${URLRequestPrefix.TM}/corp/template-patrol/save`, data);

// 获取巡检模板详情
export const getRoutineTemplateDetail = async (id: number) =>
  await get(`${URLRequestPrefix.TM}/corp/template-patrol/detail`, { params: { id } });

// 点评任务模板列表
export interface GetReviewTemplateListReq {
  createUser?: number | null;
  pageNo: number | null;
  pageSize: number | null;
  status?: string;
  templateName?: null | string;
  [property: string]: any;
}

export const getReviewTemplateList = async (data: GetReviewTemplateListReq) =>
  await post(`${URLRequestPrefix.TM}/corp/template-review/queryPage`, data);

// 启用禁用点评模板
export const updateReviewTemplateStatus = async (params: UpdateTemplateStatusReq) =>
  await get(`${URLRequestPrefix.TM}/corp/template-review/updateStatus`, { params });

// 整改任务模板列表
export interface GetRectificationTemplateListtReq {
  createUser?: number | null;
  pageNo: number | null;
  pageSize: number | null;
  status?: string;
  templateName?: null | string;
  [property: string]: any;
}

export const getRectificationTemplateList = async (data: GetRectificationTemplateListtReq) =>
  await post(`${URLRequestPrefix.TM}/corp/template-issue/queryPage`, data);

// 启用禁用整改模板
export const updateRectificationTemplateStatus = async (params: UpdateTemplateStatusReq) =>
  await get(`${URLRequestPrefix.TM}/corp/template-issue/updateStatus`, { params });

// 查询基础任务模板列表
export const getBaseTemplateList = async (data: {
  type: 'SELF' | 'PATROL' | 'REVIEW' | 'ISSUE';
  baseTemplateId?: number;
}) => await post(`${URLRequestPrefix.TM}/corp/template-common/queryBaseTemplateList`, data);

// 查询所有可用策略
export const getStrategyNameList = async () =>
  await post(`${URLRequestPrefix.TM}/corp/strategy/manage/queryAllEnableStrategy`);
// 查询基础任务检查表列表
export const getBaseTemplateChecklistList = async (params: { type: string; templateId: number }) =>
  await get(`${URLRequestPrefix.TM}/corp/template-common/baseTemplateWorksheetList`, { params });

// 基础模板检查项分组列表
export async function getBaseTemplateChecklistGroupList(params: { type: string; templateId: number }) {
  // return await get(`${URLRequestPrefix.TM}/corp/template-common/baseTemplateWorksheetList`, { params });
  return await get(`${URLRequestPrefix.TM}/corp/template-common/baseTemplateWorksheetListByCategory`, { params });
}

// 查询基础任务模板检查项
export const getBaseTemplateCheckItemList = async (worksheetId: number) =>
  await get(`${URLRequestPrefix.TM}/corp/template-common/baseTemplateWorksheetItemList`, { params: { worksheetId } });

// 新增点评任务模板
export const createReviewTemplate = async (data: any) =>
  await post(`${URLRequestPrefix.TM}/corp/template-review/add`, data);
// 新增整改任务模板
export const createRectificationTemplate = async (data: any) =>
  await post(`${URLRequestPrefix.TM}/corp/template-issue/add`, data);

// 获取整改任务模板详情
export const getRectificationTemplateDetail = async (id: number) =>
  await get(`${URLRequestPrefix.TM}/corp/template-issue/detail`, { params: { id } });

// 修改整改任务模板
export const updateRectificationTemplate = async (data: any) =>
  await post(`${URLRequestPrefix.TM}/corp/template-issue/update`, data);

// 获取点评任务模板详情
export const getReviewTemplateDetail = async (id: number) =>
  await get(`${URLRequestPrefix.TM}/corp/template-review/detail`, { params: { id } });

// 修改点评任务模板
export const updateReviewTemplate = async (data: any) =>
  await post(`${URLRequestPrefix.TM}/corp/template-review/update`, data);

// 获取点评整改模板的自检巡检模板列表
export const queryBaseTemplateList = async (data: any) =>
  await post(`${URLRequestPrefix.TM}/corp/template-common/queryBaseTemplateList`, data);

// 转办模板列表
export interface GetTransferTemplateListReq {
  createUser?: number | null;
  pageNo: number | null;
  pageSize: number | null;
  status?: TemplateStatus;
  templateName?: null | string;
}
export const getTransferTemplateList = async (data: GetTransferTemplateListReq) =>
  await post(`${URLRequestPrefix.TM}/corp/template-transfer/queryPage`, data);
// 保存转派模板
export interface SaveTransferTemplateReq {
  templateName: string;
  taskType: 'SELF' | 'PATROL';
  auditRoleIds: number[];
  doRoleIds: number[];
  confirmFlag: boolean;
  transferType: string;
  id?: number;
}
export const saveTransferTemplate = async (data: SaveTransferTemplateReq) =>
  await post(`${URLRequestPrefix.TM}/corp/template-transfer/add`, data);
export const updateTransferTemplate = async (data: SaveTransferTemplateReq) =>
  await post(`${URLRequestPrefix.TM}/corp/template-transfer/update`, data);
// 获取转派模板详情
export const getTransferTemplateDetail = async (id: number) =>
  await get(`${URLRequestPrefix.TM}/corp/template-transfer/detail`, { params: { id } });

// 启用禁用转派模板
export const updateTransferTemplateStatus = async (params: UpdateTemplateStatusReq) =>
  await get(`${URLRequestPrefix.TM}/corp/template-transfer/updateStatus`, { params });
// 引用量
export const getReferenceQuantityList = async (data: {
  templateId: number;
  taskType: TaskType;
  pageNo: number;
  pageSize: number;
}) => await post(`${URLRequestPrefix.TM}/corp/strategy/manage/queryStrategyPage`, data);

// 点评任务列表
export const queryReviewTaskList = async (data: any) =>
  await post(`${URLRequestPrefix.TM}/corp/manager/review/task/pc-page`, data);

/** 待点评任务列表 */
export const queryAwaitReviewTaskList = async (data: any) =>
  await post(`${URLRequestPrefix.TM}/corp/user/review/task/pc-page`, data);

// 导出pc点评任务
export const exportReviewTaskList = async (data: any) =>
  await post(`${URLRequestPrefix.TM}/corp/manager/review/task/pc-page-export`, data);

/** 下一个待点评任务ID */
export const getAwaitNextReviewTaskId = async (data: any) =>
  await post(`${URLRequestPrefix.TM}/corp/user/review/task/next-id`, data);

// 下一个点评任务ID
export const getNextReviewTaskId = async (data: any) =>
  await post(`${URLRequestPrefix.TM}/corp/manager/review/task/next-id`, data);
// 批量修改任务执行人
export const batchModifyReviewTaskUser = async (data: any) =>
  await post(`${URLRequestPrefix.TM}/corp/manager/review/task/batch-modify-task-user`, data);
// 点评任务埋点接口
export const startReviewTask = async (params: { taskId: number }) =>
  await get(`${URLRequestPrefix.TM}/common/review/task/start`, { params });

// 食安到店辅导点评任务列表
export const getArriveShopTutorshipReviewTaskList = async (data: any) =>
  await post(`${URLRequestPrefix.TM}/corp/review/task/arrive-shop-tutorship/pc-page`, data);

// 食安到店辅导待点评任务列表
export const getArriveShopTutorshipAwaitReviewTaskList = async (data: any) =>
  await post(`${URLRequestPrefix.TM}/corp/review/task/arrive-shop-tutorship/user/pc-page`, data);

// 导出食安到店辅导点评任务
export const exportArriveShopTutorshipReviewTaskList = async (data: any) =>
  await post(`${URLRequestPrefix.TM}/corp/review/task/arrive-shop-tutorship/pc-page-export`, data);
// 下一个食安到店辅导点评任务ID
export const getNextArriveShopTutorshipReviewTaskId = async (data: any) =>
  await post(`${URLRequestPrefix.TM}/corp/review/task/arrive-shop-tutorship/next-id`, data);

/** 下一个食安到店辅导待点评任务ID */
export const getNextArriveShopTutorshipAwaitReviewTaskId = async (data: any) =>
  await post(`${URLRequestPrefix.TM}/corp/review/task/arrive-shop-tutorship/user/next-id`, data);

// 食安到店辅导点评到店辅导情况确认
export const confirmArriveShopTutorshipReviewTask = async (data: any) =>
  await post(`${URLRequestPrefix.TM}/corp/review/task/arrive-shop-tutorship/confirm`, data);
// 点评到店辅导情况确认详情
export const getArriveShopTutorshipReviewTaskConfirmDetail = async (params: any) =>
  await get(`${URLRequestPrefix.TM}/corp/review/task/arrive-shop-tutorship/confirm-detail`, { params });
// 获取学习情况
export const getUserStudyInfoByBaseTaskId = async (params: { baseTaskId: number }) =>
  await get(`${URLRequestPrefix.TM}/corp/patrol/issue/user-study-info/by-base-task-id`, { params });
// 刷新学习情况
export const refreshUserStudyInfoByBaseTaskId = async (params: { baseTaskId: number }) =>
  await get(`${URLRequestPrefix.TM}/corp/patrol/issue/refresh-study-info/by-base-task-id`, { params });

// 复制诊断模板
export const copyDiagnosticTemplate = async (templateId: number) =>
  await post(`${URLRequestPrefix.TM}/corp/diagnostic/mapping/copy`, { templateId });
