import { stringify } from 'qs';
import { get, post } from '..';
import { URLRequestPrefix } from '../config';
import { HttpResult } from '../result';
import {
  AddIntentAddOrEdit,
  AddIntentBatchAddOrEdit,
  AddIntentBatchParams,
  AddIntentListParams,
  BatchAccountInvestParamsReq,
  BudgetConfigParams,
  CreateExperimentParams,
  CreateTimePeriodParams,
  DynamicBidConfig,
  ExperimentDetail,
  ExperimentListItem,
  ExperimentListParams,
  FailedRetryParams,
  GeAccountInvestParamsReq,
  GetInvestProjectListReq,
  GetInvestStoreListReq,
  GetRefUserReq,
  GetShopPromotionIntentListReq,
  ItemDeleteParams,
  JoinOrUnJoinParams,
  PlatformType,
  ShopPromotionIntentListRes,
  TimePeriodListItem,
  TimePeriodListParams,
} from '@/constants/invest';
import { BindCameraStatus, StoreStatus, StoreType } from '@/constants/organization';

// 统投管理列表
export const queryInvestStoreList = (data: GetInvestStoreListReq) =>
  post(`${URLRequestPrefix.OM}/corp/shopPromotionIntentBatch/list`, data);

// 投流结果列表
export const queryResultList = (data: GetInvestStoreListReq) =>
  post(`${URLRequestPrefix.OM}/corp/promotion/plan/result/query`, data);

// 统投任务下发
export const startInvestStore = async (id: number) =>
  await post(`${URLRequestPrefix.OM}/corp/shopPromotionIntentBatch/start`, { id });

// 预算计算配置详情
export const budgetConfigDetail = async (brandId: number) =>
  await post(`${URLRequestPrefix.OM}/corp/min/budget/config/detail`, { brandId });

// 新建或更新预算计算配置
export const budgetConfigUpsert = async (data: BudgetConfigParams) =>
  await post(`${URLRequestPrefix.OM}/corp/min/budget/config/upsert`, data);

// 投流月度计划管理列表
export const queryMonthPlanList = (data: GetInvestStoreListReq) =>
  post(`${URLRequestPrefix.OM}/corp/manual/promotion/intent/task/list`, data);

// 投流月度计划管理-生成计划
export const createMonthPlan = async (data: BudgetConfigParams) =>
  await post(`${URLRequestPrefix.OM}/corp/manual/promotion/intent/task/create`, data);

// 统投确认详情列表
export const getShopPromotionIntentList = async (
  data: GetShopPromotionIntentListReq,
): Promise<HttpResult<ShopPromotionIntentListRes>> =>
  await post<ShopPromotionIntentListRes>(`${URLRequestPrefix.OM}/corp/shopPromotionIntent/list`, data);

// 删除指定记录
export const shopPromotionIntentDel = async (id: number) =>
  await post(`${URLRequestPrefix.OM}/corp/shopPromotionIntent/del/${id}`);

// 修改投流明细
export const updateJoinOrUnJoin = async (data: JoinOrUnJoinParams) =>
  await post(`${URLRequestPrefix.OM}/corp/shopPromotionIntent/joinOrUnJoin`, data);

/**
 * @method            getGroupTreeList
 * @description       获取组织树数据不返回塔斯汀节点
 * @returns
 */
export const getGroupTreeList = async () =>
  await post(`${URLRequestPrefix.Center}/gzt/shop/getGroupTreeList`, {
    groupType: 2,
    privilegeCode: 1,
  });

// 获取门店管理列表
export type GetStoreListReq = {
  bindCameraStatus?: BindCameraStatus; // 绑定摄像头状态 0 没有 1 绑定
  fightGroupId: number; // 战区组织id
  fightGroupIds?: number[];
  groupType: number; // groupType 1-区位组织，2-战区组织
  privilegeCode: number; // privilegeCode 权限类型：1-战区组织，2-门店监控
  shopStatus?: StoreStatus; // 门店状态 1:筹备中、2:营业中、3:停业中、4:闭店中、8:待营业
  type?: StoreType; // 门店类型 取字典枚举 1:直营，2:加盟
};
export const getStoreList = async (data: GetStoreListReq) =>
  await post(`${URLRequestPrefix.Center}/gzt/shop/getShopList`, data);

// 获取投流计划列表
export const queryProjectList = (data: GetInvestProjectListReq) =>
  post(`${URLRequestPrefix.OM}/corp/promotion/plan/query`, data);

// 计划失败重试
export const planFailedRetry = (data: FailedRetryParams) =>
  post(`${URLRequestPrefix.OM}/corp/promotion/plan/failed/retry`, data);

// 计划子项删除
export const planItemDelete = (data: ItemDeleteParams) =>
  post(`${URLRequestPrefix.OM}/corp/promotion/plan/item/delete`, data);

// 获取管理员配置
export const getRefUserList = (data: GetRefUserReq) => {
  const Data = { ...data, shopIds: data?.shopIds?.join(',') };

  return get(`${URLRequestPrefix.OM}/corp/routine/task/flow/ref-user?${stringify(Data)}`);
};

// 管理员导入模板
export const getImportUrl = () => get(`${URLRequestPrefix.OM}/common/routine/task/flow/export`);

// 导入接口

export function createBatchImport(data: any) {
  const formData = new FormData();

  formData.append('file', data?.file.file);
  formData.append('platform', data?.platform);

  return post(`${URLRequestPrefix.OM}/corp/routine/task/flow/ref-user/import`, formData);
}

// 更新管理员配置

export const updateManageUser = (data: { shopId?: string; shopName?: string; nickName?: string; phone?: string }) =>
  post(`${URLRequestPrefix.OM}/corp/routine/task/flow/ref-user/update`, data);

// 统投确认管理详情导出

export const exportShopPromotionIntentDetailList = (data: GetInvestStoreListReq) =>
  post(`${URLRequestPrefix.OM}/corp/shopPromotionIntent/export`, data);

// 统投管理  详情批量导入
export const importShopPromotionIntent = (data: any) => {
  const formData = new FormData();

  formData.append('file', data?.file.file);
  formData.append('costType', data?.costType);

  return post(`${URLRequestPrefix.OM}/common/routine/task/flow/amount/import`, formData);
};

export const exportBidRatio = (data: any) => post(`${URLRequestPrefix.OM}/corp/promotion/plan/bid/ratio/export`, data);

export const importBidRatio = (data: any) => {
  const formData = new FormData();

  formData.append('file', data?.file.file);

  return post<any[]>(`${URLRequestPrefix.OM}/common/routine/task/flow/bid/ratio/import`, formData);
};

export interface IOperateRecordListItem {
  id: number;
  planMouth: string;
  shopId: string;
  shopName: string;
  beforePlanFee: number;
  afterPlanFee: number;
  beforePromotion: boolean;
  afterPromotion: boolean;
  exitPromotionReason: string;
  effectiveDate: string;
  operatorId: number;
  operatorName: string;
  operatorPhone: string;
  operateTime: string;
}

// 获取操作记录列表数据
export function queryOperateRecordList(data: any) {
  return post('/om-api/corp/shopPromotionIntentLog/queryListByPage', data) as unknown as Promise<
    PageRes<IOperateRecordListItem>
  >;
}

// 导出操作记录报表
export function exportOperateRecordReport(data: {
  batchId: number;
  shopIds: string[];
  operateStartDate: string;
  operateEndDate: string;
}) {
  return post('/om-api/corp/shopPromotionIntentLog/export', data);
}

// 获取每月投流预算详情
export function getInvertBudgetDetailMonthly(data: { intentId: number }) {
  return post('/om-api/corp/shopPromotionIntent/dynamic/detail', data) as unknown as Promise<{
    dynamicDataList: {
      /** 推荐日费用 */
      amtTransPreM1: number;
      date: string;
      /** 实际日费用 */
      transPreM1: number | null;
    }[];
  }>;
}

// 账号列表
// 0美团 1饿了么
export const getAccountList = (data: { platform: 0 | 1; brandId: number }) =>
  post(`${URLRequestPrefix.OM}/corp/account/list`, data);

// 更新账号参数配置
export const updateAccountInvestParams = (data: GeAccountInvestParamsReq) =>
  post(`${URLRequestPrefix.OM}/corp/account/param/config/upsert`, data);

// 删除账号参数配置
export const deleteAccountInvestParams = (data: { brandId: number; account: string }) =>
  post(`${URLRequestPrefix.OM}/corp/account/param/config/delete`, data);

// 批量设置所有账号与组织的搜索词
export const batchAccountInvestParams = (data: BatchAccountInvestParamsReq) =>
  post(`${URLRequestPrefix.OM}/corp/account/group/params/batch/set`, data);

// 查询已使用战区
export const getUsedGroups = (data: { platform: 0 | 1; brandId: number }) =>
  post(`${URLRequestPrefix.OM}/corp/account/used/groups`, data);

// 动态调价参数配置查询
export const getDynamicBidConfig = (data: { platform: 0 | 1; brandId: number }) =>
  post(`${URLRequestPrefix.OM}/corp/dynamic/bid/config/query`, data);

// 动态调价参数配置
export const updateDynamicBidConfig = (data: DynamicBidConfig) =>
  post(`${URLRequestPrefix.OM}/corp/dynamic/bid/config/upsert`, data);

// 加投计划管理
export const addIntentBatchList = (data: AddIntentBatchParams) =>
  post(`${URLRequestPrefix.OM}/corp/takeaway/addIntent/batch/list`, data);

// 创建计划
export const addIntentBatchAdd = (data: AddIntentBatchAddOrEdit) =>
  post(`${URLRequestPrefix.OM}/corp/takeaway/addIntent/batch/add`, data);

// 编辑计划
export const addIntentBatchEdit = (data: AddIntentBatchAddOrEdit) =>
  post(`${URLRequestPrefix.OM}/corp/takeaway/addIntent/batch/edit`, data);

// 加投计划详情
export const addIntentBatchDetail = (id: number) =>
  post(`${URLRequestPrefix.OM}/corp/takeaway/addIntent/batch/detail/${id}`);

// 创建加投计划的数据导出
export function addIntentBatchExport(data: any) {
  return post(`${URLRequestPrefix.OM}/corp/takeaway/addIntent/batch/add/export`, data);
}
// 创建加投计划的数据导入
export const addIntentBatchImport = (data: any) => {
  const formData = new FormData();

  formData.append('file', data?.file.file);

  return post(`${URLRequestPrefix.OM}/corp/takeaway/addIntent/batch/add/import`, formData);
};

// 加投计划-通知下发
export const addIntentBatchStart = (id: number) =>
  post(`${URLRequestPrefix.OM}/corp/takeaway/addIntent/batch/start/${id}`);

// 加投计划-取消
export const addIntentBatchCancel = (id: number) =>
  post(`${URLRequestPrefix.OM}/corp/takeaway/addIntent/batch/cancel/${id}`);

// 加投计划详情列表
export const addIntentList = async (data: AddIntentListParams) =>
  post(`${URLRequestPrefix.OM}/corp/takeaway/addIntent/list`, data);

// 加投计划详情列表-编辑
export const addIntentEdit = async (data: AddIntentAddOrEdit) =>
  post(`${URLRequestPrefix.OM}/corp/takeaway/addIntent/edit`, data);

// 加投计划详情导出
export function addIntentListExport(data: any) {
  return post(`${URLRequestPrefix.OM}/corp/takeaway/addIntent/list/export`, data);
}
// 加投计划详情导入
export const addIntentListImport = (data: any) => {
  const formData = new FormData();

  formData.append('file', data?.file.file);

  return post(`${URLRequestPrefix.OM}/corp/takeaway/addIntent/importEdit`, formData);
};
// 投流时段管理列表
export const fetchTimePeriodList = async (data: TimePeriodListParams) =>
  post(`${URLRequestPrefix.OM}/corp/promotion/plan/time/period/template/list`, data) as unknown as Promise<{
    result: TimePeriodListItem[];
    total: number;
    pages: number;
  }>;

// 投流时段管理-已配置门店
export const timePeriodShop = async (data: { platform: PlatformType }) =>
  post(
    `${URLRequestPrefix.OM}/corp/promotion/plan/time/period/template/configured/shop?platform=${data.platform}`,
  ) as unknown as Promise<{ configuredShopIds: string[] }>;

export const createTimePeriod = async (data: CreateTimePeriodParams) =>
  post(`${URLRequestPrefix.OM}/corp/promotion/plan/time/period/template/add`, data);

export const editTimePeriod = async (data: CreateTimePeriodParams & { templateId: number }) =>
  post(`${URLRequestPrefix.OM}/corp/promotion/plan/time/period/template/update`, data);

export const removeTimePeriod = async (data: { templateId: number; userId: string; userName: string }) =>
  post(`${URLRequestPrefix.OM}/corp/promotion/plan/time/period/template/delete`, data);

// 投流实验管理
export const fetchExperimentList = async (data: ExperimentListParams) =>
  post(`${URLRequestPrefix.OM}/corp/promotion/experiment/config/list`, data) as unknown as Promise<{
    result: ExperimentListItem[];
    total: number;
    pages: number;
  }>;

export const fetchExperimentDetail = async (data: { experimentId: number }) =>
  post(`${URLRequestPrefix.OM}/corp/promotion/experiment/config/detail`, data) as unknown as Promise<ExperimentDetail>;

export const createExperiment = async (data: CreateExperimentParams) =>
  post(`${URLRequestPrefix.OM}/corp/promotion/experiment/config/upsert`, data);

export const removeExperiment = async (data: { experimentId: number; userId: string; userName: string }) =>
  post(`${URLRequestPrefix.OM}/corp/promotion/experiment/config/delete`, data);

export const fetchExperimentOptions = async (data: { platform: number }) =>
  post(`${URLRequestPrefix.OM}/corp/promotion/experiment/config/options`, data) as unknown as Promise<{
    experiments: { experimentId: number; experimentName: string; relExperimentTags: string[] }[];
  }>;
export const fetchExperimentTags = async (data: { platform: number }) =>
  post(`${URLRequestPrefix.OM}/corp/promotion/experiment/tags?platform=${data.platform}`) as unknown as Promise<
    { experimentTag: string; experimentTagDesc: string }[]
  >;
/** 查询已参与指定实验标签的门店（新建/编辑时使用） */
export const fetchExperimentShopList = async (data: {
  platform: number;
  experimentId: number;
  experimentTags: string[];
}) => post(`${URLRequestPrefix.OM}/corp/promotion/experiment/shop/experiments`, data) as unknown as Promise<string[]>;
