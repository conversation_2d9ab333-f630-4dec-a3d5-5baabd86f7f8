import { FC, useRef } from 'react';
import { ActionType, ProCard, ProForm, ProTable } from '@ant-design/pro-components';
import { Button, message, Modal, Space } from 'antd';
import useStrategyModal, { StrategyModalType } from './hooks/use-strategy-modal';
import Service, { RequestName } from './service';
import { commonColumns } from './table-config';
import { Permission } from '@/components/Permission';
import { getColumns, getCommonConfig, onReset } from '@/components/pro-table-config';
import { Auth } from '@/constants/auth';
import { StrategyTaskType } from '@/constants/strategy';
import { RouteKey } from '@/router/config';
import useGlobalNavigate from '@/router/useGlobalNavigate';
import { renderPage } from '@/utils/render';

const StrategyManage: FC = renderPage(() => {
  const [form] = ProForm.useForm();
  const navigate = useGlobalNavigate();
  const [service, executeRequest] = Service();
  const actionRef = useRef<ActionType>();

  const { showModal } = useStrategyModal();
  const commonConfig = getCommonConfig({
    search: {
      form,
    },
  });

  const columns = getColumns({
    searchColumns: [
      {
        title: '策略名称',
        dataIndex: 'strategyName',
        colSize: 1,
      },
      {
        title: '策略ID',
        dataIndex: 'id',
        colSize: 1,
        valueType: 'digit',
        fieldProps: {
          precision: 0,
        },
      },
      {
        title: '基础类型',
        dataIndex: 'taskType',
        colSize: 1,
        valueType: 'select',
        valueEnum: { [StrategyTaskType.SELF]: '自检任务', [StrategyTaskType.PATROL]: '巡检任务' },
      },
      {
        title: '激活状态',
        dataIndex: 'enable',
        colSize: 1,
        valueType: 'select',
        valueEnum: {
          true: '启用',
          false: '关闭',
        },
      },
    ],
    tableColumns: [
      ...commonColumns,
      {
        title: '操作',
        dataIndex: 'operator',
        width: 150,
        render: (_, record) => {
          return (
            <Space>
              <a
                onClick={() => {
                  showModal({
                    type: StrategyModalType.Edit,
                    initialValues: {
                      strategyName: record?.strategyName,
                      id: record?.id,
                      enable: record?.enable,
                    },
                    onOk: (data) => {
                      return executeRequest(RequestName.UpdateStrategy, data).then(() => {
                        message?.success('编辑策略成功');
                        actionRef?.current?.reload();
                      });
                    },
                  });
                }}
              >
                编辑
              </a>
              {!record.enable ? (
                <a
                  onClick={() => {
                    navigate(RouteKey.SMConfig, {
                      searchParams: {
                        id: record?.id,
                        safetyFlag: record?.safetyFlag,
                      },
                    });
                  }}
                >
                  策略配置
                </a>
              ) : null}
              <a
                onClick={() => {
                  navigate(RouteKey.SMConfig, {
                    searchParams: {
                      id: record?.id,
                      readonly: true,
                      safetyFlag: record?.safetyFlag,
                    },
                  });
                }}
              >
                查看详情
              </a>
              {!record?.enable && (
                <a
                  onClick={() => {
                    Modal.confirm({
                      title: '删除策略',
                      closable: true,
                      okText: '确认',
                      cancelText: '取消',
                      icon: null,
                      content: '策略删除后无法找回，是否确认删除策略？',
                      onOk: async () => {
                        return await executeRequest(RequestName.DeleteStrategy, record?.id).then(() => {
                          actionRef?.current?.reload(true);
                        });
                      },
                    });
                  }}
                >
                  删除
                </a>
              )}
            </Space>
          );
        },
      },
    ],
  });

  const formatParams = (values) => {
    const { current, pageSize, strategyName, id, taskType, enable } = values;

    return {
      pageSize,
      pageNo: current,
      strategyName,
      id,
      taskType,
      enable,
    };
  };

  return (
    <>
      <ProTable
        {...commonConfig}
        columns={columns}
        actionRef={actionRef}
        tableExtraRender={() => {
          return (
            <ProCard>
              <Space>
                <Permission permission={Auth.策略管理_新增策略}>
                  <Button
                    type="primary"
                    onClick={() => {
                      showModal({
                        type: StrategyModalType.Add,
                        onOk: (data) => {
                          return executeRequest(RequestName.CreateStrategy, data).then(() => {
                            message?.success('新增策略成功');
                            actionRef?.current?.reload();
                          });
                        },
                      });
                    }}
                  >
                    新增策略
                  </Button>
                </Permission>
                <Permission permission={Auth.策略管理_新增食安策略}>
                  <Button
                    type="primary"
                    onClick={() => {
                      showModal({
                        type: StrategyModalType.AddFoodSafety,
                        onOk: (data) => {
                          return executeRequest(RequestName.CreateStrategy, {
                            ...data,
                            safetyFlag: true,
                          }).then(() => {
                            message?.success('新增食安策略成功');
                            actionRef?.current?.reload();
                          });
                        },
                      });
                    }}
                  >
                    新增食安策略
                  </Button>
                </Permission>
              </Space>
            </ProCard>
          );
        }}
        tableRender={(_p, _d, { table }) => {
          return <ProCard>{table}</ProCard>;
        }}
        form={{
          syncToUrl(values, type) {
            if (type === 'get') {
              const { enable } = values;

              return {
                ...values,
                enable: enable?.toString(),
              };
            }

            return { ...values };
          },
        }}
        onReset={() => onReset(form)}
        request={async (params) => {
          const res = await executeRequest(RequestName.GetStrategyManageList, formatParams(params));

          return {
            data: res?.data ?? [],
            success: true,
            total: res.total,
          };
        }}
      />
    </>
  );
});

export default StrategyManage;
