import './index.scss';

import { createContext, type FC, useEffect, useRef, useState } from 'react';
import { type Cell, type Edge } from '@antv/x6';
import { <PERSON><PERSON>, Card, Drawer, message } from 'antd';
import dayjs from 'dayjs';
import { isBoolean, isNil } from 'lodash';
import { Config } from './components/Config';
import {
  delayExecutedMap,
  EdgeJudgeScoreMethodSymbol,
  EdgeTriggerType,
  EdgeTriggerTypeBE,
  TaskExecuteWayEnum,
} from './components/Config/edge/constants';
import FlowChart from './components/flow-chart';
import { baseTaskNode } from './components/flow-chart/node/base-task-node';
import { NodeEnum } from './components/flow-chart/node/common';
import { endNode } from './components/flow-chart/node/end-node';
import { extendedTaskNode } from './components/flow-chart/node/extended-task-node';
import { startNode } from './components/flow-chart/node/start-node';
import { subprocessNode } from './components/flow-chart/node/subprocess-node';
import Service, { RequestName } from './service';
import { StrategyPatrolType, StrategyRoutineType } from '@/constants/strategy';
import { TemplateStatus } from '@/constants/template';
import useQuerySearchParams from '@/hooks/use-query-search-params';
import useGlobalNavigate from '@/router/useGlobalNavigate';
import { formatTime } from '@/utils/time';

interface StrategyConfigContextType {
  flowChart: FlowChart | undefined;
}

export const StrategyConfigContext = createContext<StrategyConfigContextType | null>(null);

// eslint-disable-next-line max-lines-per-function
const StrategyConfig: FC = () => {
  const [params] = useQuerySearchParams();
  const containerRef = useRef();
  const stencilRef = useRef();
  const flowChartRef = useRef<FlowChart>();
  const [service, executeRequest, dispatch] = Service();
  const [edges, setEdges] = useState<Edge[]>([]);

  const navigate = useGlobalNavigate();

  const [configInit, setConfigInit] = useState<{
    visible: boolean;
    cell: Cell;
    edgesConnectedByNode: Edge[];
  }>({
    visible: false,
    cell: undefined,
    edgesConnectedByNode: [],
  });
  const handleNodeSelected = (cell?: Cell) => {
    dispatch({ templateOptions: [] });

    if ([NodeEnum.开始, NodeEnum.结束].includes(cell?.getData()?.type)) {
      if (!configInit?.visible) {
        setConfigInit({ visible: false, cell: undefined, edgesConnectedByNode: [] });

        return;
      }
    }

    setConfigInit(() => {
      return {
        visible: true,
        cell,
        edgesConnectedByNode: flowChartRef.current.getConnectedEdges(cell),
      };
    });

    if (cell?.isNode()) {
      setEdges(flowChartRef?.current?.getConnectedEdges(cell));
    }
  };

  useEffect(() => {
    executeRequest(RequestName.GetRoleCascaderData, '1,5');
    executeRequest(RequestName.GetGroupTreeList);
    executeRequest(RequestName.GetStrategyNameList, {});
    // executeRequest(RequestName.GetGroupShopTree, { privilegeCode: 1 });
    requestShop();

    if (params?.id) {
      executeRequest(RequestName.GetStrategyDetail, params?.id).then((res) => {
        try {
          const json = JSON.parse(res?.bodyJson || '');

          flowChartRef?.current?.importJson(json);
        } catch (e) {
          console.log(e);
        }
      });
    }

    if (!flowChartRef.current) {
      flowChartRef.current = new FlowChart({
        onNodeSelected: handleNodeSelected,
        dblclick: handleNodeSelected,
        disabled: !isNil(params?.readonly) && JSON.parse(params?.readonly),
      });
    }

    const flowChart = flowChartRef.current;

    flowChart.generateGraph(containerRef?.current);
    flowChart.setBasicNodes([startNode, baseTaskNode, extendedTaskNode, subprocessNode, endNode]);

    if (!params?.readonly) {
      flowChart.generateStencil(stencilRef?.current);
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const requestTemplate = (data: {
    type: 'SELF' | 'PATROL' | 'REVIEW' | 'ISSUE';
    baseTemplateId?: number;
    taskSubType?: string;
  }) => {
    executeRequest(RequestName.GetBaseTemplateList, { ...data, status: TemplateStatus.EFFECTIVE });
  };

  const requestShop = (fightGroupIds?: string[]) => {
    executeRequest(RequestName.GetStoreList, { fightGroupIds, groupType: 2, privilegeCode: 1 });
  };

  const requestConfig = () => {
    return executeRequest(RequestName.GetStrategyConfig);
  };

  const requestTransfer = (data) => {
    return executeRequest(RequestName.GetTransferTemplateList, data);
  };

  const requestDifferenceItemArriveShopTemplate = (data) => {
    return executeRequest(RequestName.GetDifferenceItemArriveShopTemplateList, data);
  };

  const temporaryStorage = () => {
    const json = flowChartRef?.current?.toJSON();

    return executeRequest(RequestName.UpdateStrategy, { id: params?.id, bodyJson: JSON.stringify(json) }).then(() => {
      return json;
    });
  };

  const requestDownloadUrl = () => {
    return executeRequest(RequestName.GetTemplateDownloadUrl).then((res) => {
      if (res.data) {
        const blob = new Blob([res.data]);
        const _url = URL.createObjectURL(blob);
        const link = document.createElement('a');

        link.href = _url;

        const queryFileName = decodeURI(res.headers['content-disposition'].split(';')[1].split('filename=')[1]);

        link.download = queryFileName;
        link.click();
      }
    });
  };

  return (
    <StrategyConfigContext.Provider
      value={{
        flowChart: flowChartRef.current,
      }}
    >
      <div className="flex flex-col select-none">
        <Card
          extra={
            !params?.readonly && (
              <div className="flex justify-end py-4">
                <Button
                  className="mr-4"
                  onClick={() => {
                    temporaryStorage();
                  }}
                >
                  暂存
                </Button>
                <Button
                  type="primary"
                  onClick={() => {
                    // 提交前先暂存数据
                    temporaryStorage().then((json: any) => {
                      const eventList = json?.cells?.map((item) => item?.data?.triggers);
                      const isEvent = eventList?.some((item) =>
                        item?.some((item) => item?.triggerType === EdgeTriggerType.EVENT),
                      );

                      const formatTrigger = (trigger) => {
                        const {
                          triggerType,
                          taskCycleType,
                          triggerTime,
                          date,
                          dateRange,
                          delayExecuted,
                          delayDays,
                          delayExecuteTime,
                        } = trigger;

                        return {
                          type: EdgeTriggerTypeBE[triggerType],
                          taskCycleType,
                          triggerTime: formatTime(triggerTime),
                          startDate:
                            triggerType === EdgeTriggerType.CIRCULATION
                              ? dayjs(dateRange?.[0]).format('YYYY-MM-DD')
                              : dayjs(date).format('YYYY-MM-DD'),
                          endDate:
                            triggerType === EdgeTriggerType.CIRCULATION
                              ? dayjs(dateRange?.[1]).format('YYYY-MM-DD')
                              : dayjs(date).format('YYYY-MM-DD'),
                          runType:
                            triggerType === EdgeTriggerType.EVENT || triggerType === EdgeTriggerType.HIKVISION_EVENT
                              ? 'ONCE'
                              : triggerType,
                          eventValue: trigger?.eventValue,
                          event: (() => {
                            if (triggerType === EdgeTriggerType.HIKVISION_EVENT) {
                              return {
                                hikvisionProjectId: trigger?.hikvisionProjectId,
                                hikvisionTaskId: trigger?.hikvisionTaskId,
                                eventType: 'HIKVISION_PROJECT',
                              };
                            }

                            if (trigger?.event) {
                              return { eventType: trigger?.event, eventValue: trigger?.eventValue };
                            }

                            return undefined;
                          })(),

                          delayExecuted: delayExecutedMap[delayExecuted],
                          ...(delayExecuted === TaskExecuteWayEnum.CUSTOMIZE
                            ? {
                                delayDays,
                                delayExecuteTime: formatTime(delayExecuteTime),
                              }
                            : {}),
                        };
                      };
                      const formatCondition = (condition, index) => {
                        const {
                          strategyTableId,
                          expressionValue,
                          expressionKey,
                          andFlag,
                          level,
                          levelFlag,
                          JudgmentMethod,
                          maxunqualified,
                          notifyScrm,
                        } = condition;

                        const option = service?.strategyOptions?.find((item) => item.value === strategyTableId);
                        const formatLevel = level === 'STAR' ? 'GREEN' : level;

                        const expressionBuilder = () => {
                          // 判断规则为任务逾期或或宣享小盒未付款时
                          if (strategyTableId === 14 || strategyTableId === 15) {
                            return '=0';
                          }

                          if (strategyTableId === 10) {
                            return JudgmentMethod;
                          }

                          if (option?.type === 'PATROL') {
                            if (+levelFlag === 1) {
                              return `== '${formatLevel}'`;
                            }

                            return `!= '${formatLevel}'`;
                          }

                          if (strategyTableId === 13) {
                            return `< ${maxunqualified}`;
                          }

                          if (expressionKey && !isNil(expressionValue)) {
                            return `${EdgeJudgeScoreMethodSymbol[expressionKey]} ${expressionValue}`;
                          }

                          return undefined;
                        };

                        if (option?.type === 'PATROL') {
                          return {
                            strategyTableId,
                            andFlag: index === 0 ? 'true' : andFlag,
                            expression: expressionBuilder(),
                          };
                        }

                        return {
                          strategyTableId,
                          andFlag: index === 0 ? 'true' : andFlag,
                          expression: expressionBuilder(),
                          extraParams: isBoolean(notifyScrm)
                            ? JSON.stringify({
                                flag: notifyScrm,
                              })
                            : undefined,
                        };
                      };

                      const formatJSONToBE = (json: any) => {
                        const edges = [];
                        const nodeMap = [];

                        json?.cells?.forEach((cell) => {
                          if (cell?.shape === 'edge') {
                            edges.push(cell);

                            cell?.data?.triggers?.forEach((trigger) => {
                              if (
                                trigger?.triggerType === EdgeTriggerType.HIKVISION_EVENT &&
                                (!trigger?.hikvisionProjectId || !trigger?.hikvisionTaskId)
                              ) {
                                message.error('海康项目ID和海康任务ID不能为空');

                                throw new Error('海康项目ID和海康任务ID不能为空');
                              }
                            });
                          } else {
                            const id = cell?.id;
                            const nodeType = cell?.data?.type;
                            const cellData = cell?.data;

                            if (cellData?.type === NodeEnum.基础任务) {
                              if (
                                [StrategyRoutineType.VIDEO, StrategyRoutineType.FOOD_SAFETY_VIDEO].includes(
                                  cellData?.taskSubType,
                                ) &&
                                cellData?.taskShopFlag === false
                              ) {
                                if (
                                  !cellData?.videoPatrolType &&
                                  !cellData?.shopIds?.length &&
                                  !cellData?.groupIds?.length
                                ) {
                                  message.error('基础节点的组织或门店或大数据门店结果不能同时为空');

                                  throw new Error('基础节点的组织或门店或大数据门店结果不能同时为空');
                                }

                                if (cellData?.videoPatrolType && !cellData?.videoPatrolLimit) {
                                  message.error('请输入每周巡检门店数量');

                                  throw new Error('请输入每周巡检门店数量');
                                }
                              }
                            }

                            // 如果是基础任务，并且不是通过表格导入，组织或门店需要2选1
                            if (
                              cellData?.type === NodeEnum.基础任务 &&
                              cellData?.taskShopFlag === false &&
                              !isEvent &&
                              ![StrategyRoutineType.VIDEO, StrategyRoutineType.FOOD_SAFETY_VIDEO].includes(
                                cellData?.taskSubType,
                              )
                            ) {
                              if (!cellData?.shopIds?.length && !cellData?.groupIds?.length) {
                                message.error('基础节点的组织或门店不能同时为空');

                                throw new Error('基础节点的组织或门店不能同时为空');
                              }
                            } else if (cellData?.type === NodeEnum.扩展任务 && !isEvent) {
                              // 如果是扩展任务，组织或门店需要2选1
                              if (!cellData?.shopIds?.length && !cellData?.groupIds?.length) {
                                message.error('拓展任务的组织或门店不能同时为空');

                                throw new Error('拓展任务的组织或门店不能同时为空');
                              }
                            }

                            if (
                              [StrategyPatrolType.DIAGNOSTIC, StrategyPatrolType.FOOD_SAFETY_ARRIVE_SHOP].includes(
                                cellData?.taskSubType,
                              ) &&
                              isEvent
                            ) {
                              message.error('诊断任务/食安稽核到店辅导不支持关联策略生成');

                              throw new Error('诊断任务/食安稽核到店辅导不支持关联策略生成');
                            }

                            nodeMap[cell?.id] = {
                              id,
                              nodeType,
                              nodeId: id,
                              subProcessStartNodeId: cell?.data?.subProcessStartNodeId,
                              baseCondition: [NodeEnum.开始, NodeEnum.结束].includes(nodeType)
                                ? undefined
                                : {
                                    ...cell?.data,
                                    type: undefined,
                                  },
                            };
                          }
                        });

                        edges.forEach((edge) => {
                          const { source, target, data } = edge;
                          const { cell: sourceId } = source;
                          const { cell: targetId } = target;

                          const { groupName, triggers, conditions } = data;

                          if (!nodeMap?.[sourceId]?.conditionGroups) {
                            nodeMap[sourceId].conditionGroups = [];
                          }

                          if (
                            triggers?.length &&
                            triggers?.some(
                              (s: {
                                delayExecuted: TaskExecuteWayEnum;
                                delayDays: number;
                                delayExecuteTime: string;
                                triggerTime: string;
                              }) =>
                                s?.delayExecuted === TaskExecuteWayEnum.CUSTOMIZE &&
                                s?.delayDays === 0 &&
                                (dayjs(s?.delayExecuteTime, 'HH:mm:ss').isBefore(dayjs(s?.triggerTime, 'HH:mm:ss')) ||
                                  dayjs(s?.delayExecuteTime, 'HH:mm:ss').isSame(dayjs(s?.triggerTime, 'HH:mm:ss'))),
                            )
                          ) {
                            message.error('当执行日期为0天时，执行时间需大于生成时间');

                            return;
                          }

                          nodeMap[sourceId].conditionGroups.push({
                            postNodeId: targetId,
                            groupName,
                            triggers: triggers?.map((trigger) => formatTrigger(trigger)),
                            conditions: conditions?.map((condition, index) => formatCondition(condition, index)),
                          });
                        });

                        return Object.keys(nodeMap).map((key) => nodeMap[key]);
                      };

                      executeRequest(RequestName.ConfigStrategy, { id: params?.id, nodes: formatJSONToBE(json) }).then(
                        () => {
                          navigate(-1);
                        },
                      );
                    });
                  }}
                >
                  提交
                </Button>
              </div>
            )
          }
        >
          <div className="flex">
            {!params?.readonly && <div ref={stencilRef} className="w-[180px] relative" />}
            <div ref={containerRef} className="h-[700px] flex-1" />
            <Drawer
              placement="right"
              width={420}
              closable={true}
              // mask={false}
              maskClosable={true}
              onClose={() => {
                flowChartRef.current?.clearSelection();
                setConfigInit({ visible: false, cell: undefined, edgesConnectedByNode: [] });
              }}
              destroyOnClose={true}
              open={configInit?.visible}
              getContainer={false}
            >
              <Config
                cell={configInit.cell}
                edges={edges}
                strategyOptions={service?.strategyOptions}
                edgesConnectedByNode={configInit.edgesConnectedByNode}
                roleOptions={service?.roleOptions}
                strategyNameOptions={service?.strategyNameOptions}
                onTaskTypeChange={(val) => {
                  requestTemplate(val);

                  const { type, ...rest } = val;

                  requestTransfer({
                    ...rest,
                    taskType: type,
                  });

                  // if (type === 'PATROL') {
                  requestDifferenceItemArriveShopTemplate({
                    ...rest,
                    taskType: type,
                  });
                  // }
                }}
                submitExcel={(data) => {
                  return executeRequest(RequestName.SubmitExcel, data);
                }}
                transferOptions={service?.transferOptions}
                differenceItemArriveShopTemplateOptions={service?.differenceItemArriveShopTemplateOptions}
                downloadTemplate={requestDownloadUrl}
                templateOptions={service?.templateOptions}
                organizationOptions={service?.organizationOptions}
                // groupShopTreeOptions={service?.groupShopTreeOptions}
                shopOptions={service?.shopOptions}
                onOrganizationChange={(val) => {
                  requestShop(val);
                }}
                requestConfig={requestConfig}
              />
            </Drawer>
          </div>
        </Card>
      </div>
    </StrategyConfigContext.Provider>
  );
};

export default StrategyConfig;
