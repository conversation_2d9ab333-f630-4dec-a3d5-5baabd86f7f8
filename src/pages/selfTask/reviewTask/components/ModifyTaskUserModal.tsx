import { useMemo } from 'react';
import ProForm, { ProFormSelect } from '@ant-design/pro-form';
import { useRequest } from 'ahooks';
import { Button, message, Modal, Space } from 'antd';
import { getUserInfoList } from '@/http/apis/center-control';
import { batchModifyReviewTaskUser } from '@/http/apis/template';
import createModal from '@/utils/antd/createModal';
import { formatUserListToRoleOptions } from '@/utils/format';

interface ModifyTaskUserModalProps {
  taskIds: any[];
}

export const ModifyTaskUserModal = createModal<{}, ModifyTaskUserModalProps>(
  (_p, setVisible, payload) => {
    const [form] = ProForm.useForm();

    const { taskIds } = payload?.data || {};

    const { onSuccess } = payload;

    const { run: batchModifyReviewTaskUserRun, loading } = useRequest(batchModifyReviewTaskUser, {
      manual: true,
      onSuccess: () => {
        message.success('修改执行人成功');
        onSuccess?.();
        setVisible(false);
      },
    });

    const { data: userOptions } = useRequest(getUserInfoList, {});

    const formatUserOptions = useMemo(() => {
      return formatUserListToRoleOptions(userOptions, true);
    }, [userOptions]);

    return [
      {
        footer: (_, { CancelBtn }) => {
          return (
            <Space>
              <CancelBtn />
              <Button
                type="primary"
                onClick={() => {
                  form.submit();
                }}
                loading={loading}
              >
                确定
              </Button>
            </Space>
          );
        },
      },
      <ProForm
        layout="horizontal"
        submitter={false}
        form={form}
        onFinish={(values) => {
          Modal.confirm({
            title: `确定要将${taskIds.length}条任务转派给${values?.userId?.label}吗？`,
            onOk: () => {
              batchModifyReviewTaskUserRun({
                taskIds,
                userId: values?.userId?.value,
              });
            },
          });
        }}
        preserve={false}
      >
        <ProFormSelect
          name="userId"
          label="任务执行人"
          rules={[{ required: true, message: '请选择任务执行人' }]}
          options={formatUserOptions || []}
          width="md"
          fieldProps={{
            showSearch: true,
            labelInValue: true,
          }}
        />
      </ProForm>,
    ];
  },
  {
    title: '请选择任务执行人',
    width: 856,
    destroyOnClose: true,
  },
);
