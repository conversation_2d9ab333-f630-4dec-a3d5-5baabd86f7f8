import { Tabs } from 'antd';
import Counseling from '../components/Counseling';
import { Auth } from '@/constants/auth';
import useQuerySearchParams from '@/hooks/use-query-search-params';
import { usePermission } from '@/hooks/usePermission';

export default function FoodSafe() {
  const hasPermission = usePermission();

  const tabs = [
    {
      key: '1',
      label: '待处理',
      children: <Counseling isAwaitDeal />,
      auth: Auth.任务中心_我的任务_点评任务_食安稽核到店辅导点评任务_待处理,
    },
    {
      key: '2',
      label: '全部',
      children: <Counseling />,
      auth: Auth.任务中心_我的任务_食安稽核到店辅导点评任务,
    },
  ].filter((item) => hasPermission(item.auth));

  const [urlParams, setUrlParams] = useQuerySearchParams();

  return (
    <Tabs
      items={tabs}
      activeKey={urlParams?.activeTab || tabs[0]?.key}
      onChange={(key) => {
        setUrlParams({ ...urlParams, activeTab: key });
      }}
    />
  );
}
