/* eslint-disable react-refresh/only-export-components */
import { ProColumns } from '@ant-design/pro-components';
import dayjs from 'dayjs';
import { InterviewStatus, JobTypeCN } from '@/constants/interview';

export const searchColumns: ProColumns[] = [
  {
    title: '门店编号',
    dataIndex: 'shopId',
    colSize: 1,
  },
  {
    title: '门店名称',
    dataIndex: 'shopName',
    colSize: 1,
  },
  {
    title: '岗位类型',
    dataIndex: 'jobType',
    colSize: 1,
    valueType: 'select',
    valueEnum: JobTypeCN,
  },
  {
    title: '是否全职',
    dataIndex: 'fullTime',
    colSize: 1,
    valueType: 'select',
    valueEnum: {
      true: '全职',
      false: '兼职',
    },
  },
  {
    title: '手机号',
    dataIndex: 'phone',
    colSize: 1,
  },
  {
    title: '微信号',
    dataIndex: 'weixinNo',
    colSize: 1,
  },
  {
    title: '招聘专员',
    dataIndex: 'accountName',
    colSize: 1,
  },
  {
    title: '状态',
    dataIndex: 'status',
    colSize: 1,
    valueType: 'select',
    valueEnum: {
      [InterviewStatus.WAIT_ENTRY]: '待入职',
      [InterviewStatus.ENTRYED]: '已入职',
    },
  },

  {
    title: '创建日期',
    dataIndex: 'createTime',
    colSize: 2,
    valueType: 'dateRange',
    fieldProps: {
      ranges: {
        近一周: [dayjs().subtract(1, 'week'), dayjs()],
        近一月: [dayjs().subtract(1, 'month'), dayjs()],
        近三个月: [dayjs().subtract(3, 'month'), dayjs()],
        近半年: [dayjs().subtract(6, 'month'), dayjs()],
      },
    },
  },
  {
    title: '入职日期',
    dataIndex: 'entryTime',
    colSize: 2,
    valueType: 'dateRange',
    // initialValue: [dayjs().subtract(2, 'week'), dayjs()],
    fieldProps: {
      ranges: {
        近一周: [dayjs().subtract(1, 'week'), dayjs()],
        近一月: [dayjs().subtract(1, 'month'), dayjs()],
        近三个月: [dayjs().subtract(3, 'month'), dayjs()],
        近半年: [dayjs().subtract(6, 'month'), dayjs()],
      },
    },
  },
  {
    title: 'OFFER发放日期',
    dataIndex: 'offerTime',
    colSize: 2,
    valueType: 'dateRange',
    fieldProps: {
      ranges: {
        近一周: [dayjs().subtract(1, 'week'), dayjs()],
        近一月: [dayjs().subtract(1, 'month'), dayjs()],
        近三个月: [dayjs().subtract(3, 'month'), dayjs()],
        近半年: [dayjs().subtract(6, 'month'), dayjs()],
      },
    },
  },
];

export const commonColumns: ProColumns[] = [
  {
    title: '序号',
    dataIndex: 'index',
    width: 60,
    render(_d, _e, index, action) {
      const { pageInfo } = action;
      const { current, pageSize } = pageInfo;

      return (current - 1) * pageSize + index + 1;
    },
  },
  {
    title: '省份',
    dataIndex: 'province',
    width: 80,
  },
  {
    title: '城市',
    dataIndex: 'city',
    width: 80,
  },
  {
    title: '区县',
    dataIndex: 'district',
    width: 80,
  },
  {
    title: '门店编号',
    dataIndex: 'shopId',
    width: 100,
  },
  {
    title: '门店名称',
    dataIndex: 'shopName',
    width: 150,
  },

  {
    title: '岗位类型',
    dataIndex: 'jobType',
    valueEnum: JobTypeCN,
    width: 80,
  },
  {
    title: '是否全职',
    dataIndex: 'fullTime',
    width: 80,
    valueEnum: {
      true: '全职',
      false: '兼职',
    },
  },
  {
    title: '候选人名称',
    dataIndex: 'candidateName',
    width: 160,
  },
  {
    title: '手机号',
    dataIndex: 'phone',
    width: 100,
  },
  {
    title: '微信',
    dataIndex: 'weixinNo',
    width: 100,
  },
  {
    title: '性别',
    dataIndex: 'gender',
    width: 80,
  },
  {
    title: '学历',
    dataIndex: 'degreeName',
    width: 80,
  },
  {
    title: '招聘渠道',
    dataIndex: 'source',
    width: 80,
  },
  {
    title: '招聘专员名称',
    dataIndex: 'accountName',
    width: 120,
  },
  {
    title: '状态',
    dataIndex: 'statusName',
    width: 80,
  },
  {
    title: 'OFFER发放日期',
    dataIndex: 'offerDate',
    width: 150,
    render: (_, record) => record?.offerDate && dayjs(record?.offerDate).format('YYYY-MM-DD'),
  },
  {
    title: '入职日期',
    dataIndex: 'entryDate',
    width: 100,
    render: (_, record) => record?.entryDate && dayjs(record?.entryDate).format('YYYY-MM-DD'),
  },

  {
    title: '创建人',
    dataIndex: 'createByName',
    width: 80,
  },
  {
    title: '创建时间',
    dataIndex: 'createAt',
    width: 150,
    render: (_, record) => record?.createAt && dayjs(record?.createAt).format('YYYY-MM-DD HH:mm:ss'),
  },
  {
    title: '更新人',
    dataIndex: 'updateByName',
    width: 80,
  },
  {
    title: '更新时间',
    dataIndex: 'updateAt',
    width: 150,
    render: (_, record) => record?.updateAt && dayjs(record?.updateAt).format('YYYY-MM-DD HH:mm:ss'),
  },
];

export function getTableColumns() {
  return [...commonColumns] as ProColumns[];
}
