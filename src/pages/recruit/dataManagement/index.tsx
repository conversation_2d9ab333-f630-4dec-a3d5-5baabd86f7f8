import { FC, Key, useEffect, useRef, useState } from 'react';
import { ActionType, ProCard, ProForm, ProTable } from '@ant-design/pro-components';
import dayjs from 'dayjs';
import { ActionHandle } from './components/ActionHandle';
import Service, { RequestName } from './service';
import { getTableColumns, searchColumns } from './table-config';
import { getColumns, getCommonConfig } from '@/components/pro-table-config';
import { getOrganizationTreeColumn } from '@/components/pro-table-config/organization-tree-select';
import { getProvinceCityArea } from '@/http/apis/demandManage';
import { formatDateToUTC } from '@/utils/date';
import { convertListToTree } from '@/utils/format';
import { renderPage } from '@/utils/render';

const DataManagement: FC = renderPage(() => {
  const [form] = ProForm.useForm();
  const [selectKeys, setSelectKeys] = useState<Key[]>([]);
  const [selectedItems, setSelectedItems] = useState<Record<string, any>[]>([]);
  const actionRef = useRef<ActionType>();
  const commonConfig = getCommonConfig({
    search: {
      form,
    },
  });

  const [service, executeRequest] = Service();

  useEffect(() => {
    executeRequest(RequestName.GetOrganizations);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const columns = getColumns({
    searchColumns: [
      getOrganizationTreeColumn({ options: service?.organizationOptions }),
      {
        title: '区域',
        width: 100,
        dataIndex: 'district',
        valueType: 'cascader',
        fieldProps: {
          showSearch: true,
          changeOnSelect: true,
        },
        request: async () => {
          const res = await getProvinceCityArea();
          const cascadedData = convertListToTree(res as any);

          return cascadedData;
        },
      },
      ...searchColumns,
    ],
    tableColumns: getTableColumns(),
  });

  const formatParams = (params) => {
    const { current, district, fullTime, createTime, entryTime, offerTime, pageSize } = params;

    return {
      ...params,
      pageNum: current || 1,
      pageSize: pageSize || 20,
      province: district?.[0],
      city: district?.[1],
      district: district?.[2] || undefined,
      fullTime: fullTime ? JSON.parse(fullTime) : undefined,
      createAtStart: createTime?.[0] && formatDateToUTC(dayjs(createTime?.[0])?.startOf('day')),
      createAtEnd: createTime?.[1] && formatDateToUTC(dayjs(createTime?.[1])?.endOf('day')),
      entryDateStart: entryTime?.[0] && formatDateToUTC(dayjs(entryTime?.[0])?.startOf('day')),
      entryDateEnd: entryTime?.[1] && formatDateToUTC(dayjs(entryTime?.[1])?.endOf('day')),
      offerDateStart: offerTime?.[0] && formatDateToUTC(dayjs(offerTime?.[0])?.startOf('day')),
      offerDateEnd: offerTime?.[1] && formatDateToUTC(dayjs(offerTime?.[1])?.endOf('day')),
      createTime: undefined,
      entryTime: undefined,
      offerTime: undefined,
    };
  };

  const resetSelectedItems = () => {
    actionRef.current?.clearSelected();
    setSelectKeys([]);
    setSelectedItems([]);
  };

  return (
    <>
      <ProTable
        {...commonConfig}
        columns={columns}
        actionRef={actionRef}
        scroll={{ x: 2200 }}
        rowKey={'id'}
        rowSelection={{
          onChange: (selectedRowKeys, selectedRows) => {
            setSelectKeys(selectedRowKeys);
            setSelectedItems(selectedRows);
          },
        }}
        tableRender={(_p, _d, { table }) => {
          return <ProCard>{table}</ProCard>;
        }}
        tableExtraRender={() => {
          return (
            <ProCard>
              <ActionHandle
                executeRequest={executeRequest}
                selectedItems={selectedItems}
                selectedKeys={selectKeys}
                actionRef={actionRef}
                resetSelectedItems={resetSelectedItems}
              />
            </ProCard>
          );
        }}
        request={async (values) => {
          const res = await executeRequest(RequestName.GetDataManagementList, formatParams(values));

          return {
            data: res?.records || [],
            success: true,
            total: res?.total,
          };
        }}
      />
    </>
  );
});

export default DataManagement;
