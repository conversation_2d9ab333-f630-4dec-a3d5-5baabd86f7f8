import useServiceReducer from '@/hooks/use-service-reducer';
import HttpAop from '@/http/aop';
import { getOrganizations } from '@/http/apis/center-control';
import { getDataManagementList, getTemplateUrl, importExcelDataManagementListItem } from '@/http/apis/dataManagement';
import { formatOrganizationsToOptions } from '@/utils/format';

export enum RequestName {
  GetOrganizations,
  GetTemplateUrl,
  ImportExcelDataManagementListItem,
  GetDataManagementList,
}

export const initState: any = {};

const Service = () => {
  const [service, executeRequest, dispatch] = useServiceReducer(initState, {
    [RequestName.GetDataManagementList]: {
      request: getDataManagementList,
    },
    [RequestName.GetTemplateUrl]: {
      request: getTemplateUrl,
    },
    [RequestName.ImportExcelDataManagementListItem]: {
      request: importExcelDataManagementListItem,
    },
    [RequestName.GetOrganizations]: {
      request: HttpAop(getOrganizations, { after: [formatOrganizationsToOptions] }),
      afterRequest: (data, dispatch) => {
        dispatch({ organizationOptions: data });
      },
    },
  });

  return [service, executeRequest, dispatch];
};

export default Service;
