import { ProFormDatePicker, ProFormDependency, ProFormSelect, ProFormText } from '@ant-design/pro-components';
import { message } from 'antd';
import { InterviewStatus, JobType, JobTypeCN } from '@/constants/interview';
import { getShopInfoByShopId } from '@/http/apis/dataManagement';

const degreeNameOptions = ['初中及以下', '中专及以下', '中专/中技', '高中', '大专', '本科', '硕士', '博士'].map(
  (m) => ({
    label: m,
    value: m,
  }),
);

export default function AddInterviewInfo({ form }) {
  const getShopName = async (shopId: string) => {
    if (!shopId) {
      return;
    }

    const res = await getShopInfoByShopId({ shopId });

    if (!res) {
      form?.setFieldValue('shopName', undefined);

      return message.error('此门店编号查询不到门店名称');
    }

    form?.setFieldValue('shopName', res?.shopName);
  };

  return (
    <div className="flex gap-x-6">
      <div className="flex flex-col flex-1 ">
        <ProFormText
          label="门店编码"
          name="shopId"
          placeholder="请输入门店编码"
          rules={[{ required: true, message: '请输入门店编码' }]}
          fieldProps={{
            maxLength: 100,
            onBlur: (e) => {
              const trimmedValue = e.target.value.trim();

              form?.setFieldValue('shopId', trimmedValue);
              getShopName(trimmedValue);
            },
          }}
        />
        <ProFormSelect
          label="岗位类型"
          name="jobType"
          placeholder="请选择岗位类型"
          rules={[{ required: true, message: '请选择岗位类型' }]}
          initialValue={JobType.DIAN_YUAN}
          valueEnum={JobTypeCN}
        />
        <ProFormText
          label="姓名"
          name="candidateName"
          placeholder="请输入姓名"
          rules={[{ required: true, message: '请输入姓名' }]}
          fieldProps={{
            maxLength: 100,
          }}
        />
        <ProFormText
          label="微信号"
          name="weixinNo"
          placeholder="请输入微信号"
          fieldProps={{
            maxLength: 100,
          }}
        />
        <ProFormSelect
          label="性别"
          name="gender"
          placeholder="请选择性别"
          rules={[{ required: true, message: '请选择性别' }]}
          options={[
            {
              label: '女',
              value: 0,
            },
            {
              label: '男',
              value: 1,
            },
          ]}
        />
        <ProFormSelect
          label="状态"
          name="status"
          placeholder="请选择状态"
          rules={[{ required: true, message: '请选择状态' }]}
          valueEnum={{
            [InterviewStatus.WAIT_ENTRY]: '待入职',
            [InterviewStatus.ENTRYED]: '已入职',
          }}
        />
        <ProFormDatePicker
          label="OFFER发放日期"
          name="offerDate"
          placeholder="请选择OFFER发放日期"
          rules={[{ required: true, message: '请选择OFFER发放日期' }]}
          fieldProps={{
            className: 'w-full',
          }}
        />
      </div>
      <div className="flex flex-col flex-1 ">
        <ProFormDependency name={['shopId']}>
          {() => {
            return <ProFormText label="门店名称" name="shopName" placeholder="-" disabled />;
          }}
        </ProFormDependency>

        <ProFormSelect
          className="w-full"
          label="是否全职"
          name="fullTime"
          placeholder="请选择是否全职"
          rules={[{ required: true, message: '请选择是否全职' }]}
          valueEnum={{
            true: '全职',
            false: '兼职',
          }}
        />
        <ProFormText
          label="手机号"
          name="phone"
          placeholder="请输入手机号"
          rules={[{ pattern: /^1\d{10}$/, message: '请输入正确的手机号' }]}
          fieldProps={{
            maxLength: 20,
          }}
          className="w-full"
        />
        <ProFormSelect label="学历" name="degreeName" placeholder="请选择学历" options={degreeNameOptions} />
        <ProFormSelect
          label="招聘渠道"
          name="source"
          placeholder="请选择招聘渠道"
          rules={[{ required: true, message: '请选择招聘渠道' }]}
          valueEnum={{
            BOSS直聘: 'BOSS直聘',
            '58同城': '58同城',
          }}
        />
        <ProFormText
          label="招聘专员"
          name="accountName"
          placeholder="请输入招聘专员"
          rules={[{ required: true, message: '请输入招聘专员' }]}
          fieldProps={{
            maxLength: 100,
          }}
        />

        <ProFormDependency name={['status']}>
          {({ status }) => {
            return (
              <ProFormDatePicker
                label="入职日期"
                name="entryDate"
                placeholder="请选择入职日期"
                rules={[{ required: InterviewStatus[status] === InterviewStatus.ENTRYED, message: '请选择入职日期' }]}
                fieldProps={{
                  className: 'w-full',
                }}
              />
            );
          }}
        </ProFormDependency>
      </div>
    </div>
  );
}
