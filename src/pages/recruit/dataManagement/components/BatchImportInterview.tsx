import { useCallback, useState } from 'react';
import { ProForm, ProFormUploadDragger } from '@ant-design/pro-components';
import { useDebounceFn, useRequest } from 'ahooks';
import { Button, message } from 'antd';
import { RequestName } from '../service';
import { useOSSClient } from '@/hooks/use-oss-client';

type TProps = {
  executeRequest: any;
  closeModal: () => void;
};

export default function BatchImportInterview({ executeRequest, closeModal }: TProps) {
  const { uploadFile } = useCallback(useOSSClient, [])('TMP');
  const [errorUrl, setErrorUrl] = useState('');
  const [form] = ProForm.useForm();
  const excels = ProForm?.useWatch('excels', form);

  const { run: handleDownloadStencil } = useDebounceFn(
    () =>
      executeRequest(RequestName.GetTemplateUrl).then((res) => {
        if (res.data) {
          const blob = new Blob([res.data]);
          const _url = URL.createObjectURL(blob);
          const link = document.createElement('a');

          link.href = _url;

          const queryFileName = decodeURI(res.headers['content-disposition'].split(';')[1].split('filename=')[1]);

          link.download = queryFileName;
          link.click();
        }
      }),
    { wait: 300 },
  );

  const { runAsync: submitInterviewExcel, loading: submitLoading } = useRequest(
    async (response) => {
      const res = await executeRequest(RequestName.ImportExcelDataManagementListItem, {
        fileName: response?.originName,
        url: response?.url,
      });

      console.log(res, 'res-submitInterviewExcel');

      if (!res || !res?.length) {
        setErrorUrl('');
        message.success('导入成功');
        closeModal();
      } else {
        message.error('文件异常');
        setErrorUrl(res);
      }

      return res;
    },
    {
      manual: true,
    },
  );

  const { run: debounceSubmit } = useDebounceFn(
    async (values) => {
      if (!Array.isArray(values?.excels) || !values?.excels?.length) {
        message.error('请选择上传文件');

        return;
      }

      try {
        const response = await uploadFile(values.excels[0]?.originFileObj, true, null, {});

        await submitInterviewExcel(response);
      } catch (error) {
        console.error('文件上传失败:', error);
        message.error('文件上传失败，请稍后重试');
      }
    },
    { wait: 300 },
  );

  return (
    <div>
      <div className="flex justify-start mb-3">
        <Button
          className="px-0"
          type="link"
          onClick={() => {
            console.log('点击了下载链接');

            handleDownloadStencil();
          }}
        >
          下载链接
        </Button>
      </div>
      <ProForm
        form={form}
        submitter={{
          render: () => (
            <div className="flex gap-x-4 justify-center mt-8">
              <Button onClick={closeModal}>取消</Button>
              <Button
                type="primary"
                htmlType="submit"
                disabled={!excels?.length || submitLoading}
                loading={submitLoading}
              >
                提交
              </Button>
            </div>
          ),
        }}
        onFinish={debounceSubmit}
      >
        <ProFormUploadDragger
          name="excels"
          max={1}
          fieldProps={{
            customRequest: async (e: any) => {
              e.onSuccess();
            },
          }}
        />

        {errorUrl && (
          <Button type="link" className="text-[#ff4d4f]" onClick={() => window.open(errorUrl)}>
            点击下载错误信息
          </Button>
        )}
      </ProForm>
    </div>
  );
}
