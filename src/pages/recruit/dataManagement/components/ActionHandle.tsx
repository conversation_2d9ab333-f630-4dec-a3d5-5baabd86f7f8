import { Key, useEffect } from 'react';
import { ActionType, ProForm } from '@ant-design/pro-components';
import { useModal } from '@tastien/thooks';
import { useRequest } from 'ahooks';
import { Button, message, Modal, ModalFuncProps, Popconfirm } from 'antd';
import AddInterviewInfo from './AddInterviewInfo';
import BatchImportInterview from './BatchImportInterview';
import { Permission } from '@/components/Permission';
import { Auth } from '@/constants/auth';
import {
  addDataManagementListItem,
  deleteDataManagementListItem,
  editDataManagementListItem,
} from '@/http/apis/dataManagement';

export function ActionHandle({
  executeRequest,
  selectedItems,
  selectedKeys,
  actionRef,
  resetSelectedItems,
}: {
  executeRequest: any;
  selectedItems: Record<string, any>[];
  selectedKeys: Key[];
  actionRef: React.MutableRefObject<ActionType>;
  resetSelectedItems: () => void;
}) {
  const [form] = ProForm.useForm();

  function showConfirmModal({ title, content, ...restModalProps }: ModalFuncProps) {
    Modal.confirm({
      title,
      content: <div className="text-center py-4">{content}</div>,
      width: 300,
      icon: null,
      ...restModalProps,
    });
  }

  const { runAsync: runAdd, loading: addLoading } = useRequest(
    async (values) => {
      const res = await addDataManagementListItem({
        ...values,
        fullTime: values?.fullTime ? JSON.parse(values?.fullTime) : undefined,
        phone: values?.phone || null,
        weixinNo: values?.weixinNo || null,
      });

      return res;
    },
    {
      manual: true,
    },
  );

  const { runAsync: runEdit, loading: editLoading } = useRequest(
    async (values) => {
      const res = await editDataManagementListItem({
        id: selectedItems[0]?.id,
        ...values,
        fullTime: values?.fullTime ? JSON.parse(values?.fullTime) : undefined,
        phone: values?.phone || null,
        weixinNo: values?.weixinNo || null,
        degreeName: values?.degreeName || null,
        entryDate: values?.entryDate || null,
      });

      return res;
    },
    {
      manual: true,
    },
  );

  const addModalProps = useModal();
  const editModalProps = useModal();
  const { runAsync: deleteRunAsync, loading: deleteLoading } = useRequest(
    async () => {
      const res = await deleteDataManagementListItem({ ids: selectedKeys });

      return res;
    },
    { manual: true },
  );

  useEffect(() => {
    form.resetFields();

    if (editModalProps.visible) {
      const item = selectedItems[0] || {};

      form.setFieldsValue({
        ...selectedItems[0],
        fullTime: JSON.stringify(item?.fullTime),
        gender: item?.gender ? 1 : 0,
      });
    }
  }, [form, editModalProps.visible, addModalProps.visible, selectedItems]);

  return (
    <div className="flex gap-x-3">
      <Permission permission={Auth.人工招聘数据管理_导入}>
        <Button
          type="primary"
          onClick={() => {
            showConfirmModal({
              title: '导入',
              width: 400,
              okText: '提交',
              closable: true,
              content: (
                <BatchImportInterview
                  executeRequest={executeRequest}
                  closeModal={() => {
                    Modal.destroyAll();
                    actionRef.current?.reload();
                  }}
                />
              ),
              footer: null,
            });
          }}
        >
          导入
        </Button>
      </Permission>
      <Permission permission={Auth.人工招聘数据管理_新增}>
        <>
          <Button
            type="primary"
            loading={addLoading}
            disabled={addLoading}
            onClick={() => {
              addModalProps.openModal();
            }}
          >
            新增
          </Button>
          <Modal {...addModalProps} title="新增" footer={null} width={780} onCancel={addModalProps.closeModal}>
            <ProForm
              className="py-6"
              form={form}
              layout="horizontal"
              onFinish={async (values) => {
                form?.validateFields();
                await runAdd({ ...values, degreeName: values?.degreeName || '' });
                message.success('新增成功');
                actionRef.current?.reload();
                addModalProps.closeModal();
              }}
              submitter={{
                render: () => {
                  return [
                    <div className="flex justify-center gap-x-4 mt-6">
                      <Button
                        onClick={() => {
                          addModalProps.closeModal();
                        }}
                      >
                        取消
                      </Button>
                      <Button htmlType="submit" type="primary" loading={addLoading} disabled={addLoading}>
                        保存
                      </Button>
                    </div>,
                  ];
                },
              }}
            >
              <AddInterviewInfo form={form} />
            </ProForm>
          </Modal>
        </>
      </Permission>
      <Permission permission={Auth.人工招聘数据管理_编辑}>
        <>
          <Button
            type="primary"
            loading={editLoading}
            disabled={editLoading || selectedItems.length !== 1}
            onClick={() => {
              editModalProps.openModal();
            }}
          >
            编辑
          </Button>
          <Modal {...editModalProps} title="编辑" footer={null} width={780} onCancel={editModalProps.closeModal}>
            <ProForm
              className="py-6"
              form={form}
              layout="horizontal"
              onFinish={async (values) => {
                form?.validateFields();
                await runEdit({ ...values, degreeName: values?.degreeName || '' });
                message.success('编辑成功');
                resetSelectedItems();
                actionRef.current?.reload();
                editModalProps.closeModal();
              }}
              submitter={{
                render: () => {
                  return [
                    <div className="flex justify-center gap-x-4 mt-6">
                      <Button
                        onClick={() => {
                          editModalProps.closeModal();
                        }}
                      >
                        取消
                      </Button>
                      <Button htmlType="submit" type="primary" loading={editLoading} disabled={editLoading}>
                        保存
                      </Button>
                    </div>,
                  ];
                },
              }}
            >
              <AddInterviewInfo form={form} />
            </ProForm>
          </Modal>
        </>
      </Permission>
      <Permission permission={Auth.人工招聘数据管理_删除}>
        <Popconfirm
          title="提示"
          description="是否确认删除对应数据？"
          onConfirm={async () => {
            await deleteRunAsync();
            resetSelectedItems();
            message.success('删除成功');
            actionRef.current?.reload();
          }}
          okText="确认"
          cancelText="取消"
        >
          <Button type="primary" loading={deleteLoading} disabled={deleteLoading || selectedItems.length === 0}>
            删除
          </Button>
        </Popconfirm>
      </Permission>
    </div>
  );
}
