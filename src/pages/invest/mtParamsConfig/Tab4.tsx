import { useEffect, useRef, useState } from 'react';
import { ActionType, ProFormSelect, ProTable } from '@ant-design/pro-components';
import { useModal } from '@tastien/thooks';
import { IUseModalResult } from '@tastien/thooks/lib/useModal';
import { Button, Form, Input, message, Modal, Switch, Tag } from 'antd';
import dayjs from 'dayjs';
import Service, { RequestName } from './service';
import OrganizationOrStoreSelectModal, {
  ModalType,
  OrganizationOrStoreSelectModalProps,
} from '@/components/organization-or-store-select-modal';
import StoreSelect from '@/components/store-select';
import { ExperimentDetail, ExperimentListItem } from '@/constants/invest';
import {
  createExperiment,
  fetchExperimentDetail,
  fetchExperimentList,
  fetchExperimentShopList,
  fetchExperimentTags,
  removeExperiment,
} from '@/http/apis/invest';
import { userStore } from '@/mobx';
import { getItem } from '@/utils/storage';

const ExperimentModal: React.FC<IUseModalResult<ExperimentListItem> & { platform: number; onSuccess: () => void }> = ({
  visible,
  closeModal,
  initValue,
  platform,
  onSuccess,
}) => {
  const [form] = Form.useForm();
  const [service, executeRequest] = Service();
  const stores = Form.useWatch('shops', form);
  const { info } = userStore;
  const [detail, setDetail] = useState<ExperimentDetail>();

  const [modalProps, setModalProps] = useState<{
    open: boolean;
    type: ModalType;
    value?: any;
    options?: OrganizationOrStoreSelectModalProps['options'];
  }>({
    open: false,
    type: ModalType.STORE,
    value: [],
  });

  useEffect(() => {
    !service.groupShopOptions && executeRequest(RequestName.GetGroupShopTree, { privilegeCode: 1 });
  }, [executeRequest, service]);

  useEffect(() => {
    const initialSetForm = async () => {
      if (initValue) {
        const detail = await fetchExperimentDetail({
          experimentId: initValue?.experimentId,
        });

        setDetail(detail);

        form.setFieldsValue({
          ...detail,
          relExperimentTags: detail?.experimentTags || [],
          disableShopOperations: detail?.disableShopOperation || 0,
          shops: detail?.shops || [],
        });
      }
    };

    visible && initialSetForm();
  }, [initValue, form, visible]);

  return (
    <Modal
      open={visible}
      title={initValue ? '编辑实验' : '新建实验'}
      onCancel={() => {
        closeModal();
        form.resetFields();
      }}
      onOk={async () => {
        const shops = form.getFieldValue('shops');
        const experimentTags = form.getFieldValue('relExperimentTags');
        // 获取已配置的门店列表
        const data = await fetchExperimentShopList({
          platform,
          experimentId: initValue?.experimentId,
          experimentTags: experimentTags || [],
        });
        const configuredShopIds = data;

        const excludeConfigShopIds = configuredShopIds.filter((id) => !initValue?.shops?.find((i) => i.shopId === id));

        // 检查选中的门店是否已配置
        const configuredSelectedShops = shops.filter((i) => excludeConfigShopIds.includes(i.shopId));

        if (configuredSelectedShops.length > 0) {
          Modal.warning({
            title: '警告',
            content: `以下门店已被配置：${configuredSelectedShops.map((i) => i.shopName).join(',')}，将自动剔除`,
          });
        }

        form?.setFieldValue(
          'shops',
          (shops || []).filter((i) => !excludeConfigShopIds.includes(i.shopId)),
        );

        form.submit();
      }}
      width={600}
    >
      <Form
        form={form}
        onFinish={async (values) => {
          try {
            initValue
              ? await createExperiment({
                  experimentId: initValue?.experimentId,
                  ...values,
                  disableShopOperations: values.disableShopOperations ? 1 : 0,
                  platform,
                  userId: info.shUserId,
                  userName: info.nickName,
                })
              : await createExperiment({
                  ...values,
                  disableShopOperations: values.disableShopOperations ? 1 : 0,
                  platform,
                  userId: info.shUserId,
                  userName: info.nickName,
                });

            closeModal();
            form.resetFields();
            onSuccess?.();
          } catch (e) {
            // message.error(e?.message || '操作失败');
          }
        }}
        onValuesChange={(change, all) => console.log(change, all)}
      >
        {initValue && (
          <Form.Item label="实验ID">
            <Input disabled value={initValue?.experimentId} />
          </Form.Item>
        )}
        <Form.Item label="实验名称" name="experimentName" rules={[{ required: true, message: '请输入实验名称' }]}>
          <Input placeholder="请输入实验名称" maxLength={50} />
        </Form.Item>

        <ProFormSelect
          label="选择实验"
          name="relExperimentTags"
          fieldProps={{
            mode: 'multiple',
            showSearch: true,
          }}
          request={async () => {
            const res = await fetchExperimentTags({
              platform,
            });

            return res.map((i) => ({
              label: i.experimentTagDesc,
              value: i.experimentTag,
            }));
          }}
        />

        <Form.Item label="禁用门店操作" name="disableShopOperations" valuePropName="checked">
          <Switch checkedChildren="是" unCheckedChildren="否" />
        </Form.Item>

        <Form.Item label="关联门店" name="shops" dependencies={['shops']}>
          <Button
            onClick={() => {
              const relExperimentTags = form.getFieldValue('relExperimentTags');

              if (!relExperimentTags || relExperimentTags.length === 0) {
                message.warning('请先选择实验');

                return;
              }

              setModalProps({
                open: true,
                type: ModalType.STORE,
                value: (stores || []).map((i) => ({ value: `SHOP-${i.shopId}`, label: i.shopName })),
              });
            }}
          >
            已选择<span className="mx-1">{stores?.length || 0}</span>个门店
          </Button>
          <div className="mt-4 rounded border border-[#d9d9d9] border-solid h-[150px] mr-2 p-2 overflow-y-auto">
            {stores?.map(({ shopName, shopId, deleted }) => (
              <Tag
                key={shopId}
                className="mr-1 mb-1"
                closeIcon={true}
                color={deleted ? 'error' : 'default'}
                style={{ display: 'inline-block' }}
                onClose={() => {
                  const res = (stores || []).filter((store) => store?.shopId !== shopId);

                  form.setFieldValue('shops', res);
                }}
              >
                {shopName}
              </Tag>
            ))}
          </div>
        </Form.Item>
      </Form>
      <OrganizationOrStoreSelectModal
        open={modalProps.open}
        type={modalProps.type}
        value={modalProps.value}
        options={service?.groupShopOptions}
        onOk={async (value) => {
          console.log('choose shop', value);

          const experimentTags = form.getFieldValue('relExperimentTags');
          // 获取已配置的门店列表
          const data = await fetchExperimentShopList({
            platform,
            experimentId: initValue?.experimentId,
            experimentTags: experimentTags || [],
          });
          const configuredShopIds = data;

          // 获取已配置的门店列表 排除当前实验的门店
          const excludeConfigShopIds = configuredShopIds.filter((id) => !detail?.shops?.find((i) => i.shopId === id));

          // 检查选中的门店是否已配置
          const configuredSelectedShops = value.filter((i) =>
            excludeConfigShopIds.includes(i.value.split('SHOP-')?.[1]),
          );

          if (configuredSelectedShops.length > 0) {
            Modal.warning({
              title: '警告',
              content: `以下门店已被配置：${configuredSelectedShops.map((i) => i.label).join(',')}，将自动剔除`,
            });
          }

          form?.setFieldValue(
            'shops',
            (value || [])
              .filter((i) => !excludeConfigShopIds.includes(i.value.split('SHOP-')?.[1]))
              .map((i) => ({ shopId: i.value.split('SHOP-')?.[1], shopName: i.label })),
          );
          setModalProps({ ...modalProps, open: false });
        }}
        onCancel={() => setModalProps({ type: ModalType.STORE, open: false })}
      />
    </Modal>
  );
};

const Tab4 = ({ platform }: { platform: number }) => {
  const actionRef = useRef<ActionType>();
  const [service, executeRequest] = Service();
  const experimentModalRef = useModal<ExperimentListItem | undefined>();
  const { info } = userStore;

  useEffect(() => {
    !service.organizationOptions && executeRequest(RequestName.GetGroupTreeList);
  }, [executeRequest, service]);

  return (
    <>
      <ProTable<ExperimentListItem, { store: { shopIds: string[] }; timePeriodTemplateName: string }>
        actionRef={actionRef}
        headerTitle="投流实验管理"
        rowKey="id"
        columns={[
          {
            title: '门店',
            dataIndex: 'store',
            hideInTable: true,
            colSize: 2,
            renderFormItem: () => {
              return (
                <StoreSelect
                  organizationOptions={service?.organizationOptions || []}
                  storeOptions={service?.storeOptions || []}
                  onStoreFocus={(groupId: any) => {
                    executeRequest(RequestName.GetStoreList, {
                      fightGroupId: groupId,
                      groupType: 2,
                      privilegeCode: 1,
                    });
                  }}
                />
              );
            },
          },
          {
            title: '实验标签',
            dataIndex: 'experimentTag',
            valueType: 'select',
            colSize: 1,
            request: async () => {
              const res = await fetchExperimentTags({
                platform,
              });

              return res.map((i) => ({
                label: i.experimentTagDesc,
                value: i.experimentTag,
              }));
            },
            hideInTable: true,
          },
          {
            title: '实验ID',
            dataIndex: 'experimentId',
            width: 120,
            search: false,
          },
          {
            title: '实验名称',
            dataIndex: 'experimentName',
            width: 120,
            search: false,
          },

          {
            title: '关联门店',
            dataIndex: 'shops',
            width: 300,
            search: false,
            renderText: (val: { shopName: string; shopId: string }[]) => {
              return (
                <div>
                  {val
                    .slice(0, 5)
                    .map((i) => i.shopName)
                    .join(';')}
                  <a
                    onClick={() => {
                      Modal.info({
                        title: '门店列表',
                        width: 600,
                        content: (
                          <ProTable
                            search={false}
                            columns={[
                              {
                                title: '门店ID',
                                dataIndex: 'shopId',
                              },
                              {
                                title: '门店名称',
                                dataIndex: 'shopName',
                              },
                            ]}
                            rowKey="shopId"
                            request={async () => {
                              return {
                                data: val || [],
                                success: true,
                                total: val.length,
                              };
                            }}
                          />
                        ),
                      });
                    }}
                  >
                    {val.length > 5 ? `...等${val.length}家` : ''}
                  </a>
                </div>
              );
            },
          },
          {
            title: '操作人',
            dataIndex: 'createUserName',
            width: 100,
            search: false,
          },
          {
            title: '操作时间',
            dataIndex: 'updateTime',
            width: 150,
            search: false,
            render: (val: string) => dayjs(val).format('YYYY-MM-DD HH:mm:ss'),
          },
          {
            title: '操作',
            width: 120,
            valueType: 'option',
            render: (_val, record) => [
              <a key="edit" onClick={() => experimentModalRef.openModal(record)}>
                编辑
              </a>,
              <a
                key="delete"
                style={{ color: '#ff4d4f' }}
                onClick={async () => {
                  Modal.confirm({
                    title: '确认删除',
                    content: '是否确认删除实验？',
                    onOk: async () => {
                      try {
                        await removeExperiment({
                          experimentId: record.experimentId,
                          userId: info.shUserId,
                          userName: info.nickName,
                        });
                        actionRef.current?.reload();
                      } catch {}
                    },
                  });
                }}
              >
                删除
              </a>,
            ],
          },
        ]}
        request={async (val) => {
          const { store, current, pageSize, ...rest } = val;
          const params = {
            ...rest,
            platform,
            shopIds: store?.shopIds,
            pageNum: current,
            pageSize: pageSize || 10,
            brandId: getItem('Brand-Id') || import.meta.env.VITE_BRAND_ID,
          };

          const res = await fetchExperimentList(params);

          console.log(res, 'res');

          return {
            data: res?.result,
            success: true,
            total: res?.total,
          };
        }}
        toolBarRender={() => [
          <Button type="primary" key="add" onClick={() => experimentModalRef.openModal()}>
            + 新建模板
          </Button>,
        ]}
        pagination={{
          defaultPageSize: 10,
          showQuickJumper: true,
        }}
      />
      <ExperimentModal {...experimentModalRef} platform={platform} onSuccess={() => actionRef.current?.reload()} />
    </>
  );
};

export default Tab4;
