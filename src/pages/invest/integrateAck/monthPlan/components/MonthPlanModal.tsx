import { useEffect } from 'react';
import { DatePicker, Form, message, Modal, ModalProps, Radio } from 'antd';
import { RangePickerProps } from 'antd/es/date-picker';
import dayjs from 'dayjs';
import Service, { RequestName } from '../../service';

interface MonthPlanModalProps extends ModalProps {
  roleOptions: { label: string; value: number; children: { label: string; value: number }[] }[];
  onSuccess: () => void;
}

const MonthPlanModal: React.FC<MonthPlanModalProps> = ({ open, onSuccess, ...props }) => {
  const [form] = Form.useForm();
  const [_, executeRequest] = Service();

  // 禁用非当月和次月的时间
  const disabledDate: RangePickerProps['disabledDate'] = (current) => {
    const today = dayjs();
    const nextMonth = dayjs().add(1, 'month');

    return current < today.startOf('month') || current > nextMonth.endOf('month');
  };

  const handleSave = async (values: any) => {
    const params = {
      ...values,
      targetMonth: values.targetMonth.format('YYYY-MM'),
    };

    await executeRequest(RequestName.CreateMonthPlan, params);

    message.success('添加成功');
    onSuccess();
  };

  useEffect(() => {
    return () => {
      form.resetFields();
    };
  }, [form, open]);

  return (
    <Modal open={open} forceRender width={500} title="生成投流计划" onOk={form.submit} {...props}>
      <Form className="p-4" form={form} labelCol={{ span: 5 }} labelAlign="left" onFinish={handleSave}>
        <Form.Item label="月份选择" name="targetMonth" rules={[{ required: true, message: '请选择月份' }]}>
          <DatePicker picker="month" disabledDate={disabledDate} />
        </Form.Item>
        <Form.Item label="平台选择" name="platform" rules={[{ required: true, message: '请选择平台' }]}>
          <Radio.Group
            options={[
              {
                label: '美团',
                value: 0,
              },
              {
                label: '饿了么',
                value: 1,
              },
            ]}
          />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default MonthPlanModal;
