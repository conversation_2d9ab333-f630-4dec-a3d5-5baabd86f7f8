import { FC, useRef, useState } from 'react';
import { PlusOutlined } from '@ant-design/icons';
import { ActionType, ProCard, ProTable } from '@ant-design/pro-components';
import { Button, Space } from 'antd';
import dayjs from 'dayjs';
import MonthPlanModal from './components/MonthPlanModal';
import Service, { RequestName } from '../service';
import { renderPage } from '@/utils/render';

const MonthPlan: FC = () => {
  const [service, executeRequest] = Service();

  const actionRef: any = useRef<ActionType>();

  const [monthPlanModal, setMonthPlanModal] = useState<boolean>(false);

  return (
    <>
      <ProTable
        actionRef={actionRef}
        search={false}
        options={false}
        tableRender={(_p, _d, domList) => {
          return [
            <ProCard key="">
              <Space className="flex mb-4 justify-end">
                <Button
                  type="primary"
                  onClick={() => {
                    setMonthPlanModal(true);
                  }}
                  icon={<PlusOutlined />}
                >
                  生成计划
                </Button>
              </Space>
              {domList.toolbar}
              {domList.table}
            </ProCard>,
          ];
        }}
        columns={[
          {
            title: '月份',
            dataIndex: 'targetMonth',
            fixed: 'left',
            width: 60,
            render: (val: string) => dayjs(val, 'YYYY-MM').format('YYYY年M月'),
          },
          {
            title: '平台',
            dataIndex: 'platformName',
            width: 60,
          },

          {
            title: '生成状态',
            dataIndex: 'stateName',
            width: 60,
          },
          {
            title: '生成结果',
            dataIndex: 'resultSummary',
            width: 200,
          },

          {
            title: '操作人',
            dataIndex: 'creatorName',
            width: 80,
          },
          {
            title: '创建时间',
            dataIndex: 'createTime',
            width: 100,
            render: (_, record) => (record?.createTime ? dayjs(record?.createTime).format('YYYY-MM-DD HH:mm:ss') : '-'),
          },
        ]}
        request={async (params) => {
          const res = await executeRequest(RequestName.QueryMonthPlanList, params);

          return {
            data: res?.result || [],
            success: true,
          };
        }}
      />
      <MonthPlanModal
        open={monthPlanModal}
        onCancel={() => {
          setMonthPlanModal(false);
        }}
        roleOptions={service?.roleOptions}
        onSuccess={() => {
          setMonthPlanModal(false);
          actionRef.current?.reload();
        }}
      />
    </>
  );
};

export default renderPage(MonthPlan);
