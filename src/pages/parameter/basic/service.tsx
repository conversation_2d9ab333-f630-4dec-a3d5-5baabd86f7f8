import { StoreStatus, StoreType } from '@/constants/organization';
import useServiceReducer from '@/hooks/use-service-reducer';
import HttpAop from '@/http/aop';
import { getStoreList, getUserInfoList, getUserInfoWithOrg } from '@/http/apis/center-control';
import { calcDefaultWeight, getRolesByChecklist, getSimpleChecklistList } from '@/http/apis/checklist';
import { getRoleClassifyList } from '@/http/apis/role';
import { getOMAllTreeList } from '@/http/apis/store';
import {
  getDisinfectionSystemConfig,
  getGuaranteeRole,
  getPatrolTransferConfig,
  getRecipientConfig,
  getSystemConfig,
  saveDisinfectionSystemConfig,
  savePatrolTransferConfig,
  saveRecipientConfig,
  saveSystemConfig,
} from '@/http/apis/system';
import { getPermissionTagSelectList, getTags } from '@/http/apis/tag';
import { getRoutineTaskListName } from '@/http/apis/task';
import { formatChecklistToOptions, formatRoleToOptions, formatShopsToOptions, formatToOptions } from '@/utils/format';

export enum RequestName {
  GetSystemConfig,
  SaveSystemConfig,
  GetPatrolTransferConfig,
  SavePatrolTransferConfig,
  GetTags,
  GetFirstSimpleChecklistList,
  GetSecondSimpleChecklistList,
  SaveDisinfectionSystemConfig,
  GetDisinfectionSystemConfig,
  GetRoleCascaderData,
  GetSimpleChecklistList,
  GetRolesByChecklist,
  CalcDefaultWeight,
  GetPermissionTagList,
  GetRoutineTaskListName,
  GetUserInfoWithOrg,
  GetRecipientConfig,
  SaveRecipientConfig,
  GetStoreList,
  GetUserInfoList,
  GetGroupTreeList,
  GetOMAllTreeList,
  GetGuaranteeRole,
}

export const initState: any = {
  config: {},
  permissionTagOptions: [],
};

const Service = () => {
  const [service, executeRequest, dispatch] = useServiceReducer(initState, {
    [RequestName.GetSystemConfig]: {
      request: getSystemConfig,
    },
    [RequestName.SaveSystemConfig]: {
      request: saveSystemConfig,
    },
    [RequestName.GetPatrolTransferConfig]: {
      request: getPatrolTransferConfig,
    },
    [RequestName.SavePatrolTransferConfig]: {
      request: savePatrolTransferConfig,
    },
    [RequestName.GetDisinfectionSystemConfig]: {
      request: getDisinfectionSystemConfig,
      afterRequest: (data, dispatch) => {
        dispatch({ disinfectionConfig: data });
      },
    },
    [RequestName.SaveDisinfectionSystemConfig]: {
      request: saveDisinfectionSystemConfig,
    },
    [RequestName.GetTags]: {
      request: HttpAop(getTags, {
        after: [formatToOptions],
      }),
      afterRequest: (data, dispatch) => {
        dispatch({ tagOptions: data });
      },
    },
    [RequestName.GetFirstSimpleChecklistList]: {
      request: HttpAop(getSimpleChecklistList, {
        after: [formatChecklistToOptions],
      }),
      afterRequest: (data, dispatch) => {
        dispatch({ firstChecklistOptions: data });
      },
    },
    [RequestName.GetSecondSimpleChecklistList]: {
      request: HttpAop(getSimpleChecklistList, {
        after: [formatChecklistToOptions],
      }),
      afterRequest: (data, dispatch) => {
        dispatch({ secondChecklistOptions: data });
      },
    },
    [RequestName.GetRoleCascaderData]: {
      request: HttpAop(getRoleClassifyList, {
        after: [formatRoleToOptions],
      }),
      afterRequest(data, dispatch) {
        dispatch({ roleOptions: data });
      },
    },
    [RequestName.GetSimpleChecklistList]: {
      request: HttpAop(getSimpleChecklistList, {
        after: [formatChecklistToOptions],
      }),
    },
    [RequestName.GetRolesByChecklist]: {
      request: HttpAop(getRolesByChecklist, {
        after: [
          (data: any) => {
            return Promise.resolve(
              data?.map(({ roleClassifyName, roleClassifyId, roles }) => ({
                value: roleClassifyId,
                label: roleClassifyName,
                children: roles?.map(({ roleId, roleName }) => ({ value: roleId, label: roleName })),
              })),
            );
          },
        ],
      }),
    },
    [RequestName.CalcDefaultWeight]: {
      request: HttpAop(calcDefaultWeight, {
        after: [
          (data) => {
            const weightMap = {};

            data.forEach(({ worksheetId, weight }) => {
              weightMap[worksheetId] = weight;
            });

            return Promise.resolve(weightMap);
          },
        ],
      }),
    },
    [RequestName.GetPermissionTagList]: {
      request: HttpAop(getPermissionTagSelectList, {
        after: [formatToOptions],
      }),
      afterRequest: (data, dispatch) => {
        dispatch({ permissionTagOptions: data });
      },
    },
    [RequestName.GetRoutineTaskListName]: {
      request: HttpAop(getRoutineTaskListName, {
        after: [formatToOptions],
      }),
      afterRequest: (data, dispatch) => {
        dispatch({ taskOptions2: data });
      },
    },
    [RequestName.GetUserInfoWithOrg]: {
      request: getUserInfoWithOrg,
    },
    [RequestName.GetRecipientConfig]: {
      request: getRecipientConfig,
    },
    [RequestName.SaveRecipientConfig]: {
      request: saveRecipientConfig,
    },
    [RequestName.GetStoreList]: {
      request: HttpAop(getStoreList, {
        after: [formatShopsToOptions],
      }),
      afterRequest: (data, dispatch) => {
        dispatch({ storeOptions: data });
      },
    },
    [RequestName.GetUserInfoList]: {
      beforeRequest: (dispatch) => {
        dispatch({ userLoading: true });
      },
      request: HttpAop(getUserInfoList),
      afterRequest: (data: any[], dispatch) => {
        dispatch({
          userLoading: false,
          personOptions: data?.map((m) => ({
            label: `${m?.nickName ?? ''} (${m?.staffCode ? `${m.staffCode} - ` : ''}${m?.mobile ?? ''})`,
            value: m?.userId,
          })),
        });
      },
    },
    /* [RequestName.GetGroupTreeList]: {
      request: HttpAop(getGroupTreeList, { after: [formatOrganizationTreeToOptions] }),
      afterRequest: (data, dispatch) => {
        dispatch({ organizationOptions: data });
      },
    }, */
    [RequestName.GetOMAllTreeList]: {
      request: HttpAop(getOMAllTreeList, {
        after: [
          (data) => {
            const loop = (nodes) => {
              return nodes.map(({ type, name, id, treeSet, shopType, shopBusinessStatus }) => {
                const value = `${type}-${id}`;
                const label = type === 'SHOP' ? `${name} (${id})` : name;

                return {
                  value,
                  key: value,
                  title: label,
                  label,
                  children: loop(treeSet),
                  shopType: shopType && StoreType[shopType],
                  shopStatus: shopBusinessStatus && StoreStatus[shopBusinessStatus],
                };
              });
            };

            return Promise.resolve(loop([data]));
          },
        ],
      }),
      afterRequest: (data, dispatch) => {
        dispatch({ organizationOptions: data });
      },
    },
    [RequestName.GetGuaranteeRole]: {
      request: HttpAop(getGuaranteeRole, {
        after: [
          (data) => {
            return Promise.resolve(
              data?.map((options: any[]) => {
                return options?.map(({ key, name }) => ({ value: key, label: name || key }));
              }),
            );
          },
        ],
      }),
      afterRequest: (data, dispatch) => {
        dispatch({ guaranteeRole: data });
      },
    },
  });

  return [service, executeRequest, dispatch];
};

export default Service;
