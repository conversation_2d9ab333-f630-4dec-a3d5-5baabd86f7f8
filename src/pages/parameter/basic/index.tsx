import { FC, useEffect, useMemo, useRef, useState } from 'react';
/* eslint-disable max-lines-per-function */
import { ProForm, ProFormDigit } from '@ant-design/pro-components';
import { Button, Card, Divider, message, Select } from 'antd';
import { useForm } from 'antd/es/form/Form';
import FormItem from 'antd/es/form/FormItem';
import { useSearchParams } from 'react-router-dom';
import AuditSetupCard from './components/audit-setup-card';
import BasicSetupCard from './components/basic-setup-card';
import CcObjectCard from './components/cc-object-card';
import CloudSetupCard from './components/cloud-setup-card';
import DiagnosisSetupCard from './components/diagnosis-setup-card';
import TaskSetupCard from './components/diagnosis-task-setup-card';
import DisinfectionSetupCard from './components/disinfection-setup-card';
import GrayScaleCard from './components/grayscale-configuration-card';
import GuaranteeCard from './components/guarantee-card';
import InspectionSetupCard from './components/inspection-setup-card';
import OperateButton from './components/operate-button';
import PatrolTransferTable from './components/patrolTransfer';
import ReportSetupCard from './components/report-setup-card';
import SelfSetupCard from './components/self-setup-card';
import Service, { RequestName } from './service';
import ItemCard from '@/components/item-card';
import { ChecklistType } from '@/constants/checklist';
import useUserListCaching from '@/hooks/use-user-list-caching';
import { DisinfectionSystemConfigData, DisinfectionSystemConfigParams } from '@/http/apis/system';

const diagnosisConfigKeys = [
  'DIAGNOSTIC_TASK_ORANGE_INSPECTOR',
  'DIAGNOSTIC_TASK_RED_INSPECTOR',
  'DIAGNOSTIC_TASK_YELLOW_INSPECTOR',
  'DIAGNOSTIC_TASK_ORANGE_TRANSFER_PROCESSOR',
  'DIAGNOSTIC_TASK_RED_TRANSFER_PROCESSOR',
  'DIAGNOSTIC_TASK_YELLOW_TRANSFER_PROCESSOR',
];

// eslint-disable-next-line max-lines-per-function
const SystemSettings: FC = () => {
  const [form] = useForm();
  const [diagnosisForm] = useForm();
  const [shopCoachForm] = useForm();
  const [disinfectionForm] = useForm();
  const [errorTipForm] = useForm();
  const [transferForm] = useForm();
  const [ccObjectForm] = useForm();
  const [grayscaleFrom] = useForm();
  const [searchParams] = useSearchParams();
  const emergencyDisinfectionRef = useRef(null);
  // 获取数据后的映射缓存，主要是为了获取对应项的id
  const [recipientMap, setRecipientMap] = useState({});
  const [auditForm] = useForm();
  const [differentItemTaskForm] = useForm();
  const [service, executeRequest] = Service();

  const [editing, setEditing] = useState<{
    basic: boolean;
    disinfection: boolean;
    diagnosis: boolean;
    errorTip: boolean;
    audit: boolean;
    transferSet: boolean;
    shopCoach: boolean;
    ccObject: boolean;
    grayscale: boolean;
    differentItemTaskForm: boolean;
  }>({
    basic: false,
    disinfection: false,
    diagnosis: false,
    errorTip: false,
    audit: false,
    transferSet: false,
    shopCoach: false,
    ccObject: false,
    grayscale: false,
    differentItemTaskForm: false,
  });

  const request = () => {
    executeRequest(RequestName.GetSystemConfig).then((res) => {
      const data = {};

      res.forEach(
        ({ key, value }) =>
          (data[key] =
            diagnosisConfigKeys.includes(key) ||
            [
              'SELF_INSPECTION_RECTIFY_AUDIT_DEFAULT_DURATION',
              'FOOD_SAFETY_ARRIVE_SHOP_TASK_REPORT_RECTIFY_DEFAULT_DURATION',
              'FOOD_SAFETY_ARRIVE_SHOP_TASK_REPORT_RECTIFY_REJECT_AFTER_DEFAULT_DURATION',
              'IN_STORE_INSPECTION_RECTIFY_AUDIT_DEFAULT_DURATION',
              'REPORT_VIDEO_RECTIFY_AUDIT_DEFAULT_DURATION',
              'FOOD_SAFETY_ARRIVE_SHOP_TASK_REPORT_RECTIFY_AUDIT_DEFAULT_DURATION',
            ].includes(key)
              ? JSON.parse(value)
              : value),
      );

      const formatFirstReview = JSON.parse(data['DIAGNOSTIC_TASK_REPORT_FIRST_REVIEW_DURATION']);
      const FOOD_SAFETY_ARRIVE_SHOP_TASK_TRANSFER_FLOW_CONFIG = data[
        'FOOD_SAFETY_ARRIVE_SHOP_TASK_TRANSFER_FLOW_CONFIG'
      ]
        ? JSON.parse(data['FOOD_SAFETY_ARRIVE_SHOP_TASK_TRANSFER_FLOW_CONFIG']).map((roles) => ({ roles }))
        : [{ roles: [] }, { roles: [] }, { roles: [] }];

      form.setFieldsValue({
        ...data,
        reportAutoConfirm: data['REPORT_AUTO_CONFIRM_DAYS'] > 0,
        REPORT_VIDEO_TIME_LIMIT_CHECK: !!+data['REPORT_VIDEO_TIME_LIMIT_CHECK'],
        REPORT_VIDEO_TIME_LIMIT: data['REPORT_VIDEO_TIME_LIMIT'] / 60,

        DIAGNOSTIC_TASK_REPORT_REVIEW_PASS_SCORE: data['DIAGNOSTIC_TASK_REPORT_REVIEW_PASS_SCORE'],
        enabled: formatFirstReview?.enabled,
        timeInterval: formatFirstReview?.timeInterval,
        timeUnit: formatFirstReview?.timeUnit,
        FOOD_SAFETY_ARRIVE_SHOP_TASK_TRANSFER_FLOW_CONFIG,
        SELF_INSPECTION_RECTIFY_AUDIT_DEFAULT_DURATION:
          data['SELF_INSPECTION_RECTIFY_AUDIT_DEFAULT_DURATION']?.timeInterval,
        FOOD_SAFETY_ARRIVE_SHOP_TASK_REPORT_RECTIFY_DEFAULT_DURATION:
          data['FOOD_SAFETY_ARRIVE_SHOP_TASK_REPORT_RECTIFY_DEFAULT_DURATION']?.timeInterval,
        FOOD_SAFETY_ARRIVE_SHOP_TASK_REPORT_RECTIFY_REJECT_AFTER_DEFAULT_DURATION:
          data['FOOD_SAFETY_ARRIVE_SHOP_TASK_REPORT_RECTIFY_REJECT_AFTER_DEFAULT_DURATION']?.timeInterval,
        IN_STORE_INSPECTION_RECTIFY_AUDIT_DEFAULT_DURATION:
          data['IN_STORE_INSPECTION_RECTIFY_AUDIT_DEFAULT_DURATION']?.timeInterval,
        REPORT_VIDEO_RECTIFY_AUDIT_DEFAULT_DURATION: data['REPORT_VIDEO_RECTIFY_AUDIT_DEFAULT_DURATION']?.timeInterval,
        FOOD_SAFETY_ARRIVE_SHOP_TASK_REPORT_RECTIFY_AUDIT_DEFAULT_DURATION:
          data['FOOD_SAFETY_ARRIVE_SHOP_TASK_REPORT_RECTIFY_AUDIT_DEFAULT_DURATION']?.timeInterval,
      });

      // 如果有用户id数据  则需要请求用户列表
      if (JSON.parse(data?.['GRAY_USER_NEW'])?.length > 0) {
        executeRequest(RequestName.GetUserInfoList);
      }

      grayscaleFrom.setFieldsValue({
        GRAY_SHOPS: JSON.parse(data?.['GRAY_SHOPS'] || '[]'),
        // GRAY_USER: JSON.parse(data?.['GRAY_USER'] || '[]'),
        GRAY_USER_NEW: JSON.parse(data?.['GRAY_USER_NEW'] || '[]'),
        GRAY_GROUP: JSON.parse(data?.['GRAY_GROUP'] || '[]'),
      });

      const _normalInspectionTask = JSON.parse(data?.['DIAGNOSTIC_TASK_REF_ROUTINE_PATROL_PLAN'] || '[]').length
        ? JSON.parse(data?.['DIAGNOSTIC_TASK_REF_ROUTINE_PATROL_PLAN']).map(({ name, value }) => ({
            taskName: {
              label: name,
              value,
            },
          }))
        : [{}];

      diagnosisForm.setFieldsValue({
        DIAGNOSTIC_TASK_REPORT_REVIEW_PASS_SCORE: data['DIAGNOSTIC_TASK_REPORT_REVIEW_PASS_SCORE'],
        enabled: formatFirstReview?.enabled,
        timeInterval: formatFirstReview?.timeInterval,
        timeUnit: formatFirstReview?.timeUnit,
        PATROL_DIAGNOSTIC_TASK_PERMISSION_LABELS: JSON.parse(data['PATROL_DIAGNOSTIC_TASK_PERMISSION_LABELS']),

        associatedInspectionTask: data?.['DIAGNOSTIC_TASK_REF_ROUTINE_PATROL_PLAN_ENABLE'] === 'true',
        normalInspectionTask: _normalInspectionTask,
        normalInspectionTaskEffectCondition:
          data?.['DIAGNOSTIC_TASK_REF_ROUTINE_PATROL_PLAN_GENERATING_CONDITION'] === 'true',
        DIAGNOSTIC_TASK_REF_ROUTINE_PATROL_PLAN_GENERATING_CONDITION_BEFORE_MONTH:
          +data?.['DIAGNOSTIC_TASK_REF_ROUTINE_PATROL_PLAN_GENERATING_CONDITION_BEFORE_MONTH'] || undefined,
        DIAGNOSTIC_TASK_REF_ROUTINE_PATROL_PLAN_GENERATING_CONDITION_AFTER_TASK_GENERATION:
          +data?.['DIAGNOSTIC_TASK_REF_ROUTINE_PATROL_PLAN_GENERATING_CONDITION_AFTER_TASK_GENERATION'] || undefined,
        firstReviewer: !!JSON.parse(data?.['DIAGNOSTIC_TASK_REVIEWER_ROLE'] || '[]').length,
        usePermissionIds: JSON.parse(data?.['DIAGNOSTIC_TASK_REVIEWER_ROLE'] || '[]'),
      });

      shopCoachForm.setFieldsValue({
        FOOD_SAFETY_ARRIVE_SHOP_PERMISSION_LABELS: JSON.parse(data['FOOD_SAFETY_ARRIVE_SHOP_PERMISSION_LABELS']),
      });
      errorTipForm.setFieldsValue({
        VIDEO_MESSAGE_SERVICE_PROVIDER: data['VIDEO_MESSAGE_SERVICE_PROVIDER'],
        VIDEO_MESSAGE_AFTER_SALE: data['VIDEO_MESSAGE_AFTER_SALE'],
        VIDEO_MESSAGE_BACKGROUND: data['VIDEO_MESSAGE_BACKGROUND'],
        VIDEO_MESSAGE_OTHER: data['VIDEO_MESSAGE_OTHER'],
      });

      auditForm?.setFieldsValue({
        FOOD_SAFETY_ARRIVE_SHOP_TASK_REPORT_REVIEW_PASS_SCORE:
          data['FOOD_SAFETY_ARRIVE_SHOP_TASK_REPORT_REVIEW_PASS_SCORE'],
        roleEnabled: !!data['FOOD_SAFETY_ARRIVE_SHOP_TASK_REPORT_FIRST_REVIEW_ROLE'],
        FOOD_SAFETY_ARRIVE_SHOP_TASK_REPORT_FIRST_REVIEW_ROLE:
          data['FOOD_SAFETY_ARRIVE_SHOP_TASK_REPORT_FIRST_REVIEW_ROLE'],
        ...JSON.parse(data['FOOD_SAFETY_ARRIVE_SHOP_TASK_REPORT_FIRST_REVIEW_DURATION']),
      });

      differentItemTaskForm?.setFieldsValue({
        DIFFERENCE_ITEM_REJECT_TIME: data['DIFFERENCE_ITEM_REJECT_TIME'],
      });
    });
  };
  const requestTransfer = () => {
    executeRequest(RequestName.GetPatrolTransferConfig).then((res) => {
      transferForm?.setFieldsValue({ data: res?.length === 0 ? [{}] : res });
    });
  };

  const requestDisinfection = () => {
    executeRequest(RequestName.GetDisinfectionSystemConfig).then(
      (res: DisinfectionSystemConfigData & { exigencyDisinfectionWorksheetConfig: any[] }) => {
        const {
          suspendResonList,
          bpmInfo,
          firstTaskWorksheetId,
          secondTaskWorksheetId,
          exigencyDisinfectionWorksheetConfig,
          ...rest
        } = res;

        disinfectionForm?.setFieldsValue({
          ...rest,
          bpmId: bpmInfo?.id,
          firstTaskWorksheetId: { checklist: firstTaskWorksheetId?.id },
          secondTaskWorksheetId: { checklist: secondTaskWorksheetId?.id },
          suspendResonList: suspendResonList?.map((reason: string) => ({
            reason,
          })),
          exigencyDisinfectionWorksheetConfig: !!exigencyDisinfectionWorksheetConfig?.length
            ? (exigencyDisinfectionWorksheetConfig || []).map((item) => ({
                ...item,
                workSheetId: item?.worksheetId,
                required: !!item.reviewerRole,
                reviewTimeIntervalRequired: !!item?.reviewTimeInterval,
              }))
            : [{}],
        });
      },
    );
  };

  const requestRecipient = () => {
    executeRequest(RequestName.GetRecipientConfig).then((data) => {
      const map = {};

      data?.forEach(({ sysConfigKeyEnum, recipientIds, id }) => {
        map[sysConfigKeyEnum] = { id, existeds: recipientIds };

        ccObjectForm?.setFieldValue(
          sysConfigKeyEnum,
          recipientIds?.map(({ userId }) => ({ key: userId, userId, value: userId })),
        );
      });
      setRecipientMap(map);
    });
  };
  const requestFirstChecklist = (labelIds?: any) => {
    return executeRequest(RequestName.GetFirstSimpleChecklistList, {
      labelIds: labelIds || [],
      planType: ChecklistType.DISINFECTION,
    });
  };
  const requestSecondChecklist = (labelIds?: any) => {
    return executeRequest(RequestName.GetSecondSimpleChecklistList, {
      labelIds: labelIds || [],
      planType: ChecklistType.DISINFECTION,
    });
  };

  const getChecklistOptions = (labelIds?: any) => {
    return executeRequest(RequestName.GetSimpleChecklistList, {
      labelIds: labelIds || [],
      planType: ChecklistType.DISINFECTION,
    });
  };

  const exigencyDisinfectionWorksheetConfig = ProForm.useWatch('exigencyDisinfectionWorksheetConfig', form);
  const disabledDirectSheets = useMemo(() => {
    return exigencyDisinfectionWorksheetConfig?.filter(({ deleted }) => deleted) || [];
  }, [exigencyDisinfectionWorksheetConfig]);

  const requestRoles = (worksheetIds: any) => {
    return executeRequest(RequestName.GetRolesByChecklist, worksheetIds);
  };
  const requestWeights = (worksheetIds: any) => {
    return executeRequest(RequestName.CalcDefaultWeight, worksheetIds);
  };

  const requestUsers = (keyword: string, pageNo: number) => {
    return executeRequest(RequestName.GetUserInfoWithOrg, {
      withPermission: false,
      pageNo,
      pageSize: 50,
      type: 1,
      keyword,
    });
  };

  useEffect(() => {
    executeRequest(RequestName.GetTags, 'WORKSHEET');
    request();
    requestDisinfection();
    requestFirstChecklist();
    requestSecondChecklist();
    requestRecipient();
    executeRequest(RequestName.GetStoreList, {
      groupType: 2,
      privilegeCode: 1,
    });

    executeRequest(RequestName.GetRoleCascaderData, 1);
    requestTransfer();
    executeRequest(RequestName.GetPermissionTagList);
    // executeRequest(RequestName.GetGroupTreeList);
    executeRequest(RequestName.GetOMAllTreeList);

    executeRequest(RequestName.GetGuaranteeRole);
  }, []);

  useEffect(() => {
    if (searchParams.get('activeKey') === 'emergencyDisinfection' && emergencyDisinfectionRef.current) {
      emergencyDisinfectionRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [searchParams]);

  const { userListCachingRef } = useUserListCaching({ options: service?.personOptions });

  return (
    <Card className="tstd">
      <ProForm
        layout="horizontal"
        form={form}
        disabled={!editing?.basic}
        submitter={false}
        onFinish={(values) => {
          console.log(values, '=values');

          const data = Object.keys(values)?.map((key) => {
            if (key === 'REPORT_VIDEO_TIME_LIMIT_CHECK') {
              return { key, value: Number(values[key]).toString() };
            }

            if (key === 'REPORT_VIDEO_TIME_LIMIT') {
              return { key, value: Number(values[key] * 60).toString() };
            }

            if (key === 'reportAutoConfirm' && !values[key]) {
              return { key: 'REPORT_AUTO_CONFIRM_DAYS', value: '0' };
            }

            if (key === 'FOOD_SAFETY_ARRIVE_SHOP_TASK_TRANSFER_FLOW_CONFIG' && values[key]) {
              return {
                key: 'FOOD_SAFETY_ARRIVE_SHOP_TASK_TRANSFER_FLOW_CONFIG',
                value: JSON.stringify(values[key].map(({ roles }) => roles)),
              };
            }

            if (
              [
                'SELF_INSPECTION_RECTIFY_AUDIT_DEFAULT_DURATION',
                'FOOD_SAFETY_ARRIVE_SHOP_TASK_REPORT_RECTIFY_DEFAULT_DURATION',
                'FOOD_SAFETY_ARRIVE_SHOP_TASK_REPORT_RECTIFY_REJECT_AFTER_DEFAULT_DURATION',
                'IN_STORE_INSPECTION_RECTIFY_AUDIT_DEFAULT_DURATION',
                'REPORT_VIDEO_RECTIFY_AUDIT_DEFAULT_DURATION',
                'FOOD_SAFETY_ARRIVE_SHOP_TASK_REPORT_RECTIFY_AUDIT_DEFAULT_DURATION',
              ].includes(key)
            ) {
              return {
                key,
                value: JSON.stringify({
                  timeInterval: values[key],
                  timeUnit: 'Days',
                }),
              };
            }

            return { key, value: diagnosisConfigKeys.includes(key) ? JSON.stringify(values[key]) : values[key] };
          });

          return executeRequest(RequestName.SaveSystemConfig, { data }).then(() => {
            message.success('保存成功');
            request();
            setEditing({ ...editing, basic: false });
          });
        }}
      >
        <BasicSetupCard />
        <SelfSetupCard roleOptions={service?.roleOptions} />
        <InspectionSetupCard form={form} />
        <ReportSetupCard />
        <CloudSetupCard form={form} />
        <DiagnosisSetupCard roleOptions={service?.roleOptions} />
        <AuditSetupCard form={form} disabled={!editing?.basic} roleOptions={service?.roleOptions} />
      </ProForm>
      <OperateButton
        isEdit={editing?.basic}
        onSubmit={() => {
          form?.submit();
        }}
        onCancel={() => {
          setEditing({ ...editing, basic: false });
          request();
        }}
        onEdit={() => setEditing({ ...editing, basic: true })}
      />
      <Divider />
      <div ref={emergencyDisinfectionRef}>
        <ProForm
          layout="horizontal"
          // disabled={!editing?.disinfection}
          form={disinfectionForm}
          submitter={false}
          onFinish={(values) => {
            const {
              bpmId,
              firstTaskWorksheetId,
              secondTaskWorksheetId,
              suspendResonList,
              exigencyDisinfectionWorksheetConfig,
              ...rest
            } = values;

            const params: DisinfectionSystemConfigParams = {
              ...rest,
              bpmId,
              firstTaskWorksheetId: firstTaskWorksheetId?.checklist,
              secondTaskWorksheetId: secondTaskWorksheetId?.checklist,
              suspendResonList: suspendResonList?.filter(({ reason }) => !!reason).map(({ reason }) => reason),
              exigencyDisinfectionWorksheetConfig: exigencyDisinfectionWorksheetConfig.map((item) => {
                return {
                  ...item,
                  worksheetId: item?.workSheetId,
                };
              }),
            };

            return executeRequest(RequestName.SaveDisinfectionSystemConfig, params).then(() => {
              message.success('保存成功');
              requestDisinfection();
              setEditing({ ...editing, disinfection: false });
            });
          }}
        >
          <DisinfectionSetupCard
            bpmInfo={service?.disinfectionConfig?.bpmInfo}
            disabled={!editing?.disinfection}
            firstChecklistOptions={service?.firstChecklistOptions}
            secondChecklistOptions={service?.secondChecklistOptions}
            tagOptions={service?.tagOptions}
            requestFirstChecklist={requestFirstChecklist}
            requestSecondChecklist={requestSecondChecklist}
            form={disinfectionForm}
            reviewerOptions={service?.roleOptions}
            requestChecklists={getChecklistOptions}
            extendChecklistOptions={disabledDirectSheets?.map(({ workSheetId, workSheetName }) => ({
              label: workSheetName,
              value: workSheetId,
            }))}
            requestRoles={requestRoles}
            requestWeights={requestWeights}
          />
        </ProForm>
      </div>
      <OperateButton
        isEdit={editing?.disinfection}
        onSubmit={() => {
          disinfectionForm?.submit();
        }}
        onCancel={() => {
          setEditing({ ...editing, disinfection: false });
          requestDisinfection();
        }}
        onEdit={() => setEditing({ ...editing, disinfection: true })}
      />
      <Divider />

      <ItemCard title="巡检转办设置">
        <ProForm
          submitter={false}
          form={transferForm}
          onFinish={(values) => {
            return executeRequest(RequestName.SavePatrolTransferConfig, { ...values }).then(() => {
              message.success('保存成功');
              requestTransfer();
              setEditing({ ...editing, transferSet: false });
            });
          }}
        >
          <PatrolTransferTable
            form={transferForm}
            name="data"
            roleOptions={service?.roleOptions || []}
            disabled={!editing?.transferSet}
          />
        </ProForm>
      </ItemCard>
      <div>
        {editing?.transferSet ? (
          <>
            <Button
              type="primary"
              className="mr-4"
              onClick={() => {
                transferForm?.submit();
              }}
            >
              提交
            </Button>
            <Button
              onClick={() => {
                setEditing({ ...editing, transferSet: false });
              }}
            >
              取消
            </Button>
          </>
        ) : (
          <Button type="primary" onClick={() => setEditing({ ...editing, transferSet: true })}>
            编辑
          </Button>
        )}
      </div>
      <Divider />
      <GuaranteeCard values={service?.guaranteeRole} />
      <Divider />

      <ProForm
        layout="horizontal"
        submitter={false}
        disabled={!editing?.diagnosis}
        form={diagnosisForm}
        onFinish={(values) => {
          const data = [
            {
              key: 'DIAGNOSTIC_TASK_REPORT_REVIEW_PASS_SCORE',
              value: values?.DIAGNOSTIC_TASK_REPORT_REVIEW_PASS_SCORE,
            },
            {
              key: 'DIAGNOSTIC_TASK_REPORT_FIRST_REVIEW_DURATION',
              value: JSON.stringify({
                enabled: values?.enabled,
                timeInterval: values?.enabled ? values?.timeInterval : null,
                timeUnit: values?.enabled ? values?.timeUnit : null,
              }),
            },
            {
              key: 'PATROL_DIAGNOSTIC_TASK_PERMISSION_LABELS',
              value: JSON.stringify(values?.PATROL_DIAGNOSTIC_TASK_PERMISSION_LABELS),
            },
            {
              key: 'DIAGNOSTIC_TASK_REF_ROUTINE_PATROL_PLAN_ENABLE',
              value: values?.associatedInspectionTask,
            },
            {
              key: 'DIAGNOSTIC_TASK_REF_ROUTINE_PATROL_PLAN',
              value: values?.associatedInspectionTask
                ? JSON.stringify(
                    values?.normalInspectionTask?.map(({ taskName }) => {
                      return {
                        name: taskName?.label,
                        value: taskName?.value,
                      };
                    }),
                  )
                : '[]',
            },
            {
              key: 'DIAGNOSTIC_TASK_REF_ROUTINE_PATROL_PLAN_GENERATING_CONDITION',
              value: values?.normalInspectionTaskEffectCondition,
            },
            {
              key: 'DIAGNOSTIC_TASK_REF_ROUTINE_PATROL_PLAN_GENERATING_CONDITION_BEFORE_MONTH',
              value: +values?.DIAGNOSTIC_TASK_REF_ROUTINE_PATROL_PLAN_GENERATING_CONDITION_BEFORE_MONTH || 0,
            },
            {
              key: 'DIAGNOSTIC_TASK_REF_ROUTINE_PATROL_PLAN_GENERATING_CONDITION_AFTER_TASK_GENERATION',
              value: +values?.DIAGNOSTIC_TASK_REF_ROUTINE_PATROL_PLAN_GENERATING_CONDITION_AFTER_TASK_GENERATION || 0,
            },
            {
              key: 'DIAGNOSTIC_TASK_REVIEWER_ROLE',
              value: values?.firstReviewer ? JSON.stringify(values?.usePermissionIds) : '[]',
            },
          ];

          return executeRequest(RequestName.SaveSystemConfig, { data }).then(() => {
            message.success('保存成功');
            request();
            setEditing({ ...editing, diagnosis: false });
          });
        }}
      >
        <TaskSetupCard
          cardTitle="诊断任务设置"
          switchName="enabled"
          intervalName="timeInterval"
          unitName="timeUnit"
          scoreName="DIAGNOSTIC_TASK_REPORT_REVIEW_PASS_SCORE"
          permissionTag="PATROL_DIAGNOSTIC_TASK_PERMISSION_LABELS"
          permissionTagOptions={service?.permissionTagOptions}
          form={diagnosisForm}
          associatedInspectionTask={true}
          disabled={!editing?.diagnosis}
        />
      </ProForm>
      <OperateButton
        isEdit={editing?.diagnosis}
        onSubmit={() => {
          diagnosisForm?.submit();
        }}
        onCancel={() => {
          request();
          requestDisinfection();
          setEditing({ ...editing, diagnosis: false });
        }}
        onEdit={() => setEditing({ ...editing, diagnosis: true })}
      />
      <Divider />

      <ItemCard title="到店辅导任务设置">
        <ProForm
          layout="horizontal"
          form={shopCoachForm}
          disabled={!editing?.shopCoach}
          submitter={false}
          onFinish={(values) => {
            const data = [
              {
                key: 'FOOD_SAFETY_ARRIVE_SHOP_PERMISSION_LABELS',
                value: JSON.stringify(values?.FOOD_SAFETY_ARRIVE_SHOP_PERMISSION_LABELS),
              },
            ];

            return executeRequest(RequestName.SaveSystemConfig, { data }).then(() => {
              message.success('保存成功');
              request();
              setEditing({ ...editing, shopCoach: false });
            });
          }}
        >
          <FormItem
            name="FOOD_SAFETY_ARRIVE_SHOP_PERMISSION_LABELS"
            label="权限标签"
            rules={[{ required: true, message: '请选择权限标签' }]}
          >
            <Select
              disabled={!editing.shopCoach}
              style={{ width: 400 }}
              mode="multiple"
              filterOption={(input, option) => (option.label as string)?.includes(input)}
              placeholder="请选择权限标签"
              onChange={(_value, option) => {
                const permissionTags = option?.filter((v) => !!v?.value);

                shopCoachForm?.setFieldValue('FOOD_SAFETY_ARRIVE_SHOP_PERMISSION_LABELS', permissionTags);
              }}
              options={service?.permissionTagOptions || []}
            />
          </FormItem>
        </ProForm>
      </ItemCard>
      <OperateButton
        isEdit={editing?.shopCoach}
        onSubmit={() => {
          shopCoachForm?.submit();
        }}
        onCancel={() => {
          setEditing({ ...editing, shopCoach: false });
          request();
        }}
        onEdit={() => setEditing({ ...editing, shopCoach: true })}
      />
      <Divider />
      <ProForm
        layout="horizontal"
        submitter={false}
        disabled={!editing?.audit}
        form={auditForm}
        onFinish={(values) => {
          const data = [
            {
              key: 'FOOD_SAFETY_ARRIVE_SHOP_TASK_REPORT_FIRST_REVIEW_ROLE',
              value: values?.FOOD_SAFETY_ARRIVE_SHOP_TASK_REPORT_FIRST_REVIEW_ROLE,
            },
            {
              key: 'FOOD_SAFETY_ARRIVE_SHOP_TASK_REPORT_REVIEW_PASS_SCORE',
              value: values?.FOOD_SAFETY_ARRIVE_SHOP_TASK_REPORT_REVIEW_PASS_SCORE,
            },
            {
              key: 'FOOD_SAFETY_ARRIVE_SHOP_TASK_REPORT_FIRST_REVIEW_DURATION',
              value: JSON.stringify({
                enabled: values?.enabled,
                timeInterval: values?.enabled ? values?.timeInterval : null,
                timeUnit: values?.enabled ? values?.timeUnit : null,
              }),
            },
          ];

          return executeRequest(RequestName.SaveSystemConfig, { data }).then(() => {
            message.success('保存成功');
            request();
            setEditing({ ...editing, audit: false });
          });
        }}
      >
        <TaskSetupCard
          cardTitle="食安稽核到店辅导任务设置"
          switchName="enabled"
          intervalName="timeInterval"
          unitName="timeUnit"
          scoreName="FOOD_SAFETY_ARRIVE_SHOP_TASK_REPORT_REVIEW_PASS_SCORE"
          form={auditForm}
          roleOptions={service?.roleOptions}
          roleName="FOOD_SAFETY_ARRIVE_SHOP_TASK_REPORT_FIRST_REVIEW_ROLE"
          roleSwitchName="roleEnabled"
        />
      </ProForm>
      <OperateButton
        isEdit={editing?.audit}
        onSubmit={() => {
          auditForm?.submit();
        }}
        onCancel={() => {
          setEditing({ ...editing, audit: false });
        }}
        onEdit={() => setEditing({ ...editing, audit: true })}
      />
      <Divider />
      <ItemCard title="差异项到店任务设置">
        <ProForm
          layout="horizontal"
          submitter={false}
          disabled={!editing?.differentItemTaskForm}
          form={differentItemTaskForm}
          onFinish={(values) => {
            const data = [
              {
                key: 'DIFFERENCE_ITEM_REJECT_TIME',
                value: values?.DIFFERENCE_ITEM_REJECT_TIME,
              },
            ];

            return executeRequest(RequestName.SaveSystemConfig, { data }).then(() => {
              message.success('保存成功');
              request();
              setEditing({ ...editing, differentItemTaskForm: false });
            });
          }}
        >
          <ProFormDigit
            label="差异项驳回"
            name="DIFFERENCE_ITEM_REJECT_TIME"
            addonAfter="次后触发到店任务"
            placeholder="请输入正整数"
            fieldProps={{ precision: 0, min: 1, max: 100 }}
            rules={[{ required: true, message: '请输入正整数' }]}
          />
        </ProForm>
      </ItemCard>
      <OperateButton
        isEdit={editing?.differentItemTaskForm}
        onSubmit={() => {
          differentItemTaskForm?.submit();
        }}
        onCancel={() => {
          setEditing({ ...editing, differentItemTaskForm: false });
        }}
        onEdit={() => setEditing({ ...editing, differentItemTaskForm: true })}
      />
      <Divider />

      <CcObjectCard
        requestOptions={requestUsers}
        form={ccObjectForm}
        disabled={!editing.ccObject}
        recipientMap={recipientMap}
        onFinish={(values) => {
          const data = Object.keys(values)?.map((key: string) => {
            const { id } = recipientMap[key];
            const recipientIds = values[key];

            return { id, sysConfigKeyEnum: key, recipientIds: recipientIds?.map(({ userId }) => ({ userId })) };
          });

          return executeRequest(RequestName.SaveRecipientConfig, data).then(() => {
            message.success('保存成功');
            requestRecipient();
            setEditing({ ...editing, ccObject: false });
          });
        }}
      />
      <OperateButton
        isEdit={editing?.ccObject}
        onSubmit={() => {
          ccObjectForm?.submit();
        }}
        onCancel={() => {
          // 取消的时候也重新请求接口，重置数据
          requestRecipient();
          setEditing({ ...editing, ccObject: false });
        }}
        onEdit={() => setEditing({ ...editing, ccObject: true })}
      />
      <Divider />
      <GrayScaleCard
        form={grayscaleFrom}
        disabled={!editing.grayscale}
        userOptions={userListCachingRef?.current || service?.personOptions}
        onUserFocus={() => {
          if (!userListCachingRef?.current) {
            executeRequest(RequestName.GetUserInfoList);
          }
        }}
        userLoading={service?.userLoading}
        storeOptions={service?.storeOptions}
        organizationOptions={service?.organizationOptions}
        onFinish={(values) => {
          const data = Object.keys(values)?.map((key: string) => {
            const recipientIds = values[key];

            if (key === 'GRAY_GROUP') {
              const res: number[] = recipientIds?.map((m) => (m?.value ? +m.value?.split('-')[1] : m));

              return { key, value: JSON.stringify(res) };
            }

            return { key, value: JSON.stringify(recipientIds) };
          });

          return executeRequest(RequestName.SaveSystemConfig, { data }).then(() => {
            message.success('保存成功');
            request();
            setEditing({ ...editing, grayscale: false });
          });
        }}
      />
      <OperateButton
        isEdit={editing?.grayscale}
        onSubmit={() => {
          grayscaleFrom?.submit();
        }}
        onCancel={() => {
          // 取消的时候也重新请求接口，重置数据
          requestRecipient();
          setEditing({ ...editing, grayscale: false });
        }}
        onEdit={() => setEditing({ ...editing, grayscale: true })}
      />
    </Card>
  );
};

export default SystemSettings;
