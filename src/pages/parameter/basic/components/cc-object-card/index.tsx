import { FC } from 'react';
import { ProForm, ProFormInstance, ProFormProps } from '@ant-design/pro-components';
import DebounceSelect, { DebounceSelectProps } from './debounce-select';
import ItemCard from '@/components/item-card';

export interface CcObjectCardProps {
  requestOptions: DebounceSelectProps['requestOptions'];
  form: ProFormInstance;
  onFinish?: ProFormProps['onFinish'];
  disabled?: boolean;
  recipientMap?: any; // 后端返回处理过的映射对象
}

const CcObjectCard: FC<CcObjectCardProps> = ({ requestOptions, form, onFinish, disabled, recipientMap }) => {
  return (
    <ItemCard title="加盟商处罚流程抄送对象设置">
      <ProForm layout="horizontal" submitter={false} form={form} onFinish={onFinish} disabled={disabled}>
        <DebounceSelect
          requestOptions={requestOptions}
          label="总部"
          name="FRANCHISEE_PENALTIES_ROUTINE_TASK_MESSAGE_HEADER"
          existeds={recipientMap['FRANCHISEE_PENALTIES_ROUTINE_TASK_MESSAGE_HEADER']?.existeds || []}
        />
        <DebounceSelect
          requestOptions={requestOptions}
          label="南部"
          name="FRANCHISEE_PENALTIES_ROUTINE_TASK_MESSAGE_SOUTH"
          existeds={recipientMap['FRANCHISEE_PENALTIES_ROUTINE_TASK_MESSAGE_SOUTH']?.existeds || []}
        />
        <DebounceSelect
          requestOptions={requestOptions}
          label="东部"
          name="FRANCHISEE_PENALTIES_ROUTINE_TASK_MESSAGE_EAST"
          existeds={recipientMap['FRANCHISEE_PENALTIES_ROUTINE_TASK_MESSAGE_EAST']?.existeds || []}
        />
        <DebounceSelect
          requestOptions={requestOptions}
          label="西部"
          name="FRANCHISEE_PENALTIES_ROUTINE_TASK_MESSAGE_WEST"
          existeds={recipientMap['FRANCHISEE_PENALTIES_ROUTINE_TASK_MESSAGE_WEST']?.existeds || []}
        />
        <DebounceSelect
          requestOptions={requestOptions}
          label="中部"
          name="FRANCHISEE_PENALTIES_ROUTINE_TASK_MESSAGE_MIDDLE"
          existeds={recipientMap['FRANCHISEE_PENALTIES_ROUTINE_TASK_MESSAGE_MIDDLE']?.existeds || []}
        />
        <DebounceSelect
          requestOptions={requestOptions}
          label="北部"
          name="FRANCHISEE_PENALTIES_ROUTINE_TASK_MESSAGE_NORTH"
          existeds={recipientMap['FRANCHISEE_PENALTIES_ROUTINE_TASK_MESSAGE_NORTH']?.existeds || []}
        />
      </ProForm>
    </ItemCard>
  );
};

export default CcObjectCard;
