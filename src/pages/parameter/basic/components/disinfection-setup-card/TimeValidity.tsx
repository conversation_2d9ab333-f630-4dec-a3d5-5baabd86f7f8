import { ProFormItem, ProFormItemProps, ProFormSelect } from '@ant-design/pro-components';
import { InputNumber } from 'antd';

export default function TimeValidity({
  disabled,
  isRequired,
  ...proFormItemProps
}: ProFormItemProps & {
  isRequired?: boolean;
}) {
  const formName = Array.isArray(proFormItemProps.name) ? proFormItemProps.name : [proFormItemProps.name];

  return (
    <div className="flex">
      <ProFormItem
        {...(isRequired ? { rules: [{ required: true, message: '请输入整数' }] } : {})}
        {...proFormItemProps}
        name={[...formName, 'timeInterval']}
      >
        <InputNumber min={0} precision={0} max={99} disabled={disabled} />
      </ProFormItem>
      <ProFormSelect
        initialValue="Hours"
        name={[...formName, 'timeUnit']}
        allowClear={false}
        disabled={disabled}
        options={[
          {
            label: '小时',
            value: 'Hours',
          },
          {
            label: '天',
            value: 'Days',
          },
        ]}
      />
    </div>
  );
}
