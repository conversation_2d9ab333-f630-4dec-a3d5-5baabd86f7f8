import { FC, useState } from 'react';
import { CloseCircleOutlined } from '@ant-design/icons';
import {
  ProForm,
  ProFormInstance,
  ProFormItem,
  ProFormList,
  ProFormSelect,
  ProFormText,
} from '@ant-design/pro-components';
import { But<PERSON>, message } from 'antd';
import FormItem from 'antd/es/form/FormItem';
import TimeValidity from './TimeValidity';
import ChecklistSelect, { ChecklistSelectProps } from '@/components/checklist-select';
import ItemCard from '@/components/item-card';
import RoleCascader from '@/components/role-cascader';
import { ChecklistTable } from '@/pages/task/self/components/checklist-field';

export interface DisinfectionSetupCardProps {
  bpmInfo?: any;
  disabled?: boolean;
  firstChecklistOptions?: ChecklistSelectProps['checklistOptions'];
  secondChecklistOptions?: ChecklistSelectProps['checklistOptions'];
  tagOptions?: ChecklistSelectProps['tagOptions'];
  requestFirstChecklist?: (value: any) => Promise<any>;
  requestSecondChecklist?: (value: any) => Promise<any>;
  form: ProFormInstance;
  reviewerOptions: any;
  requestChecklists: any;
  extendChecklistOptions: any;
  requestRoles: any;
  requestWeights: any;
}

const DisinfectionSetupCard: FC<DisinfectionSetupCardProps> = ({
  bpmInfo,
  disabled,
  firstChecklistOptions,
  secondChecklistOptions,
  tagOptions,
  requestFirstChecklist,
  requestSecondChecklist,
  form,
  reviewerOptions,
  requestChecklists,
  extendChecklistOptions,
  requestRoles,
  requestWeights,
}) => {
  const suspendResonList = ProForm.useWatch('suspendResonList', form);
  const [isShrink, setIsShrink] = useState<boolean>(true); // 是否收缩

  return (
    <>
      <ItemCard title="消杀设置">
        <ProFormSelect
          label="流程模型绑定"
          placeholder="请选择流程标识"
          width="md"
          name="bpmId"
          disabled={true}
          extra={bpmInfo?.deleted && <span className="text-[#ff4d4f]">{`流程模型: ${bpmInfo?.id} 已被删除`}</span>}
        />
        <ProForm.Item
          label="一道结构消杀任务关联检查表"
          name="firstTaskWorksheetId"
          extra="设置成功后，项目经理发起的一道结构消杀任务将自动使用此检查表"
          className="w-[700px]"
          rules={[
            {
              required: true,
              validator: (_, value) => {
                if (!value?.checklist) {
                  return Promise.reject('请选择检查表');
                }

                return Promise.resolve();
              },
            },
          ]}
        >
          <ChecklistSelect
            disabled={disabled}
            checklistOptions={firstChecklistOptions}
            tagOptions={tagOptions}
            onTagChange={(labelIds) => {
              requestFirstChecklist?.(labelIds);
            }}
          />
        </ProForm.Item>
        <ProForm.Item
          label="二道结构消杀任务关联检查表"
          name="secondTaskWorksheetId"
          extra="设置成功后，项目经理发起的二道结构消杀任务将自动使用此检查表"
          className="w-[700px]"
          rules={[
            {
              required: true,
              validator: (_, value) => {
                if (!value?.checklist) {
                  return Promise.reject('请选择检查表');
                }

                return Promise.resolve();
              },
            },
          ]}
        >
          <ChecklistSelect
            disabled={disabled}
            checklistOptions={secondChecklistOptions}
            tagOptions={tagOptions}
            onTagChange={(labelIds) => {
              requestSecondChecklist?.(labelIds);
            }}
          />
        </ProForm.Item>

        <ProFormList
          name="suspendResonList"
          label="结构消杀任务中止原因维护"
          copyIconProps={false}
          initialValue={[{}]}
          // max={10}
          fieldExtraRender={() => {
            if (suspendResonList?.length > 3) {
              return (
                <Button
                  className="w-[552px]"
                  onClick={() => {
                    setIsShrink(!isShrink);
                  }}
                >
                  {isShrink ? '更多展示' : '收起展示'}
                </Button>
              );
            }
          }}
          itemRender={({ action, listDom }, { index }) => {
            return isShrink && index > 2 ? (
              <></>
            ) : (
              <div className="flex">
                {listDom}
                {action}
              </div>
            );
          }}
          deleteIconProps={
            !disabled && suspendResonList?.length > 1
              ? {
                  Icon: CloseCircleOutlined,
                }
              : false
          }
          actionGuard={{
            beforeAddRow: (_d, _i, count) => {
              if (count > 9) {
                message.error('最多10条，无法再新增');

                return Promise.resolve(false);
              } else {
                return Promise.resolve(true);
              }
            },
          }}
          creatorButtonProps={{
            position: 'top',
            creatorButtonText: '新增原因',
            disabled,
            style: {
              width: 552,
            },
          }}
        >
          <ProFormText
            disabled={disabled}
            placeholder="请输入不通过原因，最多30字"
            name={'reason'}
            width="xl"
            fieldProps={{
              maxLength: 30,
              showCount: true,
            }}
          />
        </ProFormList>
      </ItemCard>
      <ItemCard title="紧急消杀设置">
        <ProFormItem label="紧急消杀关联检查表">
          <ChecklistTable
            disabled={disabled}
            name={'exigencyDisinfectionWorksheetConfig'}
            tagOptions={tagOptions}
            reviewerOptions={reviewerOptions}
            requestChecklists={requestChecklists}
            form={form}
            extendChecklistOptions={extendChecklistOptions}
            requestRoles={requestRoles}
            requestWeights={requestWeights}
            onlySheet={true}
          />
        </ProFormItem>
        <TimeValidity
          label="整改审核时效"
          name={['exigencyDisinfectionIssueConfig', 'auditDuration']}
          disabled={disabled}
          isRequired
        />
        <TimeValidity
          label="不合格项整改任务时效"
          name={['exigencyDisinfectionIssueConfig', 'nonEngineeringBarrierRectificationDuration']}
          disabled={disabled}
          isRequired
        />
        <TimeValidity
          label="工程封堵整改任务时效"
          name={['exigencyDisinfectionIssueConfig', 'engineeringBarrierRectificationDuration']}
          disabled={disabled}
          isRequired
        />
        <FormItem
          label="不合格整改项审核人角色配置"
          name={['exigencyDisinfectionIssueConfig', 'nonEngineeringBarrierAuditRoleIds']}
          rules={[
            {
              required: true,
              message: '请选择不合格整改项审核人角色配置',
            },
          ]}
        >
          <RoleCascader
            multiple
            options={reviewerOptions || []}
            style={{ width: '20.5rem' }}
            placeholder="请选择"
            disabled={disabled}
          />
        </FormItem>
      </ItemCard>
      <ItemCard title="日常消杀设置">
        <TimeValidity
          label="整改审核时效"
          name={['dailyDisinfectionIssueConfig', 'auditDuration']}
          disabled={disabled}
          isRequired
        />
        <TimeValidity
          label="不合格项整改任务时效"
          name={['dailyDisinfectionIssueConfig', 'nonEngineeringBarrierRectificationDuration']}
          disabled={disabled}
          isRequired
        />
        <TimeValidity
          label="工程封堵整改任务时效"
          name={['dailyDisinfectionIssueConfig', 'engineeringBarrierRectificationDuration']}
          disabled={disabled}
          isRequired
        />
        <FormItem
          label="不合格整改项审核人角色配置"
          name={['dailyDisinfectionIssueConfig', 'nonEngineeringBarrierAuditRoleIds']}
          rules={[
            {
              required: true,
              message: '请选择不合格整改项审核人角色配置',
            },
          ]}
        >
          <RoleCascader
            multiple
            options={reviewerOptions || []}
            style={{ width: '20.5rem' }}
            placeholder="请选择"
            disabled={disabled}
          />
        </FormItem>
      </ItemCard>
    </>
  );
};

export default DisinfectionSetupCard;
