import { FC, useMemo } from 'react';
import { Select, Table } from 'antd';
import ItemCard from '@/components/item-card';

interface GuaranteeCardProps {
  values?: any[];
}

const GuaranteeCard: FC<GuaranteeCardProps> = ({ values }) => {
  const { cols, dataSource } = useMemo(() => {
    const lens: number[] = [];

    const data = values?.map((item) => {
      lens.push(item?.length);

      const map = {};

      item.forEach((option, index) => {
        map[index] = option?.value;
        map[`option${index}`] = [option];
      });

      return map;
    });

    return { cols: Math.max(...lens, 0), dataSource: data };
  }, [values]);

  const columns = useMemo(() => {
    return Array.from({ length: cols }).map((_, index) => {
      if (index === 0) {
        return {
          title: '原任务执行角色',
          dataIndex: index,
          render: (_, record) => {
            return record?.[index] ? (
              <Select
                options={record?.[`option${index}`]}
                className="w-[180px]"
                value={record?.[index]}
                disabled={true}
              />
            ) : undefined;
          },
        };
      }

      return {
        title: '任务替补执行角色',
        dataIndex: index,
        render: (_, record) => {
          return record?.[index] ? (
            <Select
              options={record?.[`option${index}`]}
              className="w-[180px]"
              value={record?.[index]}
              disabled={true}
            />
          ) : undefined;
        },
      };
    });
  }, [cols]);

  return (
    <ItemCard
      title={
        <div>
          <span>人员异常兜底配置</span>
          <span className="text-xs">（此处配置将对策略所有任务类型生效）</span>
        </div>
      }
    >
      <Table columns={columns} dataSource={dataSource} pagination={false} />
    </ItemCard>
  );
};

export default GuaranteeCard;
