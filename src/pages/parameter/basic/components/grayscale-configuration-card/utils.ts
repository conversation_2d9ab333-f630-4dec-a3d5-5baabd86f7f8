/**
 * @description 根据组织机构id查询对应组织信息
 * @param organizationOptions 组织选项
 * @param {number[]} ids 组织id
 * @returns
 */
export function mapByIdQuery(organizationOptions: any[], ids: number[]) {
  const map = new Map<number, Record<string, any>>();

  const flattenTree = (tree: any[]) => {
    return tree?.reduce((acc, node) => {
      acc.push(node);

      // 如果有子节点，递归处理并将结果展开
      if (Array.isArray(node?.children) && node?.children?.length) {
        acc.push(...flattenTree(node.children));
      }

      return acc;
    }, []);
  };

  flattenTree(organizationOptions)?.forEach(({ value, ...rest }) => {
    map.set(+value?.split('-')[1], { ...rest, value });
  });

  return ids?.map((m) => map.get(m));
}
