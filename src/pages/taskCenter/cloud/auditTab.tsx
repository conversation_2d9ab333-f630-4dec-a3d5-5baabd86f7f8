import { FC, useEffect, useState } from 'react';
import { ProCard, ProForm, ProTable } from '@ant-design/pro-components';
import { Button, message, Space } from 'antd';
import dayjs from 'dayjs';
import useTransferModal from './hooks/use-transfer-modal';
import Service, { RequestName } from './service';
import { getAuditOrPatrolSearchColumns, getAuditOrPatrolTableColumns } from './table-config';
import { TabKey } from '.';
import { DownloadButton } from '@/components/DownloadButton';
import { Permission } from '@/components/Permission';
import { getColumns, getCommonConfig, onReset } from '@/components/pro-table-config';
import { Auth } from '@/constants/auth';
import { StrategyAuditRiskLevelCN, StrategyTaskStatus } from '@/constants/strategy';
import useOutOfBusinessModal from '@/hooks/use-out-of-business-modal';
import useQuerySearchParams from '@/hooks/use-query-search-params';
import useUserListCaching from '@/hooks/use-user-list-caching';
import { usePermission } from '@/hooks/usePermission';
import useConfig from '@/mobx/config';
import { ModifyTaskUserModal } from '@/pages/selfTask/reviewTask/components/ModifyTaskUserModal';
import { RouteKey } from '@/router/config';
import useGlobalNavigate from '@/router/useGlobalNavigate';
import { formatDateToUTC } from '@/utils/date';

const AuditTab: FC = () => {
  const [params, setParams]: any = useQuerySearchParams();
  const [form] = ProForm.useForm();
  const [selectedRowKeys, setSelectedRowKeys] = useState<any>([]);
  const [selectedRows, setSelectedRows] = useState<any>([]);
  const [service, executeRequest] = Service();
  const navigate = useGlobalNavigate();

  const { showModal } = useTransferModal();
  const { showModal: showCloseModal } = useOutOfBusinessModal();
  const { config }: any = useConfig();
  const { userListCachingRef } = useUserListCaching({ options: service?.personOptions });

  const commonConfig = getCommonConfig({
    search: {
      form,
    },
  });

  const hasPermission = usePermission();
  const hasModifyUserPermission = hasPermission(Auth.策略_视频云巡检_线上稽核任务_修改执行人);

  useEffect(() => {
    executeRequest(RequestName.GetGroupTreeList);
    console.log(params?.auditPersonId, '=params?.auditPersonId');

    if (params?.auditPersonId) {
      executeRequest(RequestName.GetUserInfoList);
    }

    if (params?.auditStore) {
      const { shopIds, groupId } = params?.auditStore;

      if (shopIds?.length > 0 || groupId) {
        executeRequest(RequestName.GetStoreList, { groupId, groupType: 2, privilegeCode: 1 });
      }
    }

    form?.setFieldsValue({
      ...params,
      auditPersonId: params?.auditPersonId ? Number(params?.auditPersonId) : undefined,
    });
    form?.submit();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    form?.submit();
  }, [params?.auditPageNo, params?.auditPageSize, form]);

  const columns = getColumns({
    searchColumns: getAuditOrPatrolSearchColumns({
      tabKey: TabKey.Audit,
      organizationOptions: service?.organizationOptions,
      storeOptions: service?.storeOptions,
      storeLoading: service?.storeLoading,
      onStoreFocus: (groupId: any) => {
        executeRequest(RequestName.GetStoreList, { groupId, groupType: 2, privilegeCode: 1 });
      },

      userListCachingRef,
      personOptions: service?.personOptions,
      personLoading: service?.personLoading,
      onPersonFocus: () => {
        executeRequest(RequestName.GetUserInfoList);
      },

      riskOptions: Object.keys(StrategyAuditRiskLevelCN).map((key) => ({
        value: key,
        label: StrategyAuditRiskLevelCN[key],
      })),
    }),
    tableColumns: getAuditOrPatrolTableColumns({
      tabKey: TabKey.Audit,
      showModal: showCloseModal,
      onPatrol: (record) => {
        navigate(RouteKey.TCCPatrol, {
          searchParams: {
            tasks: JSON.stringify([
              {
                taskId: record?.taskId,
                shopId: record?.shopId,
                shopName: record?.shopName,
                hikvisionStoreId: record?.hikvisionStoreId,
              },
            ]),
            type: TabKey.Audit,
            size: 1,
            reformLimit: config?.REPORT_VIDEO_NOT_QUALIFIED_ISSUE_ITEM_DEFAULT_RECTIFY_DEADLINE,
          },
        });
      },
      onTransfer: (record) => {
        showModal({
          initialValues: {
            taskIds: [record?.taskId],
          },
          onOk: (values) => {
            return executeRequest(RequestName.ApplyBatchTransfer, values).then(() => {
              message.success('转派成功');
              form?.submit();
            });
          },
        });
      },
    }),
  });

  const formatParams = (values) => {
    const { auditStore, auditDataRange, auditRiskLevel, auditTaskStatus, auditPageNo, auditPageSize, auditPersonId } =
      values;

    return {
      ...auditStore,
      pageNo: auditPageNo || 1,
      pageSize: auditPageSize || 20,
      taskStartTime: auditDataRange?.[0] && formatDateToUTC(auditDataRange?.[0]),
      taskEndTime: auditDataRange?.[1] && formatDateToUTC(auditDataRange?.[1]),
      taskUserId: auditPersonId,
      riskLevels: auditRiskLevel,
      taskStatus: auditTaskStatus,
    };
  };

  return (
    <>
      <ProTable
        {...commonConfig}
        columns={columns}
        scroll={{ x: 1400 }}
        rowKey="taskId"
        params={{ tabKey: params?.tabKey }}
        rowSelection={{
          selectedRowKeys,
          onChange: (selectedRowKeys, selectedRows) => {
            setSelectedRowKeys(selectedRowKeys);
            setSelectedRows(selectedRows);
          },
          getCheckboxProps: (record) => ({
            disabled: ![
              StrategyTaskStatus.WAITING_START,
              StrategyTaskStatus.RUNNING,
              StrategyTaskStatus.EXPIRED_RUNNING,
            ].includes(record?.taskStatus), // Column configuration not to be checked
          }),
        }}
        tableExtraRender={() => {
          const transferTaskIds = selectedRows
            ?.filter((record) => [StrategyTaskStatus.WAITING_START].includes(record?.taskStatus) && record?.hasTransfer)
            ?.map((item) => item?.taskId);

          return (
            <ProCard>
              <div className="mb-2 flex justify-between">
                <Space>
                  <Button
                    type="primary"
                    disabled={!selectedRowKeys?.length}
                    onClick={() => {
                      const _tasks = selectedRows?.filter(
                        (item) =>
                          [
                            StrategyTaskStatus.WAITING_START,
                            StrategyTaskStatus.RUNNING,
                            StrategyTaskStatus.EXPIRED_RUNNING,
                          ].includes(item?.taskStatus) && item?.taskHandler,
                      );

                      if (!_tasks?.length) {
                        message.warning('没有可巡检的门店');

                        return;
                      }

                      navigate(RouteKey.TCCPatrol, {
                        searchParams: {
                          tasks: JSON.stringify(
                            _tasks?.slice(0, config?.REPORT_VIDEO_SAME_TIME_LIMIT)?.map((item) => ({
                              taskId: item?.taskId,
                              shopId: item?.shopId,
                              shopName: item?.shopName,
                              hikvisionStoreId: item?.hikvisionStoreId,
                            })),
                          ),
                          type: TabKey.Audit,
                          size: Math.min(selectedRows?.length, config?.REPORT_VIDEO_SAME_TIME_LIMIT),
                        },
                      });
                    }}
                  >
                    批量巡检
                  </Button>
                  <Button
                    disabled={!transferTaskIds?.length}
                    onClick={() => {
                      showModal({
                        initialValues: {
                          taskIds: transferTaskIds,
                        },
                        onOk: (values) => {
                          return executeRequest(RequestName.ApplyBatchTransfer, values).then(() => {
                            message.success('转派成功');
                            form?.submit();
                          });
                        },
                      });
                    }}
                  >
                    批量转派
                  </Button>
                  <Permission permission={Auth.策略_视频云巡检_线上稽核任务_修改执行人}>
                    <Button
                      type="primary"
                      disabled={!selectedRowKeys?.length}
                      onClick={() => {
                        ModifyTaskUserModal.showModal({
                          data: {
                            taskIds: selectedRowKeys,
                          },
                          onSuccess: () => {
                            form?.submit();
                          },
                        });
                      }}
                    >
                      修改执行人
                    </Button>
                  </Permission>
                </Space>
                <DownloadButton
                  downloadReq={() => {
                    const payload = { ...params, ...form?.getFieldsValue(), tabKey: TabKey.Audit };

                    return executeRequest(
                      hasModifyUserPermission
                        ? RequestName.ExportCloudAuditTaskListManage
                        : RequestName.ExportCloudAuditTaskList,
                      formatParams(payload),
                    );
                  }}
                />
              </div>
            </ProCard>
          );
        }}
        tableRender={(_p, _d, { table }) => {
          return <ProCard>{table}</ProCard>;
        }}
        loading={service?.auditLoading}
        pagination={{
          current: params?.auditPageNo || 1,
          pageSize: params?.auditPageSize || 20,
          showSizeChanger: true,
          onChange: (page, pageSize) => {
            setParams({
              ...params,
              auditPageNo: page,
              auditPageSize: pageSize,
            });
          },
          total: service?.auditTaskTotal,
        }}
        dataSource={service?.auditTaskList}
        onSubmit={async (values) => {
          const newValues = { ...params, ...form?.getFieldsValue(), ...values, tabKey: TabKey.Audit };

          setParams(newValues);

          await executeRequest(
            hasModifyUserPermission ? RequestName.GetCloudAuditTaskListManage : RequestName.GetCloudAuditTaskList,
            formatParams(newValues),
          );
        }}
        onReset={() => {
          setParams(
            Object.assign(
              params,
              onReset(form, { auditDataRange: [dayjs().add(-6, 'day').startOf('day'), dayjs().endOf('day')] }),
            ),
          );
        }}
      />
      <ModifyTaskUserModal />
    </>
  );
};

export default AuditTab;
