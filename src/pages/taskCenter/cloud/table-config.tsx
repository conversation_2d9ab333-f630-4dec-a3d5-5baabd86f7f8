import { ProColumns, ProFormSelectProps } from '@ant-design/pro-components';
import { Space, Tag } from 'antd';
import classNames from 'classnames';
import dayjs from 'dayjs';
import { Tab<PERSON><PERSON> } from '.';
import { ChecklistSelectProps } from '@/components/checklist-select';
import {
  getChecklistColumn,
  getDateRangeColumn,
  getPersonColumn,
  getReportPassedColumn,
  getReportStatusColumn,
  getStoreColumn,
} from '@/components/pro-table-config';
import getCloudRiskTypeColumn from '@/components/pro-table-config/cloud-risk-type';
import getStrategyTaskStatusColumn from '@/components/pro-table-config/strategy-task-status';
import { getTaskTemplateColumn } from '@/components/pro-table-config/task-template';
import { StoreSelectProps } from '@/components/store-select';
import { StoreStatus, StoreStatusCN } from '@/constants/organization';
import {
  StrategyAuditRiskLevelCN,
  StrategyRoutineTypeCN,
  StrategySpecialistRiskLevelCN,
  StrategyTaskStatus,
  StrategyTaskStatusCN,
  StrategyTemplateTypeCN,
} from '@/constants/strategy';
import ITimePicker, { DateQueryType } from '@/pages/selfTask/reviewTask/components/TimePicker';

interface SearchColumnsProps {
  organizationOptions?: StoreSelectProps['organizationOptions'];
  storeOptions?: StoreSelectProps['storeOptions'];
  storeLoading?: StoreSelectProps['storeLoading'];
  onStoreFocus?: StoreSelectProps['onStoreFocus'];
  riskOptions?: ProFormSelectProps['options'];
  tabKey?: string;
  tagOptions?: ChecklistSelectProps['tagOptions'];
  checklistOptions?: ChecklistSelectProps['checklistOptions'];
  onTagChange?: ChecklistSelectProps['onTagChange'];
  templateOptions?: ProFormSelectProps['options'];
  userListCachingRef?: any;
  onPersonFocus?: () => void;
  personOptions?: ProFormSelectProps['options'];
  personLoading?: boolean;
}

export const getAuditOrPatrolSearchColumns = (opt?: SearchColumnsProps): ProColumns[] => {
  return [
    getStoreColumn(
      {
        organizationOptions: opt?.organizationOptions,
        storeOptions: opt?.storeOptions,
        storeLoading: opt?.storeLoading,
        onStoreFocus: opt?.onStoreFocus,
      },
      { dataIndex: opt?.tabKey === TabKey.Patrol ? 'patrolStore' : 'auditStore' },
    ),
    getDateRangeColumn(
      {},
      {
        title: '任务下发时间',
        dataIndex: opt?.tabKey === TabKey.Patrol ? 'patrolDataRange' : 'auditDataRange',
        initialValue: [dayjs().add(-6, 'day').startOf('day'), dayjs().endOf('day')],
      },
    ),
    getCloudRiskTypeColumn(
      { options: opt?.riskOptions },
      { dataIndex: opt?.tabKey === TabKey.Patrol ? 'patrolRiskLevel' : 'auditRiskLevel' },
    ),

    getStrategyTaskStatusColumn(
      {
        options: Object.keys(StrategyTaskStatusCN)
          .filter(
            (key: StrategyTaskStatus) => ![StrategyTaskStatus.CANCELED, StrategyTaskStatus.AUDIT_EXPIRED].includes(key),
          )
          .map((key) => ({
            value: key,
            label: StrategyTaskStatusCN[key],
          })),
      },
      { dataIndex: opt?.tabKey === TabKey.Patrol ? 'patrolTaskStatus' : 'auditTaskStatus' },
    ),
    getPersonColumn(
      {
        options: opt?.userListCachingRef?.current || opt?.personOptions,
        onFocus: () => {
          if (!opt?.userListCachingRef?.current) {
            opt?.onPersonFocus();
          }
        },
        loading: opt?.personLoading,
      },
      { dataIndex: opt?.tabKey === TabKey.Patrol ? 'patrolPersonId' : 'auditPersonId', title: '执行人' },
    ),
  ];
};

export const getArriveSearchColumns = (opt?: SearchColumnsProps): ProColumns[] => {
  return [
    getStoreColumn(
      {
        organizationOptions: opt?.organizationOptions,
        storeOptions: opt?.storeOptions,
        storeLoading: opt?.storeLoading,
        onStoreFocus: opt?.onStoreFocus,
      },
      { dataIndex: 'arriveStore' },
    ),
    {
      title: '',
      dataIndex: 'dateQuery',
      hideInTable: true,
      colSize: 2,
      renderFormItem: () => {
        return (
          <ITimePicker
            typeOptions={[
              {
                value: DateQueryType.ReportSubmissionTime,
                label: '任务下发时间',
              },
              {
                value: DateQueryType.TaskStartTime,
                label: '巡检开始时间',
              },
            ]}
          />
        );
      },
      initialValue: {
        type: DateQueryType.ReportSubmissionTime,
        dateType: undefined,
        date: [dayjs().add(-6, 'days').startOf('day'), dayjs().endOf('day')],
      },
    },
    // getDateRangeColumn(
    //   {},
    //   {
    //     title: '任务下发时间',
    //     dataIndex: 'arriveDateRange',
    //     initialValue: [dayjs().add(-6, 'day').startOf('day'), dayjs().endOf('day')],
    //   },
    // ),
    // getDateRangeColumn(
    //   {},
    //   {
    //     title: '巡检开始时间',
    //     dataIndex: 'taskStartDateRange',
    //     initialValue: [dayjs().add(-6, 'day').startOf('day'), dayjs().endOf('day')],
    //   },
    // ),
    getStrategyTaskStatusColumn(
      {
        options: Object.keys(StrategyTaskStatusCN).map((key) => ({
          value: key,
          label: StrategyTaskStatusCN[key],
        })),
      },
      { dataIndex: 'taskStatusList' },
    ),
    {
      title: '任务完成原因',
      dataIndex: 'taskExtendStatus',
      valueEnum: {
        UNVERIFIABLE: '无法核查',
      },
      valueType: 'select',
    },

    getPersonColumn(
      {
        options: opt?.userListCachingRef?.current || opt?.personOptions,
        onFocus: () => {
          if (!opt?.userListCachingRef?.current) {
            opt?.onPersonFocus();
          }
        },
        loading: opt?.personLoading,
      },
      { dataIndex: 'arrivePersonId', title: '执行人' },
    ),
  ];
};

export const getReviewSearchColumns = (opt?: SearchColumnsProps): ProColumns[] => {
  return [
    getStoreColumn(
      {
        organizationOptions: opt?.organizationOptions,
        storeOptions: opt?.storeOptions,
        storeLoading: opt?.storeLoading,
        onStoreFocus: opt?.onStoreFocus,
      },
      { dataIndex: 'arriveStore' },
    ),
    getChecklistColumn(
      {
        tagOptions: opt?.tagOptions,
        checklistOptions: opt?.checklistOptions,
        onTagChange: opt?.onTagChange,
      },
      { dataIndex: 'worksheetId' },
    ),
    getTaskTemplateColumn({ options: opt?.templateOptions }, { dataIndex: 'templateId' }),
    getReportPassedColumn(null, { title: '报告是否通过', dataIndex: 'passed' }),
    getReportStatusColumn(
      {
        valueEnum: {
          WAITING_CONFIRM: '待确认',
          CONFIRMED: '已确认',
          CANCELED: '已作废',
        },
      },
      { dataIndex: 'reportStatus', title: '报告状态' },
    ),
    getDateRangeColumn(
      {},
      {
        title: '任务下发时间',
        dataIndex: 'arriveDateRange',
        initialValue: [dayjs().add(-6, 'day').startOf('day'), dayjs().endOf('day')],
      },
    ),
  ];
};

interface OperationProps {
  onPatrol?: (record: any) => void;
  onTransfer?: (record: any) => void;
  onReview?: (record: any) => void;
  onCameraSituation?: (record: any) => void;
  onDetail?: (record: any) => void;
  tabKey?: string;
  showModal?: (data: any) => void;
}

export const getAuditOrPatrolTableColumns = (opt: OperationProps): ProColumns[] => {
  return [
    {
      title: '门店编号',
      dataIndex: 'shopId',
      fixed: 'left',
      width: 160,
    },
    {
      title: '门店名称',
      dataIndex: 'shopName',
      width: 160,
    },
    {
      title: '门店状态',
      dataIndex: 'shopStatus',
      width: 80,
      render: (_, record) => {
        return record?.shopStatus ? (
          <Tag
            className={classNames({ 'cursor-pointer': record?.shopStatus === StoreStatus.OFFLINE })}
            color={record?.shopStatus === StoreStatus.OFFLINE ? 'red' : 'green'}
            onClick={(e) => {
              e.stopPropagation();

              if (record?.shopStatus === StoreStatus.OFFLINE) {
                opt?.showModal?.({
                  title: record?.shopName,
                  shopId: record?.shopId,
                });
              }
            }}
          >
            {StoreStatusCN?.[record?.shopStatus]}
          </Tag>
        ) : (
          '-'
        );
      },
    },
    // {
    //   title: '创建时间',
    //   dataIndex: 'taskUserName',
    //   width: 120,
    // },
    {
      title: '执行时段',
      dataIndex: 'evacuateTime',
      width: 200,
    },
    // {
    //   title: '任务下发时间',
    //   dataIndex: 'taskBeginTime',
    //   width: 180,
    //   render: (_, record) => (record?.taskBeginTime ? dayjs(record?.taskBeginTime).format('YYYY-MM-DD HH:mm:ss') : '-'),
    // },
    // {
    //   title: '任务截止时间',
    //   dataIndex: 'taskExpireTime',
    //   width: 180,
    //   render: (_, record) =>
    //     record?.taskExpireTime ? dayjs(record?.taskExpireTime).format('YYYY-MM-DD HH:mm:ss') : '-',
    // },
    {
      title: '风险类型',
      dataIndex: 'riskLevel',
      width: 120,
      render: (_, record) =>
        opt.tabKey === TabKey.Audit
          ? record?.riskLevel?.map((level) => StrategyAuditRiskLevelCN[level])
          : record?.riskLevel?.map((level) => StrategySpecialistRiskLevelCN[level]),
    },
    {
      title: '任务执行人',
      dataIndex: 'taskUserName',
      width: 120,
    },
    {
      title: '巡检开始时间',
      dataIndex: 'taskBeginTime',
      width: 180,
      render: (_, record) => (record?.taskBeginTime ? dayjs(record?.taskBeginTime).format('YYYY-MM-DD HH:mm:ss') : '-'),
    },
    {
      title: '巡检结束时间',
      dataIndex: 'taskExpireTime',
      width: 180,
      render: (_, record) =>
        record?.taskExpireTime ? dayjs(record?.taskExpireTime).format('YYYY-MM-DD HH:mm:ss') : '-',
    },
    {
      title: '任务状态',
      dataIndex: 'taskStatus',
      render: (_, record) => {
        if (record?.taskStatus === StrategyTaskStatus.COMPLETED && record?.hasExpired) {
          return '逾期已完成';
        }

        return StrategyTaskStatusCN[record?.taskStatus];
      },
    },
    {
      title: '操作',
      dataIndex: 'operation',
      width: 140,
      fixed: 'right',
      render: (_, record) => {
        return (
          <Space>
            {[StrategyTaskStatus.WAITING_START].includes(record?.taskStatus) &&
              record?.hasTransfer &&
              record?.taskHandler && (
                <a
                  onClick={() => {
                    opt?.onTransfer?.(record);
                  }}
                >
                  任务转派
                </a>
              )}
            {[
              StrategyTaskStatus.WAITING_START,
              StrategyTaskStatus.RUNNING,
              StrategyTaskStatus.EXPIRED_RUNNING,
            ].includes(record?.taskStatus) &&
              record?.taskHandler && (
                <a
                  onClick={() => {
                    opt?.onPatrol?.(record);
                  }}
                >
                  开始巡检
                </a>
              )}
          </Space>
        );
      },
    },
  ];
};

export const getArriveTableColumns = (opt: OperationProps): ProColumns[] => {
  return [
    {
      title: '门店编号',
      dataIndex: 'shopId',
      fixed: 'left',
      width: 160,
    },
    {
      title: '门店名称',
      dataIndex: 'shopName',
      width: 160,
    },
    {
      title: '门店状态',
      dataIndex: 'shopStatus',
      width: 80,
      render: (_, record) => {
        return record?.shopStatus ? (
          <Tag
            className={classNames({ 'cursor-pointer': record?.shopStatus === StoreStatus.OFFLINE })}
            color={record?.shopStatus === StoreStatus.OFFLINE ? 'red' : 'green'}
            onClick={(e) => {
              e.stopPropagation();

              if (record?.shopStatus === StoreStatus.OFFLINE) {
                opt?.showModal?.({
                  title: record?.shopName,
                  shopId: record?.shopId,
                });
              }
            }}
          >
            {StoreStatusCN?.[record?.shopStatus]}
          </Tag>
        ) : (
          '-'
        );
      },
    },
    {
      title: '任务执行人',
      dataIndex: 'taskUserName',
      width: 120,
    },
    {
      title: '任务类型',
      dataIndex: 'taskType',
      width: 120,
      valueEnum: StrategyTemplateTypeCN,
    },
    {
      title: '巡检类型',
      dataIndex: 'taskSubType',
      width: 120,
      valueEnum: StrategyRoutineTypeCN,
    },
    {
      title: '任务下发时间',
      dataIndex: 'createTime',
      width: 180,
      render: (_, record) => (record?.createTime ? dayjs(record?.createTime).format('YYYY-MM-DD HH:mm:ss') : '-'),
    },

    {
      title: '巡检开始时间',
      dataIndex: 'taskStartTime',
      width: 180,
      render: (_, record) => (record?.taskStartTime ? dayjs(record?.taskStartTime).format('YYYY-MM-DD HH:mm:ss') : '-'),
    },
    {
      title: '巡检结束时间',
      dataIndex: 'submitTime',
      width: 180,
      render: (_, record) => (record?.submitTime ? dayjs(record?.submitTime).format('YYYY-MM-DD HH:mm:ss') : '-'),
    },
    {
      title: '任务状态',
      dataIndex: 'taskStatus',
      width: 180,
      render: (_, record) => {
        if (record?.taskStatus === StrategyTaskStatus.AUDITING) {
          return '已完成';
        }

        if (record?.taskStatus === StrategyTaskStatus.COMPLETED && record?.hasExpired) {
          return '逾期已完成';
        }

        return StrategyTaskStatusCN[record?.taskStatus];
      },
    },
    {
      title: '任务完成原因',
      dataIndex: 'taskExtendStatus',
      width: 120,
      valueEnum: {
        UNVERIFIABLE: '无法核查',
        NONE: '-',
      },
    },
    {
      title: '操作',
      dataIndex: 'operation',
      width: 180,
      fixed: 'right',
      render: (_, record) => {
        return (
          <Space>
            {[
              StrategyTaskStatus.WAITING_START,
              StrategyTaskStatus.RUNNING,
              StrategyTaskStatus.EXPIRED_RUNNING,
            ].includes(record?.taskStatus) && (
              <Space>
                {record?.taskHandler && (
                  <a
                    onClick={() => {
                      opt?.onPatrol?.(record);
                    }}
                  >
                    开始巡检
                  </a>
                )}
                {record?.hasTransfer && record?.taskStatus === StrategyTaskStatus.WAITING_START && (
                  <a
                    onClick={() => {
                      opt?.onTransfer?.(record);
                    }}
                  >
                    任务转派
                  </a>
                )}
              </Space>
            )}
            {[StrategyTaskStatus.COMPLETED].includes(record?.taskStatus) && (
              <Space>
                {record?.taskExtendStatus !== 'UNVERIFIABLE' && (
                  <a
                    onClick={() => {
                      opt?.onDetail?.(record);
                    }}
                  >
                    查看详情
                  </a>
                )}
                <a
                  onClick={() => {
                    opt?.onCameraSituation?.(record);
                  }}
                >
                  摄像头情况
                </a>
              </Space>
            )}
          </Space>
        );
      },
    },
  ];
};

export const getReviewTableColumns = (opt: OperationProps): ProColumns[] => {
  return [
    {
      title: '策略名称',
      dataIndex: '',
      fixed: 'left',
      width: 160,
    },
    {
      title: '批次ID',
      dataIndex: '',
      width: 160,
    },
    {
      title: '任务名称',
      dataIndex: '',
      width: 120,
    },
    {
      title: '门店类型',
      dataIndex: '',
      width: 200,
    },
    {
      title: '门店名称',
      dataIndex: '',
      width: 200,
    },
    {
      title: '任务类型',
      dataIndex: '',
      width: 120,
      valueEnum: StrategyTemplateTypeCN,
    },
    {
      title: '巡检类型',
      dataIndex: '',
      width: 120,
      valueEnum: StrategyRoutineTypeCN,
    },
    {
      title: '诊断结果',
      dataIndex: '',
      width: 120,
    },
    {
      title: '巡检人',
      dataIndex: '',
      width: 120,
    },
    {
      title: '检查表',
      dataIndex: '',
      width: 120,
    },
    {
      title: '任务下发时间',
      dataIndex: 'taskBeginTime',
      width: 180,
      render: (_, record) => (record?.taskBeginTime ? dayjs(record?.taskBeginTime).format('YYYY-MM-DD HH:mm:ss') : '-'),
    },
    {
      title: '报告提交时间',
      dataIndex: 'taskExpireTime',
      width: 180,
      render: (_, record) =>
        record?.taskExpireTime ? dayjs(record?.taskExpireTime).format('YYYY-MM-DD HH:mm:ss') : '-',
    },
    {
      title: '报告状态',
      dataIndex: 'taskStatus',
    },
    {
      title: '报告是否通过',
      dataIndex: 'taskStatus',
    },
    {
      title: '报告通过分数',
      dataIndex: 'taskStatus',
    },
    {
      title: '报告得分',
      dataIndex: 'taskStatus',
    },
    {
      title: '点评状态',
      dataIndex: 'taskStatus',
    },
    {
      title: '点评是否通过',
      dataIndex: 'taskStatus',
    },
    {
      title: '视频云巡检时长',
      dataIndex: 'taskStatus',
    },
    {
      title: '签到信息',
      dataIndex: 'taskStatus',
    },
    {
      title: '签离信息',
      dataIndex: 'taskStatus',
    },
    {
      title: '检查项合格率',
      dataIndex: 'taskStatus',
    },
    {
      title: '总检查项',
      dataIndex: 'taskStatus',
    },
    {
      title: '不合格项',
      dataIndex: 'taskStatus',
    },
    {
      title: '整改项',
      dataIndex: 'taskStatus',
    },
    {
      title: '已整改项',
      dataIndex: 'taskStatus',
    },

    {
      title: '操作',
      dataIndex: 'operation',
      width: 140,
      fixed: 'right',
      render: (_, record) => {
        return (
          <Space>
            <Space>
              {[StrategyTaskStatus.WAITING_START].includes(record?.taskStatus) && record?.hasTransfer && (
                <a
                  onClick={() => {
                    opt?.onTransfer?.(record);
                  }}
                >
                  任务转派
                </a>
              )}
              {[
                StrategyTaskStatus.WAITING_START,
                StrategyTaskStatus.RUNNING,
                StrategyTaskStatus.EXPIRED_RUNNING,
              ].includes(record?.taskStatus) && (
                <a
                  onClick={() => {
                    opt?.onReview?.(record);
                  }}
                >
                  开始点评
                </a>
              )}
            </Space>
          </Space>
        );
      },
    },
  ];
};
