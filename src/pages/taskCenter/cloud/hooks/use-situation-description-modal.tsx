import { useCallback, useEffect, useRef, useState } from 'react';
import { CloseCircleFilled, PlusOutlined } from '@ant-design/icons';
import {
  ProForm,
  ProFormCheckbox,
  ProFormDatePicker,
  ProFormDependency,
  ProFormDigit,
  ProFormItemProps,
  ProFormProps,
  ProFormRadio,
  ProFormSelect,
  ProFormTextArea,
  ProFormUploadDragger,
} from '@ant-design/pro-components';
import { InputProps, message, Modal, ModalProps, Spin } from 'antd';
import { cloneDeep } from 'lodash';
import LimitedAutoComplete from '@/components/LimitedAutoComplete';
import MediaCard from '@/components/MediaCard';
import PictureInput from '@/components/picture-input';
import {
  StrategyRectificationLoopTypeCN,
  StrategyRectificationMethod,
  StrategyRectificationMethodCN,
} from '@/constants/strategy';
import { useOSSClient } from '@/hooks/use-oss-client';

// eslint-disable-next-line max-lines-per-function
const useSituationDescriptionModal = () => {
  const modalRef: any = useRef();
  const [form] = ProForm.useForm();
  const [loading, setLoading] = useState(false);

  // const { setInfo } = useMediaView();

  useEffect(() => {
    if (modalRef?.current) {
      modalRef.current.update({
        okButtonProps: {
          disabled: loading,
        },
      });
    }
  }, [loading]);

  // const issueType = ProForm.useWatch('issueType', form);
  // const isLoopRectify = ProForm.useWatch('isLoopRectify', form);

  const { uploadFile } = useCallback(useOSSClient, [])('PATROL');

  // eslint-disable-next-line max-lines-per-function
  const showModal = ({
    title,
    onOk,
    initialValues,
    showConfig,
    mustConfig,
    reasonList,
    scoreMax,
    copyUsers,
    style,
    newWindow,
    width,
    drawerPlacement,
    readOnlyReformLimit,
  }: {
    title: ModalProps['title'];
    style?: any;
    maxLength?: InputProps['maxLength'];
    onOk?: (value: any) => Promise<any>;
    scoreMax?: number;
    /** 整改期限是否为只读 */
    readOnlyReformLimit?: boolean;
    initialValues?: ProFormProps['initialValues'];
    placeholder?: string;
    showConfig?: {
      camera?: boolean;
      remark?: boolean;
      rectify?: boolean;
      reason?: boolean;
      score?: boolean;
    };
    width?: number;
    copyUsers?: any[];
    newWindow?: boolean;
    drawerPlacement?: string;
    mustConfig?: {
      camera?: boolean;
      remark?: boolean;
      reason?: boolean;
      disabledAlbum?: boolean; // 是否允许使用相册
      issueType?: boolean;
    };
    reasonList?: any[];
  } & Pick<ProFormItemProps, 'label' | 'name' | 'rules'>) => {
    if (initialValues) {
      form?.setFieldsValue(initialValues);
    }

    return (modalRef.current = Modal.confirm({
      title,
      style,
      closable: true,
      okText: '保存',
      cancelText: '取消',
      icon: null,
      width: width || 600,
      content: (
        <>
          <ProForm submitter={false} form={form} layout="horizontal" className="mt-4" labelCol={{ span: 6 }}>
            <ProForm.Item name="loading" hidden />
            {/* <ProForm.Item name="preview" hidden label="预览" />
            <ProFormDependency name={['preview']}>
              {({ preview }) => {
                return (
                  <Image
                    wrapperStyle={{ display: 'none' }}
                    preview={{
                      visible: preview?.visible,
                      onVisibleChange: (visible) => {
                        if (!visible) {
                          form?.setFieldValue('preview', {
                            visible: false,
                            image: '',
                          });
                        }
                      },
                    }}
                    src={preview?.image}
                  />
                );
              }}
            </ProFormDependency> */}
            {showConfig?.score && (
              <ProFormDigit
                label="分数"
                name="itemScore"
                fieldProps={{
                  suffix: '分',
                }}
                width={'sm'}
                min={0}
                max={(scoreMax || 1) - 1}
                rules={[
                  {
                    required: true,
                    message: '请输入分数',
                  },
                ]}
              />
            )}
            {showConfig.reason && !!reasonList?.length && (
              <ProFormCheckbox.Group
                name="itemReason"
                label="不合格原因"
                layout="vertical"
                rules={[{ required: mustConfig?.reason, message: '请选择不合格原因' }]}
                options={reasonList?.concat([
                  {
                    label: '其他',
                    value: 'OTHER',
                  },
                ])}
              />
            )}
            <ProFormDependency name={['itemReason']}>
              {({ itemReason }) => {
                return (
                  itemReason?.includes('OTHER') && (
                    // <ProFormText
                    //   label="其他原因"
                    //   name="itemOtherReason"
                    //   placeholder="请输入其他原因"
                    //   rules={[
                    //     {
                    //       required: true,
                    //       message: '请输入其他原因',
                    //     },
                    //   ]}
                    // />
                    <ProForm.Item
                      name="itemOtherReason"
                      label="其他原因"
                      rules={[{ required: true, message: '请输入其他原因' }]}
                    >
                      <LimitedAutoComplete
                        options={JSON.parse(localStorage.getItem('video_itemOtherReason') || '[]').map(
                          (item: string) => ({
                            label: item,
                            value: item,
                          }),
                        )}
                        maxLength={200}
                        placeholder="请输入其他原因"
                        allowClear
                      />
                    </ProForm.Item>
                  )
                );
              }}
            </ProFormDependency>

            {showConfig?.camera && (
              <ProForm.Item
                label="照片"
                name="itemImages"
                rules={[
                  {
                    required: mustConfig?.camera,
                    message: '请上传照片',
                  },
                ]}
              >
                <PictureInput
                  onChange={async (file) => {
                    try {
                      const response = await uploadFile(file, true);

                      const itemImages: any = form.getFieldValue('itemImages') || [];

                      form.setFieldValue(
                        'itemImages',
                        itemImages?.concat({
                          response,
                          status: 'done',
                          type: response?.contentType,
                          thumbUrl: response?.snapshotUrl,
                          uid: response?.id,
                        }),
                      );
                    } catch (e) {
                      message.error(e);
                    }
                  }}
                />
                <ProFormUploadDragger
                  name="itemImages"
                  noStyle
                  accept=".jpg,.png,.mp4,.jpeg"
                  title={'点击或将文件拖到这里上传'}
                  description={'支持.jpg/.png/.mp4/.jpeg格式，视频大小不得超过20M'}
                  icon={<PlusOutlined />}
                  max={9}
                  fieldProps={{
                    showUploadList: false,

                    //   return (
                    //     <div className="flex flex-wrap gap-2">
                    //       {fileList?.map(({ response: item }) => {
                    //         return (
                    //           item && (
                    //             <div className="relative inline-flex mt-2.5">
                    //               <CloseCircleFilled className="absolute top-[-10px] right-[-10px] z-10 size-5 text-[#ff4d4f]" />
                    //               <MediaCard
                    //                 file={item}
                    //                 key={item.id}
                    //                 newWindow={newWindow}
                    //                 // fileList={simpleImageList}
                    //                 width={100}
                    //                 height={100}
                    //               />
                    //             </div>
                    //           )
                    //         );
                    //       })}
                    //     </div>
                    //   );
                    // },
                    customRequest: async (e: any) => {
                      try {
                        if (!e?.file?.type?.includes('video') && !e?.file?.type?.includes('image')) {
                          message.error('请上传图片或视频');

                          const itemImages = form.getFieldValue('itemImages');
                          const cloneItemImages = cloneDeep(itemImages);

                          cloneItemImages.pop();

                          form.setFieldValue('itemImages', cloneItemImages);

                          return;
                        }

                        form?.setFieldValue('loading', true);
                        setLoading(true);

                        const response = await uploadFile(e.file, true, null, {});

                        e.onSuccess(response);
                        form?.setFieldValue('loading', false);
                        setLoading(false);
                      } catch (error) {
                        e.onError();

                        const itemImages = form.getFieldValue('itemImages');

                        const cloneItemImages = cloneDeep(itemImages);

                        cloneItemImages.pop();

                        form?.setFieldValue('itemImages', cloneItemImages);
                        form?.setFieldValue('loading', false);
                        setLoading(false);
                      } finally {
                      }
                    },
                  }}
                />
                <ProFormDependency name={['itemImages', 'loading']}>
                  {({ itemImages, loading }) => {
                    const images = itemImages?.filter(({ response }) => response).map(({ response }) => response);

                    return (
                      <div className="flex flex-wrap gap-2">
                        {itemImages?.map(({ response: item }, index) => {
                          return item ? (
                            <div className="relative inline-flex mt-2.5">
                              <CloseCircleFilled
                                className="absolute top-[-8px] right-[-8px] z-10  text-[#ff4d4f]"
                                style={{ fontSize: '16px' }}
                                onClick={() => {
                                  form.setFieldValue(
                                    'itemImages',
                                    itemImages?.filter((_, i) => i !== index),
                                  );
                                }}
                              />
                              <MediaCard
                                file={item}
                                key={item.id}
                                newWindow={newWindow}
                                fileList={images}
                                width={80}
                                height={80}
                                drawerPlacement={drawerPlacement}
                              />
                            </div>
                          ) : undefined;
                        })}
                        {loading && (
                          <Spin tip="上传中">
                            <div className="size-20" />
                          </Spin>
                        )}
                      </div>
                    );
                  }}
                </ProFormDependency>
              </ProForm.Item>
            )}

            {showConfig?.remark && (
              <ProFormTextArea
                label="详情说明"
                name="itemRemark"
                fieldProps={{
                  maxLength: 300,
                  showCount: true,
                }}
                rules={[
                  {
                    required: mustConfig?.remark,
                    message: '请填写详情说明',
                  },
                ]}
              />
            )}

            {showConfig?.rectify && (
              <>
                {mustConfig?.issueType && (
                  <>
                    <ProFormRadio.Group
                      label="整改方式"
                      options={[
                        {
                          label: StrategyRectificationMethodCN[StrategyRectificationMethod.SHOP_ISSUE],
                          value: 'SELECT_SHOP_ISSUE',
                        },
                        {
                          label: StrategyRectificationMethodCN[StrategyRectificationMethod.AT_ONCE_ISSUE],
                          value: 'SELECT_AT_ONCE_ISSUE',
                        },
                      ]}
                      name="issueType"
                      rules={[
                        {
                          required: mustConfig?.issueType,
                          message: '请选择整改方式',
                        },
                      ]}
                    />
                    <ProFormRadio.Group
                      label="是否需要循环整改"
                      name="isLoopRectify"
                      rules={[
                        {
                          required: true,
                          message: '请选择是否需要循环整改',
                        },
                      ]}
                      options={[
                        {
                          label: '不需要',
                          value: 0,
                        },
                        {
                          label: '需要',
                          value: 1,
                        },
                      ]}
                    />
                  </>
                )}
                <ProFormDependency name={['isLoopRectify']}>
                  {({ isLoopRectify }) => {
                    return (
                      !!isLoopRectify && (
                        <>
                          <ProFormSelect
                            label="循环整改频次"
                            name="loopRectifyType"
                            rules={[{ required: true, message: '请选择循环整改频次' }]}
                            valueEnum={StrategyRectificationLoopTypeCN}
                          />
                          <ProFormDatePicker
                            label="截止时间"
                            name="loopEndTime"
                            rules={[{ required: true, message: '请选择截止时间' }]}
                            fieldProps={{
                              format: 'YYYY-MM-DD',
                            }}
                          />
                        </>
                      )
                    );
                  }}
                </ProFormDependency>

                <ProFormDependency name={['issueType']}>
                  {({ issueType }) => {
                    return (
                      issueType !== 'SELECT_AT_ONCE_ISSUE' && (
                        <>
                          <ProFormDigit
                            label="整改期限"
                            name="reformLimit"
                            width={'sm'}
                            fieldProps={{
                              suffix: '天',
                            }}
                            disabled={readOnlyReformLimit}
                            rules={[
                              {
                                required: true,
                                message: '请输入整改期限',
                              },
                            ]}
                          />
                          <ProFormRadio.Group
                            label="处理人"
                            options={[{ label: '门店所有员工', value: 1 }]}
                            name="followName"
                            disabled={true}
                            fieldProps={{
                              defaultValue: 1,
                            }}
                          />
                          <ProFormSelect label="抄送人" name="copyUserIds" mode="multiple" options={copyUsers} />
                        </>
                      )
                    );
                  }}
                </ProFormDependency>
              </>
            )}
          </ProForm>
        </>
      ),

      onOk: async () => {
        return await form?.validateFields().then(() => {
          const values = form?.getFieldsValue();

          console.log('values', values);

          const { itemOtherReason } = values;

          if (itemOtherReason) {
            const itemOtherReasonLocalStorage = JSON.parse(localStorage.getItem('video_itemOtherReason') || '[]');
            const newItemOtherReasonLocalStorage = [
              ...new Set([...itemOtherReasonLocalStorage, itemOtherReason]),
            ].slice(-5);

            localStorage.setItem('video_itemOtherReason', JSON.stringify(newItemOtherReasonLocalStorage));
          }

          if (values?.itemReason?.includes('OTHER')) {
            values.itemReason = values?.itemReason?.filter((v) => v !== 'OTHER');
          }

          return onOk?.(values)?.then(() => {
            form?.resetFields();
          });
        });
      },
      onCancel: () => {
        form?.resetFields();
      },
    }));
  };

  return {
    showModal,
  };
};

export default useSituationDescriptionModal;
