/* eslint-disable max-lines-per-function */
import { useContext, useEffect, useRef, useState } from 'react';
import { ActionType, Key, ProCard, ProForm, ProTable } from '@ant-design/pro-components';
import { Button, Space, Tag, Tooltip } from 'antd';
import dayjs from 'dayjs';
import { isEmpty } from 'lodash';
import { CheckRouteButton } from './CheckRouteButton';
import { DescModal } from './DescModal';
import { ModifyBatchModalForm } from './ModifyBatchModalForm';
import { auditSchedulingTabsEnum, MapRouteContext } from '..';
import { PlanStatusEnum, PlanStatusEnumCN, PlanTagsEnumCN } from '../const';
import Service, { RequestName } from '../service';
import { Permission } from '@/components/Permission';
import { getColumns, getCommonConfig, getPersonColumn } from '@/components/pro-table-config';
import { Auth } from '@/constants/auth';
import useQuerySearchParams from '@/hooks/use-query-search-params';
import useUserListCaching from '@/hooks/use-user-list-caching';
import { parserParams } from '@/utils/convert';
import eventEmitter from '@/utils/eventEmitter';
import { PaginationParams } from '@/utils/pagination';

export const Plan = () => {
  const [urlParams, setUrlParams] = useQuerySearchParams();
  const _planUrlParams = parserParams(urlParams?.plan || {});

  const { changeTabAuditSchedulingTab } = useContext(MapRouteContext);

  const [paginationParams, setPaginationParams] = useState<PaginationParams>({ pageNo: 1, pageSize: 10 });
  const [form] = ProForm.useForm();
  const actionRef = useRef<ActionType>();
  const [selectKeys, setSelectKeys] = useState<Key[]>([]);
  const [selectedRows, setSelectedRows] = useState<any>([]);

  const [service, executeRequest] = Service();

  const { userListCachingRef } = useUserListCaching({ options: service?.reviewerOptions });

  const getRouteIdsByBatchDate = (date: string = dayjs().format('YYYY-MM')) => {
    executeRequest(RequestName.GetRouteIdsByBatchDate, date);
  };

  const columns = getColumns({
    searchColumns: [
      {
        title: '路线ID',
        dataIndex: 'foodSafetyNormalRouteBatchId',
        valueType: 'select',
        fieldProps: {
          options: service?.routeIds,
          showSearch: true,
        },
        colSize: 1.5,
      },
      {
        title: '状态',
        dataIndex: 'status',
        valueType: 'select',
        valueEnum: PlanStatusEnumCN,
      },
      {
        title: '稽核月份',
        dataIndex: 'batchData',
        valueType: 'dateMonth',
        colSize: 1.5,
        initialValue: dayjs().format('YYYY-MM'),
        fieldProps: {
          allowClear: false,
          onChange: (_, dateString) => {
            getRouteIdsByBatchDate(dateString);
          },
        },
      },
      getPersonColumn(
        {
          options: userListCachingRef?.current || service?.reviewerOptions,
          onFocus: () => {
            if (!userListCachingRef?.current) {
              executeRequest(RequestName.GetUserInfoList);
            }
          },
          loading: service?.reviewerLoading,
        },
        { dataIndex: 'processUserId', title: '稽核人员', colSize: 1.5 },
      ),
      {
        title: '标签状态',
        dataIndex: 'tags',
        valueType: 'select',
        valueEnum: PlanTagsEnumCN,
        fieldProps: {
          mode: 'multiple',
        },
        colSize: 1.5,
      },
    ],
    tableColumns: [
      {
        title: '路线ID',
        dataIndex: 'batchId',
        width: 300,
        render: (_, record) => {
          return (
            <a
              onClick={() => {
                changeTabAuditSchedulingTab(auditSchedulingTabsEnum.任务详情排班);
                eventEmitter.emit('GET_SCHEDULINGDESC_WITH_BATCHID', {
                  foodSafetyNormalRouteBatchId: record?.id,
                });
              }}
            >
              {record?.batchId}
            </a>
          );
        },
      },
      {
        title: '门店名称',
        dataIndex: 'planShopNames',
        width: 250,
        render: (_, record) => {
          return (
            <Tooltip title={record?.planShopNames?.join('、')}>
              <span className="cursor-pointer w-[250px] block truncate">{record?.planShopNames?.join('、')}</span>
            </Tooltip>
          );
        },
      },
      {
        title: '稽核人员',
        dataIndex: 'processUserName',
        width: 200,
      },
      {
        title: '巡检类型',
        key: 'taskSubType',
        width: 200,
        render: () => {
          return '食安线下稽核';
        },
      },
      {
        title: '路径预览',
        key: 'preview',
        render: (_, record) => {
          return <CheckRouteButton batchId={record?.batchId} />;
        },
        width: 150,
      },
      {
        title: '计划门店数',
        key: 'planShopCount',
        render: (_, record) => {
          return record?.planShopNames?.length;
        },
        width: 100,
      },
      {
        title: '状态',
        dataIndex: 'status',
        valueEnum: PlanStatusEnumCN,
        width: 150,
      },
      {
        title: '任务执行时段',
        key: 'time',
        width: 230,
        render: (_, record) => {
          return record?.beginTime && record?.endTime ? (
            <div className="flex flex-col shrink-0">
              <span>{dayjs(record?.beginTime).format('YYYY-MM-DD HH:mm:ss')}</span>
              <span>{dayjs(record?.endTime).format('YYYY-MM-DD HH:mm:ss')}</span>
            </div>
          ) : (
            '-'
          );
        },
      },
      {
        title: '标签状态',
        dataIndex: 'tags',
        width: 230,
        render: (_, record) => {
          return (
            <Space>
              {record?.tags?.map((t) => {
                return <Tag key={t}>{PlanTagsEnumCN[t]}</Tag>;
              })}
            </Space>
          );
        },
      },
      {
        title: '操作',
        valueType: 'option',
        render: (_, record) => {
          return (
            <Space className="flex">
              <Button
                size="small"
                type="link"
                onClick={() => {
                  DescModal.showModal({
                    data: {
                      batchId: record?.batchId,
                    },
                  });
                }}
              >
                查看执行详情
              </Button>
              <Permission permission={Auth.任务中心_稽核排班管理_修改批次}>
                {record?.status === PlanStatusEnum.待下发 && (
                  <Button
                    size="small"
                    type="link"
                    onClick={() => {
                      ModifyBatchModalForm.showModal({
                        data: {
                          ids: [record?.batchId],
                          onSuccess: () => {
                            actionRef.current.reload();
                            setSelectKeys([]);
                            setSelectedRows([]);
                          },
                        },
                      });
                    }}
                  >
                    编辑
                  </Button>
                )}
              </Permission>
            </Space>
          );
        },
        fixed: 'right',
      },
    ],
  });

  useEffect(() => {
    getRouteIdsByBatchDate();

    if (!isEmpty(_planUrlParams)) {
      if (_planUrlParams?.processUserId) {
        executeRequest(RequestName.GetUserInfoList);
      }

      form.setFieldsValue({
        ..._planUrlParams,
      });

      setPaginationParams({
        pageNo: _planUrlParams?.pageNo || 1,
        pageSize: _planUrlParams?.pageSize || 10,
      });
    }

    form.submit();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <>
      <ProTable
        {...getCommonConfig({
          search: {
            form,
          },
        })}
        scroll={{ x: 2500 }}
        actionRef={actionRef}
        rowKey="batchId"
        options={false}
        columns={columns}
        pagination={{
          showSizeChanger: true,
          current: paginationParams.pageNo,
          pageSize: paginationParams.pageSize,
          onChange: (pageNo, pageSize) => {
            setPaginationParams({ pageNo, pageSize });
          },
        }}
        tableExtraRender={() => (
          <ProCard>
            <Permission permission={Auth.任务中心_稽核排班管理_修改批次}>
              <Button
                type="primary"
                disabled={!selectKeys?.length}
                onClick={() => {
                  ModifyBatchModalForm.showModal({
                    data: {
                      ids: selectKeys,
                      onSuccess: () => {
                        actionRef.current.reload();
                        setSelectKeys([]);
                        setSelectedRows([]);
                      },
                    },
                  });
                }}
              >
                修改批次
              </Button>
            </Permission>
          </ProCard>
        )}
        tableRender={(_p, _d, { table }) => {
          return <ProCard>{table}</ProCard>;
        }}
        manualRequest={true}
        request={async (value) => {
          const { current, pageSize, ...val } = value;

          const payload = {
            pageNo: current || _planUrlParams?.current || 1,
            pageSize: pageSize || _planUrlParams?.pageSize || 10,
            ...val,
          };

          setUrlParams({
            ...urlParams,
            plan: {
              ...val,
              pageNo: current || _planUrlParams?.current || 1,
              pageSize: pageSize || _planUrlParams?.pageSize || 10,
            },
          });

          setPaginationParams({
            pageNo: current || _planUrlParams?.current || 1,
            pageSize: pageSize || _planUrlParams?.pageSize || 10,
          });

          const res = await executeRequest(RequestName.GetFoodSafetyNormalRouteList, payload);

          return {
            data: res?.data || [],
            success: true,
            total: res?.total,
          };
        }}
        rowSelection={{
          selectedRowKeys: selectKeys,
          type: 'checkbox',
          onChange: (selectedRowKeys, selectedRows) => {
            setSelectKeys(selectedRowKeys);
            setSelectedRows(selectedRows);
          },
          renderCell: (_, record, index, originNode) => {
            if (record?.status === PlanStatusEnum.待下发) {
              return originNode;
            }

            return null;
          },
          getCheckboxProps: (record) => ({
            disabled: ![PlanStatusEnum.待下发].includes(record?.status),
          }),
        }}
        onReset={() => {
          getRouteIdsByBatchDate();
        }}
      />
      <DescModal />
      <ModifyBatchModalForm />
    </>
  );
};
