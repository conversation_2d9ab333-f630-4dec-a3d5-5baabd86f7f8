import { StrategyTaskStatus, StrategyTaskStatusCN } from '@/constants/strategy';

export enum PlanStatusEnum {
  '待下发' = 'NOT_ISSUED',
  '待开始' = 'PENDING',
  '进行中' = 'IN_PROGRESS',
  '已结束' = 'COMPLETED',
}

export const PlanStatusEnumCN = {
  [PlanStatusEnum.待下发]: '待下发',
  [PlanStatusEnum.待开始]: '待开始',
  [PlanStatusEnum.进行中]: '进行中',
  [PlanStatusEnum.已结束]: '已结束',
};

export { StrategyTaskStatus as descStatusEnum };
export { StrategyTaskStatusCN as descStatusEnumCN };

export enum PlanTagsEnum {
  逾期进行中 = 'EXPIRED_RUNNING',
  已过期 = 'EXPIRED',
  逾期已完成 = 'EXPIRED_COMPLETED',
}

export const PlanTagsEnumCN = {
  [PlanTagsEnum.逾期进行中]: '逾期进行中',
  [PlanTagsEnum.已过期]: '已过期',
  [PlanTagsEnum.逾期已完成]: '逾期已完成',
};
