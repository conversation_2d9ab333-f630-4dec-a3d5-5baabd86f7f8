import { Empty, Tabs } from 'antd';
import { FoodAppealPage } from './FoodAppealPage';
import { Auth } from '@/constants/auth';
import useQuerySearchParams from '@/hooks/use-query-search-params';
import { usePermission } from '@/hooks/usePermission';

export default function FoodAppealIndex() {
  const hasPermission = usePermission();

  const tabs = [
    {
      key: '1',
      label: '待处理',
      children: <FoodAppealPage isWait />,
      auth: Auth.任务中心_我的任务_食安申诉任务_待审核,
    },
    {
      key: '2',
      label: '全部',
      children: <FoodAppealPage />,
      auth: Auth.任务中心_我的任务_食安申诉任务_全部,
    },
  ].filter((item) => hasPermission(item.auth));

  const [urlParams, setUrlParams] = useQuerySearchParams();

  if (!tabs?.length) {
    return <Empty description="暂无权限" />;
  }

  return (
    <Tabs
      items={tabs}
      activeKey={urlParams?.activeTab || tabs[0]?.key}
      destroyInactiveTabPane
      onChange={(key) => {
        setUrlParams({ ...urlParams, activeTab: key });
      }}
    />
  );
}
