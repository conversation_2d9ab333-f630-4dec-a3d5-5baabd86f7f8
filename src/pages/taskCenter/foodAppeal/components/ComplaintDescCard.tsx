import { useRequest } from 'ahooks';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Modal } from 'antd';
import dayjs from 'dayjs';
import { OprAction, OprActionCN, RecordModal } from './RecordModal';
import { RejectModal } from './RejectModal';
import MediaCard from '@/components/MediaCard';
import CheckItemInfo from '@/components/task-components/checklist/check-item/info';
import CheckItemResult from '@/components/task-components/checklist/check-item/result';
import ItemWrapper from '@/components/task-components/checklist/item-wrapper';
import { adjudicationAppeal } from '@/http/apis/appeal';

const { confirm } = Modal;

export const ComplaintDescCard = ({
  itemInfo,
  remark,
  isPending,
  status,
  taskId,
  baseTaskId,
  onRrefresh,
  id,
  attachments,
  createUserName,
  createTime,
  auditUserName,
  auditAttachmentIds,
  auditRemark,
  updateTime,
  isDetail,
}: any) => {
  const { runAsync: adjudicationAppealRunAsync } = useRequest(adjudicationAppeal, {
    manual: true,
    onSuccess: () => {
      // message.success('操作成功');
      onRrefresh();
    },
  });
  const passHandle = () => {
    confirm({
      title: '是否确认审核通过?',
      onOk() {
        return adjudicationAppealRunAsync({
          status: 'PASSED',
          taskId,
          baseTaskId,
          patrolReportAppealItemId: id,
        });
      },
    });
  };
  const rejectHandle = () => {
    RejectModal.showModal({
      data: {
        onFinish: async (values) => {
          await adjudicationAppealRunAsync({
            status: 'REJECTED',
            taskId,
            baseTaskId,
            patrolReportAppealItemId: id,
            ...values,
          });
        },
      },
    });
  };

  return (
    <div className="border border-solid border-[#eee] rounded-lg p-3">
      <ItemWrapper
        key={itemInfo?.worksheetItemId}
        value={JSON.stringify({
          ...itemInfo,
          worksheetItem: itemInfo?.worksheetItemSnap && JSON.parse(itemInfo?.worksheetItemSnap),
        })}
      >
        <CheckItemInfo />
        <CheckItemResult />
      </ItemWrapper>
      <Divider />

      {/* 申诉信息区域 */}
      <div className="flex flex-col gap-2">
        <RenderRecordInfoFlat
          createUserName={createUserName}
          createTime={createTime}
          remark={remark}
          attachments={attachments}
          opr={OprActionCN[OprAction.发起申诉]}
        />
        {[OprAction.审核通过, OprAction.审核驳回].includes(status) && (
          <RenderRecordInfoFlat
            createUserName={auditUserName}
            createTime={updateTime}
            remark={status === OprAction.审核驳回 ? auditRemark : undefined}
            attachments={status === OprAction.审核驳回 ? auditAttachmentIds : undefined}
            opr={OprActionCN[status]}
            remarkLabel={status === OprAction.审核驳回 ? '驳回原因' : undefined}
          />
        )}
      </div>

      {/* 操作按钮区域 */}
      <div className="mt-3 flex gap-x-3">
        {isPending ? (
          !isDetail ? (
            <>
              <StatusButton status="PASSED" currentStatus={status} onClick={passHandle} />
              <StatusButton status="REJECTED" currentStatus={status} onClick={rejectHandle} />
            </>
          ) : null
        ) : (
          <>
            {(status === 'PASSED' || status === 'REJECTED') && (
              <StatusButton status={status as 'PASSED' | 'REJECTED'} currentStatus={status} disabled />
            )}
          </>
        )}
      </div>
      <RejectModal />
      <RecordModal />
    </div>
  );
};

// 状态样式配置
const STATUS_STYLES = {
  PASSED: {
    label: '判定为合格',
  },
  REJECTED: {
    label: '维持原结果',
  },
} as const;

const StatusButton = ({ status, currentStatus, disabled = false, onClick }: any) => {
  return (
    <Button
      type={currentStatus === status ? 'primary' : 'default'}
      disabled={disabled}
      onClick={currentStatus === status ? undefined : onClick}
    >
      {STATUS_STYLES[status].label}
    </Button>
  );
};

export const InfoCard = ({
  remark,
  attachments,
  remarkLabel = '申诉理由',
}: {
  remark?: string;
  attachments?: any[];
  remarkLabel?: string;
}) => {
  if (remark || attachments?.length) {
    return (
      <div className="rounded-lg bg-[#F7F8FA] p-2">
        {remark && (
          <span className="text-sm leading-[22px] text-[#4E5969] break-all">
            <span className="text-sm font-medium leading-[22px] text-[#5B6A91]">{remarkLabel}：</span>
            {remark}
          </span>
        )}
        {!!attachments?.length && (
          <div className="flex flex-wrap gap-2 mt-2">
            {attachments?.map((item) => (
              <MediaCard file={item} key={item.id} fileList={attachments} width={60} height={60} />
            ))}
          </div>
        )}
      </div>
    );
  }

  return null;
};

const RenderRecordInfoFlat = ({
  createUserName,
  createTime,
  remark,
  attachments,
  opr,
  remarkLabel,
}: {
  createUserName: string;
  createTime: string;
  remark?: string;
  attachments?: any[];
  opr: React.ReactNode;
  remarkLabel?: string;
}) => {
  return (
    <div className="flex gap-x-2">
      <div className="flex size-6 items-center justify-center rounded-full bg-primary">
        <span className="text-[13px] font-medium leading-[22px] text-white">{createUserName?.slice(0, 1)}</span>
      </div>
      <div className="flex-1 flex flex-col gap-y-2">
        <div className="flex items-center justify-between">
          <div className="flex gap-x-1 items-center">
            <span className="text-[13px] leading-6 text-[#4E5969]">{createUserName}</span>
            {opr}
          </div>
          <span className="text-[13px] leading-[22px] text-[#4E5969]">
            {createTime ? dayjs(createTime).format('YYYY-MM-DD HH:mm') : '-'}
          </span>
        </div>
        <InfoCard remark={remark} attachments={attachments} remarkLabel={remarkLabel} />
      </div>
    </div>
  );
};
