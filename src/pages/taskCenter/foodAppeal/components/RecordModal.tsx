import { ReactNode } from 'react';
import { Timeline } from 'antd';
import dayjs from 'dayjs';
import createModal from '@/utils/antd/createModal';

interface RecordModalProps {
  record: any[];
}

export enum OprAction {
  发起申诉 = 'CREATE',
  审核通过 = 'PASSED',
  审核驳回 = 'REJECTED',
  审核作废 = 'INVALID',
  审核超时 = 'AUDIT_TIMEOUT',
  撤回申诉 = 'RECALL',
}

export const OprActionCN: Record<OprAction, ReactNode> = {
  [OprAction.发起申诉]: <span className="text-[13px] leading-[22px] text-[#4E5969]">发起申诉</span>,
  [OprAction.审核通过]: <span className="text-[13px] leading-[22px] text-[#0E42D2]">审核通过</span>,
  [OprAction.审核驳回]: <span className="text-[13px] leading-[22px] text-[#F76560]">审核驳回</span>,
  [OprAction.审核作废]: <span className="text-[13px] leading-[22px] text-[#F76560]">审核作废</span>,
  [OprAction.审核超时]: <span className="text-[13px] leading-[22px] text-[#F76560]">审核超时</span>,
  [OprAction.撤回申诉]: <span className="text-[13px] leading-[22px] text-[#F76560]">撤回申诉</span>,
};

export const RecordModal = createModal<{}, RecordModalProps>(
  (_p, setVisible, payload) => {
    const { record } = payload?.data || {};

    return [
      {
        footer: null,
      },
      <div className="pt-2">
        <Timeline
          items={record?.map((item) => {
            return {
              dot: item?.operatorName ? (
                <div className="py-1 px-2 text-xs rounded-full bg-[#1890FF] text-white">
                  {item?.operatorName?.charAt(0)}
                </div>
              ) : undefined,
              children: (
                <div className="flex flex-col gap-2">
                  <div className="flex justify-between">
                    <div className="flex gap-2">
                      <span>{item?.operatorName}</span>
                      <span>{OprActionCN?.[item?.operateAction]}</span>
                    </div>
                    <div>{item?.updateTime ? dayjs(item?.updateTime).format('YYYY-MM-DD HH:mm:ss') : '-'}</div>
                  </div>
                  {item?.extra}
                </div>
              ),
            };
          })}
        />
      </div>,
    ];
  },
  {
    title: '申诉进度',
    destroyOnClose: true,
  },
);
