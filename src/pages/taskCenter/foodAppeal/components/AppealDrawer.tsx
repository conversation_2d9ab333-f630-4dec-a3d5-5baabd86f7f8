import { useEffect, useMemo, useState } from 'react';
import { useRequest } from 'ahooks';
import { Button, Checkbox, Collapse, Divider, Drawer, message, Tabs } from 'antd';
import dayjs from 'dayjs';
import { ComplaintDescCard } from './ComplaintDescCard';
import {
  getAppealBaseInfo,
  getAppealItems,
  PatrolAppealStatus,
  PatrolAppealStatusMap,
  submitAppeal,
} from '@/http/apis/appeal';

interface AppealDrawerProps {
  open: boolean;
  onClose: () => void;
  id: number;
  taskId: number;
  baseTaskId: number;
  isDetail?: boolean;
  onRefresh?: () => void;
}

export const AppealDrawer = ({ open, onClose, taskId, baseTaskId, id, isDetail, onRefresh }: AppealDrawerProps) => {
  // 默认全部
  const [activeTab, setActiveTab] = useState<number>(-1);

  const [showUnapproved, setShowUnapproved] = useState(false);

  const [collapseActiveKey, setCollapseActiveKey] = useState<number[]>([]);

  const {
    data: appealBaseInfo,
    loading: appealBaseInfoLoading,
    refresh: refreshAppealBaseInfo,
  } = useRequest(() => getAppealBaseInfo({ taskId, baseTaskId }), {
    refreshDeps: [taskId, baseTaskId],
    ready: !!taskId && !!baseTaskId && !!open,
  }) as any;

  const {
    data: appealItems,
    loading: appealItemsLoading,
    refresh: refreshAppealItems,
  } = useRequest(() => getAppealItems({ patrolReportAppealId: id }), {
    refreshDeps: [id],
    ready: !!id && !!open,
  }) as any;

  const { tabs, workSheetListMap } = useMemo(() => {
    const _tabs: any[] = [];
    const _workSheetListMap: Map<number, any[]> = new Map();

    appealItems?.forEach((w) => {
      _workSheetListMap.set(w.worksheetId, w?.categories || []);
      _tabs.push({
        label: w.worksheetName,
        value: w.worksheetId,
        total: w?.categories?.reduce((acc, cur) => acc + (cur?.items?.length ?? 0), 0),
        filled: w?.categories?.reduce(
          (acc, cur) => acc + (cur?.items?.filter?.((i) => i.status !== PatrolAppealStatus.待审核).length ?? 0),
          0,
        ),
      });
    });

    // 处理 '全部'
    const _allCategories = appealItems?.map((w) => w?.categories)?.flatMap((c) => c) || [];

    _workSheetListMap.set(-1, _allCategories as any);
    _tabs.unshift({
      label: '全部',
      value: -1,
      total: _allCategories?.reduce((acc, cur) => acc + (cur?.items?.length ?? 0), 0),
      filled: _allCategories?.reduce(
        (acc, cur) => acc + (cur?.items?.filter?.((i) => i.status !== PatrolAppealStatus.待审核).length ?? 0),
        0,
      ),
    });

    return { tabs: _tabs, workSheetListMap: _workSheetListMap };
  }, [appealItems]);

  const { run: submitAppealRun, loading: submitAppealLoading } = useRequest(
    () =>
      submitAppeal({
        taskId,
        baseTaskId,
      }),
    {
      manual: true,
      onSuccess: () => {
        message.success('提交作成功');
        onClose?.();
        onRefresh?.();
      },
    },
  );

  const dataSource = useMemo(() => {
    return typeof activeTab === 'number' ? workSheetListMap?.get(activeTab) || [] : [];
  }, [activeTab, workSheetListMap]);

  const isPending = useMemo(() => {
    return appealBaseInfo?.status === PatrolAppealStatus.待审核;
  }, [appealBaseInfo]);

  useEffect(() => {
    setCollapseActiveKey(dataSource?.map((item) => item?.id));
  }, [dataSource]);

  const onChange = (key) => {
    setActiveTab(key);
  };

  const handleClose = () => {
    // 清空状态
    setActiveTab(-1);
    setCollapseActiveKey([]);
    onClose?.();
  };

  return (
    <Drawer
      open={open}
      onClose={handleClose}
      size="large"
      title="申诉详情"
      // loading={appealBaseInfoLoading || appealItemsLoading}
      keyboard={false}
      destroyOnClose
    >
      <div className="h-full flex flex-col gap-3">
        <div>
          <div className="flex justify-between items-center">
            <h3 className="text-base font-medium leading-7 text-[#141414]">
              {appealBaseInfo?.shopId} {appealBaseInfo?.shopName}
            </h3>
            <span>{PatrolAppealStatusMap[appealBaseInfo?.status]}</span>
          </div>
          <div className="mt-1 rounded-lg bg-[#FAFAFA] px-[9px] py-[7px] text-sm leading-6 text-[#858585] flex flex-col gap-y-1">
            <span>任务名称: {appealBaseInfo?.taskName}</span>
            <span>检查表: {appealBaseInfo?.worksheetNames?.join('、')}</span>
            <span>
              执行截止时间:{' '}
              {appealBaseInfo?.deadlineTime ? dayjs(appealBaseInfo?.deadlineTime).format('YYYY/MM/DD HH:mm:ss') : '-'}
            </span>
            {[PatrolAppealStatus.已过期, PatrolAppealStatus.已撤回, PatrolAppealStatus.已作废].includes(
              appealBaseInfo?.status,
            ) && (
              <span>
                申诉{PatrolAppealStatusMap[appealBaseInfo?.status]?.slice(1)}时间:{' '}
                {appealBaseInfo?.updateTime ? dayjs(appealBaseInfo?.updateTime).format('YYYY/MM/DD HH:mm:ss') : '-'}
              </span>
            )}
          </div>
        </div>
        <div className="flex flex-col gap-y-2 flex-1 overflow-auto">
          <div className="w-full flex justify-between items-center">
            <h3 className="text-base font-medium leading-7 text-[#141414]">任务明细</h3>
            <Checkbox
              checked={showUnapproved}
              onChange={(e) => {
                setShowUnapproved(e.target.checked);
              }}
            >
              仅显示未审核项
            </Checkbox>
          </div>
          <Tabs
            onChange={onChange}
            type="card"
            items={tabs?.map((t) => ({
              label: `${t.label} (${t.filled}/${t.total})`,
              key: t.value,
            }))}
          />
          <Collapse
            expandIconPosition="end"
            items={dataSource?.map((d) => ({
              key: d?.id,
              label: d?.name,
              children: (
                <div className="flex flex-col gap-y-2">
                  {d?.items
                    ?.filter((f) => {
                      if (showUnapproved) {
                        return f?.status === PatrolAppealStatus.待审核;
                      }

                      return true;
                    })
                    ?.map((i) => {
                      return (
                        <ComplaintDescCard
                          key={i?.id}
                          {...i}
                          isPending={isPending}
                          taskId={taskId}
                          baseTaskId={baseTaskId}
                          onRrefresh={() => {
                            refreshAppealBaseInfo();
                            refreshAppealItems();
                          }}
                          createUserName={appealBaseInfo?.createUserName}
                          createTime={appealBaseInfo?.createTime}
                          isDetail={isDetail}
                        />
                      );
                    })}
                </div>
              ),
            }))}
            activeKey={collapseActiveKey}
            onChange={(e: string[]) => {
              setCollapseActiveKey(e?.map((key) => Number(key)));
            }}
          />
        </div>
        {!isDetail && (
          <>
            <Divider style={{ margin: '12px 0' }} />
            <Button
              type="primary"
              loading={submitAppealLoading}
              onClick={submitAppealRun}
              disabled={tabs?.[0]?.filled !== tabs?.[0]?.total}
            >
              提交
            </Button>
          </>
        )}
      </div>
    </Drawer>
  );
};
