import useServiceReducer from '@/hooks/use-service-reducer';
import HttpAop from '@/http/aop';
import { exportAppealList, getPatrolAppealList, getPatrolAppealListNoPermission } from '@/http/apis/appeal';
import { getGroupTreeList, getStoreList } from '@/http/apis/center-control';
import { formatOrganizationTreeToOptions, formatShopsToOptions } from '@/utils/format';

export enum RequestName {
  GetGroupTreeList,
  GetStoreList,
  GetPatrolAppealList,
  GetPatrolAppealListNoPermission,
  ExportAppealList,
}

export const initState: any = {};

const Service = () => {
  const [service, executeRequest, dispatch] = useServiceReducer(initState, {
    [RequestName.GetGroupTreeList]: {
      request: HttpAop(getGroupTreeList, { after: [formatOrganizationTreeToOptions] }),
      afterRequest: (data, dispatch) => {
        dispatch({ organizationOptions: data });
      },
    },
    [RequestName.GetStoreList]: {
      request: HttpAop(getStoreList, { after: [formatShopsToOptions] }),
      afterRequest: (data, dispatch) => {
        dispatch({ storeOptions: data });
      },
    },
    [RequestName.GetPatrolAppealList]: {
      request: HttpAop(getPatrolAppealList),
    },
    [RequestName.GetPatrolAppealListNoPermission]: {
      request: HttpAop(getPatrolAppealListNoPermission),
    },
    [RequestName.ExportAppealList]: {
      request: HttpAop(exportAppealList),
    },
  });

  return [service, executeRequest, dispatch];
};

export default Service;
