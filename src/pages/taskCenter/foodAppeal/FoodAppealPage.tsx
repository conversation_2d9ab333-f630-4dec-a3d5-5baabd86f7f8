/* eslint-disable max-lines-per-function */
import { useEffect, useRef, useState } from 'react';
import { type ActionType, type ProColumns, ProForm, ProFormInstance, ProTable } from '@ant-design/pro-components';
import dayjs from 'dayjs';
import { AppealDrawer } from './components/AppealDrawer';
import Service, { RequestName } from './service';
import { DownloadButton } from '@/components/DownloadButton';
import { Permission } from '@/components/Permission';
import { getColumns, getStoreColumn } from '@/components/pro-table-config';
import { Auth } from '@/constants/auth';
import useQuerySearchParams from '@/hooks/use-query-search-params';
import { AppealStatus } from '@/pages/task/complaints/const';
import { parserParams } from '@/utils/convert';
import { formatDateToUTC } from '@/utils/date';
import type { PaginationParams } from '@/utils/pagination';

interface FoodAppealPageProps {
  isWait?: boolean;
}

export const FoodAppealPage = ({ isWait }: FoodAppealPageProps) => {
  const formRef = useRef<ProFormInstance>();
  const [form] = ProForm.useForm();
  const actionRef = useRef<ActionType>();
  const [service, executeRequest] = Service();
  const [params, setParams]: any = useQuerySearchParams();

  const [drawerOpen, setDrawerOpen] = useState(false);
  const [drawerIds, setDrawerIds] = useState<Record<string, any>>({});

  const [paginationParams, setPaginationParams] = useState<PaginationParams>({ pageNo: 1, pageSize: 10 });

  const formatParams = (values) => {
    const { current, pageSize, store, statuses, beginTime, endTime, taskType, ...rest } = values;

    const payload = {
      ...store,
      pageSize,
      pageNo: current,
      beginTime,
      endTime,
      statuses,
      taskType,
      ...rest,
    };

    setParams({ ...params, complaints: { ...payload } });

    return {
      ...payload,
      statuses: statuses ? [statuses] : undefined,
    };
  };

  useEffect(() => {
    executeRequest(RequestName.GetGroupTreeList);

    if (params?.complaints) {
      const { groupId, shopIds, ...rest } = parserParams(params.complaints);

      try {
        if (shopIds?.length > 0) {
          executeRequest(RequestName.GetStoreList, {
            fightGroupId: groupId,
            groupType: 2,
            privilegeCode: 1,
          });
        }
      } catch (e) {
        console.log(e);
      }

      form.setFieldsValue({
        ...rest,
        store: {
          groupId,
          shopIds,
        },
      });
    }

    form.submit();
  }, []);

  const onClickAction = (record, isDetail?: boolean) => {
    setDrawerOpen(true);
    setDrawerIds({
      id: record?.id,
      baseTaskId: record?.baseTaskId,
      taskId: record?.taskId,
      isDetail,
    });
  };

  const columns: ProColumns<any>[] = [
    ...getColumns({
      searchColumns: [
        getStoreColumn({
          organizationOptions: service?.organizationOptions,
          storeOptions: service?.storeOptions,
          onStoreFocus: (groupId: any) => {
            executeRequest(RequestName.GetStoreList, {
              fightGroupId: groupId,
              groupType: 2,
              privilegeCode: 1,
            });
          },
        }),
        {
          title: '任务状态',
          dataIndex: 'statuses',
          valueType: 'select',
          fieldProps: {
            options: [
              { label: '待审核', value: AppealStatus.发起 },
              { label: '已审核', value: AppealStatus.已提交 },
              { label: '已撤回', value: AppealStatus.撤回 },
              { label: '已过期', value: AppealStatus.已过期 },
              { label: '已作废', value: AppealStatus.已作废 },
            ],
            placeholder: '请选择任务状态',
          },
        },
        {
          title: '申诉时间',
          key: 'deadlineTime',
          valueType: 'dateRange',
          formItemProps: () => {
            return {
              rules: [
                {
                  required: true,
                  message: '请选择时间',
                },
              ],
              initialValue: [dayjs().subtract(31, 'days').endOf('day'), dayjs().endOf('day')],
            };
          },
          fieldProps: {
            disabledDate: (current, { from }) => {
              if (from) {
                return Math.abs(current.diff(from, 'days')) >= 90;
              }

              return false;
            },
          },
          colSize: 1,
          search: {
            transform: (value: any) => {
              return {
                beginTime: formatDateToUTC(dayjs(value[0]).startOf('day')),
                endTime: formatDateToUTC(dayjs(value[1]).endOf('day')),
              };
            },
          },
        },
      ],
    }),
    {
      title: '任务名称',
      dataIndex: 'taskName',
      hideInSearch: true,
      width: 200,
    },
    {
      title: '申诉提交时间',
      dataIndex: 'createTime',
      hideInSearch: true,
      valueType: 'dateTime',
      width: 200,
    },
    {
      title: '检查表',
      dataIndex: 'worksheetNames',
      hideInSearch: true,
      width: 200,
      render: (_, record) => record?.worksheetNames?.join('、'),
    },
    {
      title: '任务类型',
      dataIndex: 'taskSubType',
      valueEnum: {
        FOOD_SAFETY_VIDEO: '食安线上稽核',
        FOOD_SAFETY_NORMAL: '食安线下稽核',
      },
      width: 150,
    },
    {
      title: '门店id',
      dataIndex: 'shopId',
      hideInSearch: true,
      width: 100,
    },
    {
      title: '门店名称',
      dataIndex: 'shopName',
      hideInSearch: true,
      width: 200,
    },
    {
      title: '申诉处理时间',
      dataIndex: 'updateTime',
      hideInSearch: true,
      valueType: 'dateTime',
      width: 200,
    },
    {
      title: '申诉状态',
      dataIndex: 'status',
      hideInSearch: true,
      valueEnum: {
        [AppealStatus.发起]: {
          text: '待审核',
          color: 'orange',
        },
        [AppealStatus.审核超时转派]: {
          text: '待审核',
          color: 'orange',
        },
        [AppealStatus.通过]: {
          text: '已审核',
          color: 'green',
        },
        [AppealStatus.已提交]: {
          text: '已审核',
          color: 'green',
        },
        [AppealStatus.驳回]: {
          text: '已驳回',
          color: 'red',
        },
        [AppealStatus.撤回]: {
          text: '已撤回',
          color: 'gray',
        },
        [AppealStatus.已过期]: {
          text: '已过期',
          color: 'gray',
        },
        [AppealStatus.已作废]: {
          text: '已作废',
          color: 'gray',
        },
      },
      width: 150,
    },
    {
      title: '操作',
      key: 'operation',
      width: 150,
      fixed: 'right',
      valueType: 'option',
      render: (_v, record) => {
        return (
          <div className="flex gap-x-2">
            <Permission permission={Auth['任务中心_我的任务_食安申诉任务_查看']}>
              <a
                onClick={() => {
                  onClickAction(record, true);
                }}
              >
                查看
              </a>
            </Permission>
            {!!record?.hasPermissionAudit && [AppealStatus.发起].includes(record?.status) && isWait && (
              <Permission permission={Auth['任务中心_我的任务_食安申诉任务_审核']}>
                <a
                  onClick={() => {
                    onClickAction(record);
                  }}
                >
                  审核
                </a>
              </Permission>
            )}
          </div>
        );
      },
    },
  ];

  return (
    <>
      <ProTable
        columns={columns}
        actionRef={actionRef}
        tableClassName="pt-4"
        formRef={formRef}
        search={{
          collapsed: false,
          collapseRender: false,
          form,
        }}
        request={async (value) => {
          const res = await executeRequest(
            isWait ? RequestName.GetPatrolAppealList : RequestName.GetPatrolAppealListNoPermission,
            formatParams(value),
          );

          setPaginationParams({
            pageSize: value?.pageSize || params?.pageSize || 10,
            pageNo: value?.current || params?.current || 1,
          });

          return {
            data: res?.data,
            success: true,
            total: res?.total,
          };
        }}
        pagination={{
          showSizeChanger: true,
          current: paginationParams.pageNo,
          pageSize: paginationParams.pageSize,
        }}
        manualRequest={true}
        onChange={({ current, pageSize }) => {
          setPaginationParams((p) => ({
            ...p,
            pageNo: current ?? 1,
            pageSize: pageSize ?? 10,
          }));
        }}
        options={false}
        rowKey={'id'}
        form={{
          ignoreRules: false,
        }}
        tableExtraRender={() =>
          !isWait && (
            <div className="bg-white p-4 rounded-md flex justify-end">
              <DownloadButton
                downloadReq={() => {
                  const values = formRef.current?.getFieldsFormatValue();

                  return executeRequest(RequestName.ExportAppealList, formatParams(values));
                }}
              />
            </div>
          )
        }
      />
      <AppealDrawer
        open={drawerOpen}
        onClose={() => setDrawerOpen(false)}
        id={drawerIds?.id}
        taskId={drawerIds?.taskId}
        baseTaskId={drawerIds?.baseTaskId}
        isDetail={drawerIds?.isDetail}
        onRefresh={() => {
          form.submit();
        }}
      />
    </>
  );
};
