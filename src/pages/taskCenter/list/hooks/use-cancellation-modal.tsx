import { useRef } from 'react';
import { ProForm, ProFormProps, ProFormRadio } from '@ant-design/pro-components';
import { Modal } from 'antd';

export type ShowModalProps = {
  initialValues?: ProFormProps['initialValues'];
  onOk?: (values: any) => Promise<void>;
  /** 需要额外校验海康触发任务 */
  hasHikvisionTrigger?: boolean;
};

export enum CancellationType {
  All = 'All',
  NonExecution = 'NonExecution',
}
export const CancellationTypeCN: Record<CancellationType, string> = {
  [CancellationType.All]: '全部作废',
  [CancellationType.NonExecution]: '未执行的任务作废',
};

export const CancellationTip: Record<CancellationType, string> = {
  [CancellationType.All]:
    '任务作废后，已执行的报告作废，报告打上作废标签，不纳入数据统计，未执行的任务将打上已作废标签但不可操作，你还要继续吗？',
  [CancellationType.NonExecution]:
    '任务作废后，未执行的任务将被打上已作废标签，可查看但不可操作，已执行的报告不受影响，你还要继续吗？',
};

const useCancellationModal = () => {
  const modalRef: any = useRef();
  const [form] = ProForm.useForm();
  const tipModalRef: any = useRef();

  const showModal = ({ initialValues, onOk, hasHikvisionTrigger }: ShowModalProps) => {
    form?.setFieldsValue(initialValues);

    return (modalRef.current = Modal.confirm({
      title: '请选择作废类型',
      closable: true,
      okText: '确定',
      cancelText: '取消',
      destroyOnClose: true,
      icon: null,
      content: (
        <ProForm submitter={false} form={form} layout="horizontal" className="mt-4">
          <ProForm.Item name="batchId" hidden />
          <ProForm.Item name="taskType" hidden />
          <ProForm.Item name="taskSubType" hidden />
          <ProForm.Item name="nodeId" hidden />
          <ProFormRadio.Group
            name="type"
            // layout="vertical"
            rules={[
              {
                required: true,
                message: '请选择作废类型',
              },
            ]}
            options={Object.keys(CancellationTypeCN).map((key) => ({
              value: key,
              label: CancellationTypeCN[key],
            }))}
          />
        </ProForm>
      ),
      onOk: async () => {
        return await form?.validateFields().then(() => {
          const type = form.getFieldValue('type');
          const values = form?.getFieldsValue();

          return (tipModalRef.current = Modal.confirm({
            title: '确定要作废任务吗',
            closable: true,
            destroyOnClose: true,
            okText: '确定',
            cancelText: '取消',
            content: CancellationTip[type],
            onOk: () => {
              const { type, batchId, taskType, taskSubType, nodeId } = values;

              onOk?.({
                batchId,
                taskType,
                taskSubType,
                isAllCancel: type === CancellationType.All,
                nodeId,
                // 如果是海康任务,选择了全部作废,则需要额外传一个参数(true)
                hasCancelBatchAll: hasHikvisionTrigger && type === CancellationType.All ? true : undefined,
              });

              form?.resetFields();
            },
            onCancel: () => {
              form?.resetFields();
            },
          }));
        });
      },
      onCancel: () => {
        form?.resetFields();
      },
    }));
  };

  return { showModal };
};

export default useCancellationModal;
