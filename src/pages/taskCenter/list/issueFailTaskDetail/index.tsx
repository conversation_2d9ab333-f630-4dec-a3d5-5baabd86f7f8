import { useEffect } from 'react';
import { ProColumns, ProTable } from '@ant-design/pro-components';
import Service, { RequestName } from '../service';
import useQuerySearchParams from '@/hooks/use-query-search-params';
import { getErrorTaskList } from '@/http/apis/task-center';

export default function IssueFailTaskDetail() {
  const [service, executeRequest] = Service();
  const [searchParams] = useQuerySearchParams();

  useEffect(() => {
    executeRequest(RequestName.GetStoreList, {
      groupType: 2,
      privilegeCode: 1,
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const columns: ProColumns[] = [
    {
      title: '门店编号',
      dataIndex: 'shopId',
      hideInSearch: true,
    },
    {
      title: '门店名称',
      dataIndex: 'shopId',
      hideInTable: true,
      valueType: 'select',
      fieldProps: {
        options: service?.storeOptions,
        showSearch: true,
      },
    },
    {
      title: '门店名称',
      dataIndex: 'shopName',
      hideInSearch: true,
    },
    {
      title: '失败原因',
      dataIndex: 'reason',
      hideInSearch: true,
    },
  ];

  return (
    <ProTable
      options={false}
      // search={false}
      columns={columns}
      request={async ({ current, pageSize, shopId }) => {
        const res = await getErrorTaskList({ id: searchParams?.id, shopId, pageNo: current, pageSize });

        return {
          data: res?.data ?? [],
          total: res?.total ?? 0,
          success: true,
        };
      }}
    />
  );
}
