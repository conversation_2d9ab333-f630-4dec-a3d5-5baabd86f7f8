import { FC, ReactNode, useEffect, useRef, useState } from 'react';
import { ActionType, ProCard, ProForm, ProTable } from '@ant-design/pro-components';
import { Button } from 'antd';
import dayjs from 'dayjs';
import Service, { RequestName } from './service';
import { getTableColumns } from './table-config';
import { DateQueryType, DateType } from '@/components/date-query-select';
import PreviousPageAndNextPageButton from '@/components/PreviousPageAndNextPage/Button';
import {
  getChecklistColumn,
  getColumns,
  getCommonConfig,
  getPersonWithStaffCodeColumn,
  getReportPassedColumn,
  getReportStatusColumn,
  getStoreColumn,
  getStoreTypeColumn,
  onReset,
} from '@/components/pro-table-config';
import getDateQueryColumn from '@/components/pro-table-config/date-query';
import { getTaskTemplateColumn } from '@/components/pro-table-config/task-template';
import TaskDrawer, { ShowTaskType } from '@/components/task-drawer';
import { ChecklistStrategyType } from '@/constants/checklist-strategy';
import { NotFilledItemHandleType } from '@/constants/strategy';
import { TemplateStatus } from '@/constants/template';
import useQuerySearchParams from '@/hooks/use-query-search-params';
import { RouteKey } from '@/router/config';
import useGlobalNavigate from '@/router/useGlobalNavigate';
import { formatDateToUTC } from '@/utils/date';
import { formatQueryDate } from '@/utils/format';
import { PaginationParams } from '@/utils/pagination';
import { formatUrlObjToObject } from '@/utils/syncToUrl';

// eslint-disable-next-line max-lines-per-function
const Self: FC = () => {
  const [form] = ProForm.useForm();
  const [params, setParams] = useQuerySearchParams();
  const actionRef = useRef<ActionType>();
  const [paginationParams, setPaginationParams] = useState<PaginationParams>(() => ({
    pageNo: 1,
    pageSize: 20,
  }));
  const [hasNextPage, setHasNextPage] = useState<boolean>(true);
  const commonConfig = getCommonConfig({
    search: {
      form,
    },
  });
  const [service, executeRequest] = Service();
  const [drawerProps, setDrawerProps] = useState<{
    open: boolean;
    taskId?: string | number;
    showType?: ShowTaskType;
    title?: string;
    needReviewSummary?: boolean;
    needStatistics?: boolean;
    needExam?: boolean;
    hasReview?: boolean;
    reviewSumUp?: string; // 点评总结
    notFilledItemHandleType?: NotFilledItemHandleType;
    footer?: ReactNode;
  }>({
    open: false,
  });
  const navigate = useGlobalNavigate();

  useEffect(() => {
    executeRequest(RequestName.GetGroupTreeList);
    requestChecklist();
    executeRequest(RequestName.GetTags);
    executeRequest(RequestName.GetUserInfoList);
    executeRequest(RequestName.QueryBaseTemplateList, {
      status: TemplateStatus.EFFECTIVE,
      type: ChecklistStrategyType.SELF,
    });

    if (params?.store) {
      const { groupId, shopIds } = JSON.parse(params?.store);

      if (groupId || shopIds) {
        executeRequest(RequestName.GetStoreList, {
          fightGroupId: groupId,
          groupType: 2,
          privilegeCode: 1,
        });
      }
    }
  }, []);

  const requestChecklist = (labelIds?: number[]) => {
    return executeRequest(RequestName.GetChecklistSimplelist, {
      labelIds,
      worksheetTypes: [ChecklistStrategyType.SELF, ChecklistStrategyType.VALIDITY],
    });
  };

  const formatParams = (params) => {
    const { store, shopType, current, pageSize, sheet, status, passed, dateQuery, templateId, pageNo, ...restProps } =
      params;

    const { startDate, endDate } = formatQueryDate(dateQuery);

    const timeParmas = {
      1: {
        startDate: formatDateToUTC(startDate),
        endDate: formatDateToUTC(endDate),
      },
      2: {
        submitStartTime: formatDateToUTC(startDate),
        submitEndTime: formatDateToUTC(endDate),
      },
    };

    return {
      ...store,
      ...restProps,
      shopType,
      pageSize,
      pageNo: pageNo || current || 1,
      worksheetId: sheet?.checklist,
      status,
      passed,
      ...timeParmas[dateQuery?.type],
      taskType: 'SELF',
      templateId,
      hasCount: false,
    };
  };

  const columns = getColumns({
    searchColumns: [
      getStoreColumn({
        organizationOptions: service?.organizationOptions,
        storeOptions: service?.storeOptions,
        onStoreFocus: (groupId: any) => {
          executeRequest(RequestName.GetStoreList, {
            fightGroupId: groupId,
            groupType: 2,
            privilegeCode: 1,
          });
        },
      }),
      getStoreTypeColumn(null, { dataIndex: 'shopType' }),
      getChecklistColumn({
        tagOptions: service?.tagOptions,
        checklistOptions: service?.checklistOptions,
        onTagChange: (value) => {
          requestChecklist(value);
        },
      }),
      getDateQueryColumn({
        typeOptions: [
          {
            value: DateQueryType.TaskDeliveryDate,
            label: '基础任务下发日期',
          },
          {
            value: DateQueryType.ReportSubmissionTime,
            label: '报告提交日期',
          },
        ],
      }),
      getTaskTemplateColumn({ options: service?.templateOptions }, { dataIndex: 'templateId' }),
      // getTaskTypeColumn({}, { dataIndex: 'taskType' }),
      getReportStatusColumn(
        {
          valueEnum: {
            REVIEWED: '已点评',
            NOT_REVIEWED: '待点评',
            NO_NEEDED_REVIEWS: '无需点评',
            REVIEWED_EXPIRE: '点评超时',
          },
        },
        { dataIndex: 'reviewStatus', title: '点评状态' },
      ),
      getReportPassedColumn(null, { title: '报告是否通过', dataIndex: 'passed' }),
      getPersonWithStaffCodeColumn(),
    ],
    tableColumns: getTableColumns({
      onReview: (record) => {
        setDrawerProps({
          open: true,
          taskId: record?.baseTaskId,
          showType: ShowTaskType.Review,
          needReviewSummary: true,
          title: '任务点评',
          hasReview: record?.hasNeedReview,
        });
      },
      onView: async (record) => {
        setDrawerProps({
          open: true,
          taskId: record?.baseTaskId,
          showType: ShowTaskType.Detail,
          needReviewSummary: true,
          needStatistics: true,
          title: '任务详情',
        });
      },
      onIssueCount: (record) => {
        navigate(RouteKey.RCRTrack, {
          searchParams: { baseTaskId: record?.baseTaskId },
        });
      },
    }),
  });

  return (
    <>
      <ProTable
        {...commonConfig}
        columns={columns}
        scroll={{ x: 2000 }}
        params={paginationParams}
        actionRef={actionRef}
        tableRender={(props: any, _d, { table }) => {
          return (
            <ProCard
            // extra={
            //   <Button
            //     type="primary"
            //     onClick={() => {
            //       const values = form.getFieldsValue();

            //       executeRequest(RequestName.ExportSelfReport, formatParams(values)).then(() => {
            //         layoutStore.increaseExport();
            //       });
            //     }}
            //   >
            //     下载明细
            //   </Button>
            // }
            >
              {table}
              <PreviousPageAndNextPageButton
                pageNum={paginationParams.pageNo}
                pageSize={paginationParams.pageSize}
                dataLength={props?.action?.dataSource?.length ?? 0}
                loading={props?.action?.loading}
                onChange={({ pageNum, pageSize }) => {
                  setPaginationParams((p) => ({
                    ...p,
                    pageNo: pageNum,
                    pageSize,
                  }));
                }}
              />
            </ProCard>
          );
        }}
        onSubmit={() => {
          setPaginationParams((p) => ({
            ...p,
            pageNo: 1,
          }));
        }}
        form={{
          syncToUrl: (values, type) => {
            // 主动排查hikUserId
            const { hikUserId, ...rest } = values;

            if (type === 'get') {
              const formatValues = formatUrlObjToObject(values);
              const { passed, dateQuery } = formatValues;

              return {
                ...formatValues,
                passed: passed?.toString(),
                dateQuery: dateQuery
                  ? {
                      ...dateQuery,
                      date: dateQuery?.date
                        ? Array.isArray(dateQuery?.date)
                          ? [dayjs(dateQuery?.date?.[0]), dayjs(dateQuery?.date?.[1])]
                          : dayjs(dateQuery?.date)
                        : undefined,
                    }
                  : {
                      date: dayjs().add(-1, 'days'),
                      type: DateQueryType.TaskDeliveryDate,
                      dateType: DateType.Yesterday,
                    },
              };
            }

            return {
              ...rest,
            };
          },
        }}
        pagination={false}
        onReset={() => {
          setPaginationParams((p) => ({
            ...p,
            pageNo: 1,
          }));
          onReset(form, {
            dateQuery: {
              date: dayjs().add(-1, 'days'),
              type: DateQueryType.TaskDeliveryDate,
              dateType: DateType.Yesterday,
            },
          });
        }}
        request={async (values) => {
          console.log(values, '=values123');

          const res = await executeRequest(RequestName.GetReportCenterList, formatParams(values));

          setHasNextPage((res?.data?.length || 0) < paginationParams.pageSize);
          console.log(res, '=res');

          return {
            data: res?.data,
            total: res?.total,
            success: true,
          };
        }}
      />
      <TaskDrawer
        destroyOnClose={true}
        width={650}
        title={drawerProps?.title}
        open={drawerProps?.open}
        onClose={() => setDrawerProps({ open: false })}
        showType={drawerProps?.showType}
        taskId={drawerProps?.taskId}
        needReviewSummary={drawerProps?.needReviewSummary}
        needStatistics={drawerProps?.needStatistics}
        hasReview={drawerProps?.hasReview}
        reviewSumUp={drawerProps?.reviewSumUp}
        notFilledItemHandleType={drawerProps?.notFilledItemHandleType}
        footer={drawerProps?.footer}
        showOperationRecord
        onPreview={({ reviewSumUp, notFilledItemHandleType }) => {
          setDrawerProps({
            ...drawerProps,
            showType: ShowTaskType.Detail,
            needStatistics: true,
            needReviewSummary: true,
            reviewSumUp,
            notFilledItemHandleType,
            footer: (
              <Button
                type="primary"
                className="w-full"
                onClick={async () => {
                  if (drawerProps?.reviewSumUp) {
                    await executeRequest(RequestName.SubmitReviewSummary, {
                      taskId: drawerProps?.taskId,
                      summary: drawerProps?.reviewSumUp,
                    });
                  }

                  await executeRequest(RequestName.SubmitReviewTask, {
                    baseTaskId: drawerProps?.taskId,
                    notFilledItemHandleType: notFilledItemHandleType || NotFilledItemHandleType.SET_FULL_SCORE,
                  });
                  setDrawerProps({
                    open: false,
                  });
                  actionRef.current.reload();
                }}
              >
                提交点评
              </Button>
            ),
          });
        }}
        saveReviewItem={(dto) => {
          return executeRequest(RequestName.SaveReviewItem, dto);
        }}
      />
    </>
  );
};

export default Self;
