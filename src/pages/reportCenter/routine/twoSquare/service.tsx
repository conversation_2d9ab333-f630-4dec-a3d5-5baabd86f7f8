import useServiceReducer from '@/hooks/use-service-reducer';
import HttpAop from '@/http/aop';
import { getGroupTreeList, getStoreList } from '@/http/apis/center-control';
import {
  exportTwoSquareReport,
  getTwoSquarePatrolReportList,
  saveReviewItem,
  submitReviewSummary,
  submitReviewTask,
} from '@/http/apis/report-center';
import { formatOrganizationTreeToOptions, formatShopsToOptions } from '@/utils/format';

export enum RequestName {
  GetGroupTreeList,
  GetStoreList,
  exportReport,
  SubmitReviewSummary,
  SubmitReviewTask,
  SaveReviewItem,
  GetTwoSquarePatrolReportList,
}

const Service = () => {
  const [service, executeRequest, dispatch] = useServiceReducer(
    {},
    {
      [RequestName.GetGroupTreeList]: {
        request: HttpAop(getGroupTreeList, { after: [formatOrganizationTreeToOptions] }),
        afterRequest: (data, dispatch) => {
          dispatch({ organizationOptions: data });
        },
      },
      [RequestName.GetStoreList]: {
        request: HttpAop(getStoreList, {
          after: [formatShopsToOptions],
        }),
        afterRequest: (data, dispatch) => {
          dispatch({ storeOptions: data });
        },
      },
      [RequestName.SubmitReviewSummary]: {
        request: submitReviewSummary,
      },
      [RequestName.SubmitReviewTask]: {
        request: submitReviewTask,
      },
      [RequestName.SaveReviewItem]: {
        request: saveReviewItem,
      },
      [RequestName.GetTwoSquarePatrolReportList]: {
        request: getTwoSquarePatrolReportList,
      },
      [RequestName.exportReport]: {
        request: exportTwoSquareReport,
      },
    },
  );

  return [service, executeRequest, dispatch];
};

export default Service;
