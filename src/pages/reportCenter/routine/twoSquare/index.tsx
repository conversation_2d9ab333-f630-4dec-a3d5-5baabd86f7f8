import { ReactNode, useEffect, useRef, useState } from 'react';
import { ActionType, ProForm, ProTable } from '@ant-design/pro-components';
import { Button } from 'antd';
import dayjs from 'dayjs';
import Service, { RequestName } from './service';
import { DateQueryType, DateType } from '@/components/date-query-select';
import { DownloadButton } from '@/components/DownloadButton';
import {
  getColumns,
  getCommonConfig,
  getInspectionTypeColumn,
  getReportPassedColumn,
  getReportStatusColumn,
  getStoreColumn,
  getStoreTypeColumn,
} from '@/components/pro-table-config';
import getDateQueryColumn from '@/components/pro-table-config/date-query';
import TaskDrawer, { ShowTaskType } from '@/components/task-drawer';
import { NotFilledItemHandleType, StrategyPatrolType, StrategyPatrolTypeCN, TaskSubTypeCN } from '@/constants/strategy';
import { InspectionType } from '@/constants/task';
import useQuerySearchParams from '@/hooks/use-query-search-params';
import { formatDateToUTC } from '@/utils/date';
import { formatQueryDate } from '@/utils/format';

const formatParams = (params) => {
  const { current, pageSize, store, dateQuery, ...rest } = params;
  const { startDate, endDate } = formatQueryDate(dateQuery);

  const dateQueryMap = {
    [DateQueryType.TaskDeliveryDate]: {
      startDate: formatDateToUTC(startDate),
      endDate: formatDateToUTC(endDate),
    },
    [DateQueryType.ReportSubmissionTime]: {
      submitStartTime: formatDateToUTC(startDate),
      submitEndTime: formatDateToUTC(endDate),
    },
    [DateQueryType.BeginpatrolDate]: {
      taskStartTime: formatDateToUTC(startDate),
      taskEndTime: formatDateToUTC(endDate),
    },
  };

  return {
    ...store,
    ...rest,
    pageNo: current ?? 1,
    pageSize: pageSize ?? 20,
    ...(dateQueryMap[dateQuery?.type] || {}),
  };
};

export default function TwoSquare() {
  const [service, executeRequest] = Service();
  const [searchParams, setSearchParams] = useQuerySearchParams();
  const [form] = ProForm.useForm();

  const actionRef = useRef<ActionType>();
  const [drawerProps, setDrawerProps] = useState<{
    open: boolean;
    taskId?: string | number;
    showType?: ShowTaskType;
    title?: string;
    needReviewSummary?: boolean;
    needStatistics?: boolean;
    needExam?: boolean;
    hasReview?: boolean;
    reviewSumUp?: string; // 点评总结
    notFilledItemHandleType?: NotFilledItemHandleType;
    footer?: ReactNode;
  }>({
    open: false,
  });

  const commonConfig = getCommonConfig({
    search: {
      form,
    },
  });

  useEffect(() => {
    executeRequest(RequestName.GetGroupTreeList);

    if (searchParams?.store) {
      const { groupId, shopIds } = searchParams.store;

      if (groupId || shopIds) {
        executeRequest(RequestName.GetStoreList, {
          fightGroupId: groupId,
          groupType: 2,
          privilegeCode: 1,
        });
      }
    }
  }, []);

  useEffect(() => {
    if (Object.keys(searchParams || {})?.length) {
      form.setFieldsValue(searchParams);
    }
  }, [form, searchParams]);

  const columns = getColumns({
    searchColumns: [
      getStoreColumn({
        organizationOptions: service?.organizationOptions,
        storeOptions: service?.storeOptions,
        onStoreFocus: (groupId: any) => {
          executeRequest(RequestName.GetStoreList, {
            fightGroupId: groupId,
            groupType: 2,
            privilegeCode: 1,
          });
        },
      }),
      getStoreTypeColumn({}, { dataIndex: 'shopType' }),
      getDateQueryColumn({
        typeOptions: [
          {
            value: DateQueryType.TaskDeliveryDate,
            label: '基础任务下发日期',
          },
          /* {
            value: DateQueryType.ReportSubmissionTime,
            label: '报告提交日期',
          },
          {
            value: DateQueryType.BeginpatrolDate,
            label: '开始巡检时间',
          }, */
        ],
        limit: 90,
      }),
      getInspectionTypeColumn(
        {
          options: Object.keys(StrategyPatrolTypeCN)
            .filter((key) => key !== StrategyPatrolType.FOOD_SAFETY_ARRIVE_SHOP)
            .map((key) => ({
              label: StrategyPatrolTypeCN[key as keyof typeof StrategyPatrolTypeCN],
              value: key as keyof typeof StrategyPatrolTypeCN,
            })),
        },
        { dataIndex: 'taskSubType' },
      ),
      getReportStatusColumn(
        {
          valueEnum: {
            WAITING_CONFIRM: '待确认',
            CONFIRMED: '已确认',
            CANCELED: '已作废',
          },
        },
        { dataIndex: 'reportStatus', title: '报告状态' },
      ),
      getReportPassedColumn(),
    ],
    tableColumns: [
      {
        title: '任务名称',
        dataIndex: 'taskName',
      },
      {
        title: '任务类型',
        dataIndex: 'taskType',
        valueEnum: TaskSubTypeCN,
      },
      {
        title: '巡检类型',
        dataIndex: 'taskSubType',
        valueEnum: {
          [InspectionType.NORMAL]: '到店巡检',
          [InspectionType.VIDEO]: '视频云巡检',
          [InspectionType.FOOD_SAFETY_NORMAL]: '食安线下稽核',
          [InspectionType.FOOD_SAFETY_VIDEO]: '食安线上稽核',
          [InspectionType.DIAGNOSTIC]: '诊断巡检',
          [InspectionType.FOOD_SAFETY_ARRIVE_SHOP]: '食安稽核到店辅导',
          [InspectionType.DIFFERENCE_ITEM_ARRIVE_SHOP]: '差异项到店巡检',
          HANDOVER: '交接任务',
        },
      },
      {
        title: '门店',
        dataIndex: 'shopName',
      },
      {
        title: '门店类型',
        dataIndex: 'shopType',
        render: (_, record) => (record.shopType === 'JOIN' ? '加盟M' : '加盟T'),
      },
      {
        title: '报告状态',
        dataIndex: 'reportStatus',
        valueEnum: {
          WAITING_SUBMIT: '待提交',
          WAITING_CONFIRM: '待确认',
          CONFIRMED: '已确认',
          CANCELED: '已作废',
        },
      },
      {
        title: '报告是否通过',
        dataIndex: 'passed',
        valueEnum: {
          true: '通过',
          false: '不通过',
        },
      },
      {
        title: '操作',
        render: (_, record) => (
          <a
            onClick={() => {
              setDrawerProps({
                open: true,
                taskId: record?.baseTaskId,
                showType: ShowTaskType.Detail,
                needReviewSummary: true,
                needStatistics: true,
                title: '任务详情',
              });
            }}
          >
            查看详情
          </a>
        ),
      },
    ],
  });

  return (
    <>
      <ProTable
        {...commonConfig}
        columns={columns}
        actionRef={actionRef}
        tableExtraRender={({ pagination }) => (
          <div className="flex justify-end bg-white py-3 px-5 rounded">
            <DownloadButton
              disabled={typeof pagination === 'object' && !pagination?.total}
              downloadReq={() => {
                const values = form.getFieldsValue();

                return executeRequest(RequestName.exportReport, formatParams(values));
              }}
            />
          </div>
        )}
        request={async (values) => {
          const formData = { ...form.getFieldsValue(), ...values };

          const params = formatParams({
            ...formData,
            dateQuery: formData?.dateQuery ?? {
              date: dayjs().add(-1, 'days'),
              type: DateQueryType.TaskDeliveryDate,
              dateType: DateType.Yesterday,
            },
          });

          const res = await executeRequest(RequestName.GetTwoSquarePatrolReportList, params);

          setSearchParams({
            ...searchParams,
            ...params,
            store: formData?.store,
          });

          return {
            success: true,
            data: res?.data ?? [],
            total: res?.total ?? 0,
          };
        }}
      />
      <TaskDrawer
        destroyOnClose={true}
        width={650}
        title={drawerProps?.title}
        open={drawerProps?.open}
        onClose={() => setDrawerProps({ open: false })}
        showType={drawerProps?.showType}
        taskId={drawerProps?.taskId}
        needReviewSummary={drawerProps?.needReviewSummary}
        needStatistics={drawerProps?.needStatistics}
        hasReview={drawerProps?.hasReview}
        reviewSumUp={drawerProps?.reviewSumUp}
        notFilledItemHandleType={drawerProps?.notFilledItemHandleType}
        footer={drawerProps?.footer}
        showOperationRecord
        onPreview={({ reviewSumUp, notFilledItemHandleType }) => {
          setDrawerProps({
            ...drawerProps,
            showType: ShowTaskType.Detail,
            needStatistics: true,
            needReviewSummary: true,
            reviewSumUp,
            notFilledItemHandleType,
            footer: (
              <Button
                type="primary"
                className="w-full"
                onClick={async () => {
                  if (drawerProps?.reviewSumUp) {
                    await executeRequest(RequestName.SubmitReviewSummary, {
                      taskId: drawerProps?.taskId,
                      summary: drawerProps?.reviewSumUp,
                    });
                  }

                  await executeRequest(RequestName.SubmitReviewTask, {
                    baseTaskId: drawerProps?.taskId,
                    notFilledItemHandleType: notFilledItemHandleType || NotFilledItemHandleType.SET_FULL_SCORE,
                  });
                  setDrawerProps({
                    open: false,
                  });
                  actionRef.current.reload();
                }}
              >
                提交点评
              </Button>
            ),
          });
        }}
        saveReviewItem={(dto) => {
          return executeRequest(RequestName.SaveReviewItem, dto);
        }}
      />
    </>
  );
}
