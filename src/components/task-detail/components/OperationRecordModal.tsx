import { IUseModalResult } from '@tastien/thooks/lib/useModal';
import { useRequest } from 'ahooks';
import { Modal, Timeline } from 'antd';
import dayjs from 'dayjs';
import { StrategyOperationActionCN } from '@/constants/strategy';
import { getStragegyOperationLog } from '@/http/apis/strategy';

export default function OperationRecordModal({ visible, closeModal, initValue }: IUseModalResult<{ taskId: any }>) {
  const { data = [] } = useRequest(
    async () => {
      const res = await getStragegyOperationLog(initValue?.taskId);

      return res || [];
    },
    {
      ready: !!initValue?.taskId,
    },
  );

  return (
    <Modal
      title="操作记录"
      open={visible}
      onCancel={closeModal}
      style={{ margin: '0 0 0 auto', top: 0, padding: 0 }}
      width={450}
      maskClosable={false}
      footer={null}
    >
      <div className="pt-2" style={{ minHeight: 'calc(100vh - 103px' }}>
        <Timeline className="mt-2 ml-1">
          {data?.map(({ operatorName, updateTime, operateAction, remark }, index: number) => {
            return (
              <Timeline.Item key={index}>
                {operatorName} {!!updateTime && dayjs(updateTime).format('MM-DD HH:mm')}{' '}
                {StrategyOperationActionCN?.[operateAction]}
                {!!remark?.length && <div className="mt-1">{remark}</div>}
              </Timeline.Item>
            );
          })}
        </Timeline>
      </div>
    </Modal>
  );
}
