import { FC, ReactNode, useCallback, useMemo, useState } from 'react';
import { useModal } from '@tastien/thooks';
import { Checkbox, Result, Spin } from 'antd';
import cn from 'classnames';
import { isBoolean, isNil } from 'lodash';
import OperationRecordModal from './components/OperationRecordModal';
import DiagnosticScore from '../strategy-task-detail/diagnostic-score';
import Checklist from '../task-components/checklist';
import CheckItemInfo from '../task-components/checklist/check-item/info';
import CheckItemResult from '../task-components/checklist/check-item/result';
import useChecklistData from '../task-components/checklist/hooks/use-checklist-data';
import useChecklistOperate from '../task-components/checklist/hooks/use-checklist-operate';
import ItemWrapper from '../task-components/checklist/item-wrapper';
import ReviewItemResult from '../task-components/checklist/review-item/result';
import ExamDetail from '../task-components/exam-detail';
import TaskInfo from '../task-components/info';
import TaskInfoBody from '../task-components/info/body';
import TaskPage from '../task-components/page';
import SumUpResult from '../task-components/sum-up/result';
import Summary from '../task-components/summary';
import { filterReportNotReviewed, filterUnqualified } from '../task-components/utils/filter';
import { verifyCheckItem } from '../task-components/utils/verify';
import { NotFilledItemHandleType, StrategyRoutineType, StrategyTaskType } from '@/constants/strategy';
import { DiagnosisTypeCN } from '@/constants/task';
import useTaskDetail from '@/hooks/use-task-detail';
import { useQueryReportAppealInfo } from '@/http/apis/appeal';
import { userStore } from '@/mobx';
import useReportedItem from '@/pages/taskCenter/cloud/hooks/use-reported-item';

export interface TaskDetailProps {
  taskId: string | number;
  needReviewSummary?: boolean;
  needStatistics?: boolean;
  needExam?: boolean;
  reviewSumUp?: string; // 预览的点评总结
  patrolSumUp?: string; // 预览的巡检总结
  notFilledItemHandleType?: NotFilledItemHandleType;
  footer?: ReactNode;
  placement?: 'left' | 'right';
  newWindow?: boolean;
  renderHeader?: () => ReactNode;
  modalWidth?: number;
  /** 是否显示操作记录 */
  showOperationRecord?: boolean;
  reportModalStyle?: any;
}

const TaskDetail: FC<TaskDetailProps> = ({
  taskId,
  needReviewSummary,
  needStatistics,
  needExam,
  reviewSumUp,
  notFilledItemHandleType,
  footer,
  patrolSumUp,
  placement,
  newWindow,
  renderHeader,
  modalWidth,
  showOperationRecord,
  reportModalStyle,
}) => {
  const [showUnqualified, setShowUnqualified] = useState<boolean>(false);
  const [showNotReview, setShowNotReview] = useState<boolean>(false);
  const { detail } = useTaskDetail({ taskId, needReviewSummary, needStatistics, needExam, notFilledItemHandleType });
  const filterFn = useCallback(() => {
    if (showNotReview && showUnqualified) {
      return (item: any) => filterUnqualified(item) || filterReportNotReviewed(item);
    }

    if (showUnqualified) {
      return filterUnqualified;
    }

    if (showNotReview) {
      return (item: any) => filterReportNotReviewed(item);
    }

    return undefined;
  }, [showNotReview, showUnqualified]);
  const { worksheetMap, worksheetOptions, worksheetCategoryMap } = useChecklistData({
    worksheets: detail?.worksheets,
    filter: filterFn(),
    progressCheck: verifyCheckItem,
  });
  const { activeWorksheetId, setActiveWorksheetId, activeCategoryId, setActiveCategoryId, switchCategory } =
    useChecklistOperate({ worksheetMap, worksheetOptions });

  const { info, roleType } = userStore;

  const { judgeComponent, sopDrawerComponent } = useReportedItem({
    reportItemFlag: detail?.info?.reportItemFlag,
    baseTaskId: detail?.info?.taskId,
    configId: 1,
    isSumbit: isBoolean(detail?.info?.hasReportItem),
    secondReportItems: detail?.info?.reportItems,
    isReport: true,
    modalStyle: reportModalStyle,
    drawerPlacement: placement,
    newWindow,
    secondActionContent: detail?.info?.reportItemConfigDetail?.secondActionContent,
  });
  const operationRecordProps = useModal<any>();

  // 是否需要申诉
  const needAppeal = useMemo(() => {
    // 只有公司员工才需要
    if (roleType !== 'CORP') {
      return false;
    }

    return (
      (detail?.info?.taskType === StrategyTaskType.SELF && detail?.info?.reportStatus === 'CONFIRMED') ||
      detail?.info?.taskType === StrategyTaskType.PATROL
    );
  }, [detail?.info?.reportStatus, detail?.info?.taskType, roleType]);

  const { data: reportAppealInfoData } = useQueryReportAppealInfo({
    queryKey: ['reportAppealInfo', needAppeal, detail?.info?.taskId],
    params: {
      id: detail.info.taskId,
      onlyMeCanAppeal: false,
    },
    enabled: needAppeal && !!detail?.info?.taskId,
  });

  if (!detail?.loading && detail?.pageError) {
    return <Result title="数据异常，请稍后再试" status="500" />;
  }

  return (
    <TaskPage className="overflow-auto">
      {isBoolean(detail?.info?.passed) && (
        <div
          className={cn(
            'w-full flex justify-center py-[9px] text-xs',
            detail?.info?.passed ? 'bg-[#E8FFEA] text-[#23C343]' : 'bg-[#FFECE8] text-[#F76560]',
          )}
        >
          <span role="img" aria-label="celebration">
            {detail?.info?.passed
              ? `🎉${{ SELF: '自检', PATROL: '巡检' }[detail?.info?.taskType]}报告合格`
              : `${{ SELF: '自检', PATROL: '巡检' }[detail?.info?.taskType]}报告不合格`}
          </span>
        </div>
      )}
      {detail?.loading ? (
        <div className="h-full flex justify-center items-center ">
          <Spin tip="报告加载中......" size="large">
            <div className="w-[200px] h-[200px]" />
          </Spin>
        </div>
      ) : (
        <>
          {judgeComponent}
          {renderHeader ? (
            renderHeader()
          ) : (
            <TaskInfo
              data={detail?.info}
              extra={
                showOperationRecord ? (
                  <a
                    className="text-primary"
                    onClick={async () => {
                      operationRecordProps.openModal({ taskId });
                    }}
                  >
                    操作记录
                  </a>
                ) : undefined
              }
              renderHeader={() =>
                detail?.info?.taskSubType === StrategyRoutineType.DIAGNOSTIC &&
                (detail?.diagnosticInfo?.alarmLevel ? DiagnosisTypeCN[detail?.diagnosticInfo?.alarmLevel] : '-')
              }
              renderBody={() =>
                detail?.diagnosticInfo?.weightScore && <DiagnosticScore data={detail?.diagnosticInfo} />
              }
            >
              <TaskInfoBody data={detail?.info} />
            </TaskInfo>
          )}

          {needStatistics &&
            detail?.statistics?.itemCount > 0 &&
            ![
              StrategyRoutineType.DIAGNOSTIC,
              StrategyRoutineType.FOOD_SAFETY_ARRIVE_SHOP,
              StrategyRoutineType.DIFFERENCE_ITEM_ARRIVE_SHOP,
            ].includes(detail?.info?.taskSubType) && <Summary data={detail?.statistics} />}

          <Checklist
            extra={
              <div>
                {detail?.info?.taskType === StrategyTaskType.PATROL && (
                  <Checkbox
                    checked={showUnqualified}
                    onChange={(e) => {
                      setShowUnqualified(e.target.checked);
                    }}
                  >
                    仅显示不合格项
                  </Checkbox>
                )}
                {/* <Checkbox
                  checked={showNotReview}
                  onChange={(e) => {
                    setShowNotReview(e.target.checked);
                  }}
                >
                  未点评项
                </Checkbox> */}
              </div>
            }
            worksheetOptions={worksheetOptions}
            categoryOptions={activeWorksheetId && worksheetMap?.[activeWorksheetId]?.categoryOptions}
            categoryPanels={activeCategoryId && worksheetCategoryMap?.[activeCategoryId]?.categorys}
            renderCollapseExtra={(item) => {
              return `${item?.progress}/${item?.itemOptions?.length}`;
            }}
            onWorksheetChange={(val) => {
              setActiveWorksheetId(val);

              switchCategory(val);
            }}
            onCategoryChange={(val) => {
              setActiveCategoryId(val);
            }}
            renderCategoryLabel={(item) => {
              return `${item?.name} (${item?.progress}/${item?.itemOptions?.length})`;
            }}
            renderCheckItem={(item) => {
              return item?.hidden ? undefined : (
                <ItemWrapper
                  value={JSON.stringify({
                    ...item,
                    taskStatus: detail?.info?.taskStatus,
                    taskSubType: detail?.info?.taskSubType,
                    drawerPlacement: placement,
                    newWindow,
                    modalWidth,
                    reportAppealInfoData,
                  })}
                >
                  <CheckItemInfo />
                  <CheckItemResult />
                  <ReviewItemResult />
                </ItemWrapper>
              );
            }}
          />
          {(detail?.info?.summary || patrolSumUp) && (
            <SumUpResult label="巡检总结" value={detail?.info?.summary || patrolSumUp} />
          )}
          {needReviewSummary &&
            detail?.reviewSummarys &&
            Object.keys(detail?.reviewSummarys)?.length > 0 &&
            Object.keys(detail?.reviewSummarys)?.map((key: string) => {
              const content: string = detail?.reviewSummarys?.[key]
                ?.map(({ username, roleNames, summary }: any) => `${username}(${roleNames?.join('、')})：${summary}`)
                .join('\n');

              return <SumUpResult value={content} label={`点评总结${key}`} />;
            })}
          {detail?.info?.taskSubType === StrategyRoutineType.FOOD_SAFETY_ARRIVE_SHOP && !isNil(detail?.examDetail) && (
            <ExamDetail confirm={detail?.examDetail?.confirm} remark={detail?.examDetail?.remark} />
          )}
          {/* 预览时携带的点评总结展示 */}
          {reviewSumUp && (
            <SumUpResult
              value={`${info?.nickName}(${info?.roleBaseInfos?.map(({ roleName }: any) => roleName).join('、')})：${reviewSumUp}`}
              label="点评总结"
            />
          )}
          {footer ? <div className="p-3 bg-white">{footer}</div> : undefined}
          {/* 操作记录 */}
          {showOperationRecord && <OperationRecordModal {...operationRecordProps} />}
          {sopDrawerComponent}
        </>
      )}
    </TaskPage>
  );
};

export default TaskDetail;
