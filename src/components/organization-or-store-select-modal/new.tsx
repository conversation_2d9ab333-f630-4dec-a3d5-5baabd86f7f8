import { FC, useEffect, useMemo, useState } from 'react';
import { CloseOutlined } from '@ant-design/icons';
import {
  ModalForm,
  ProForm,
  ProFormCascader,
  ProFormSelect,
  ProFormText,
  ProFormTextArea,
} from '@ant-design/pro-components';
import { message, Modal, ModalProps, Tag, Tree } from 'antd';
import { cloneDeep } from 'lodash';
import List from 'rc-virtual-list';
import { TissueType } from '@/constants/common';
import { StoreStatusCN, StoreTypeCN } from '@/constants/organization';
import { loopFns } from '@/utils/loop';

export enum ModalType {
  ORGANIZATION = 'ORGANIZATION',
  STORE = 'STORE',
}

const ModalTypeCN = {
  [ModalType.ORGANIZATION]: '组织',
  [ModalType.STORE]: '门店',
};

// 中台和运营通的数据格式是不同的，中台 1:直营，2:加盟
// const ShopTypeMap = {
//   DIRECT: 1,
//   JOIN: 2,
// };

export interface OrganizationOrStoreSelectModalProps extends ModalProps {
  type: ModalType;
  options?: any[];
  onOk?: (data: any) => void;
  onCancel?: () => void;
  value?: any;
  open?: boolean;
  defaultExpandAll?: boolean;
  defaultExpandedKeys?: string[];
}

const filter = (options, filteFn: ((data: any) => any)[] = []) => {
  const filterData = [];

  options?.forEach(async (option: any) => {
    const data = loopFns(option, filteFn);

    if (data) {
      const { children } = data;

      filterData.push({ ...data, children: filter(children, filteFn) });
    }
  });

  return filterData;
};

const check = ({ item, name, shopStatus, shopType }) => {
  const { label, value, shopType: dataShopType, shopStatus: dataShopStatus } = item;

  if (value.includes('SHOP')) {
    if (name && !label?.includes(name)) {
      return false;
    }

    if (shopType && dataShopType !== +shopType) {
      return false;
    }

    if (shopStatus && dataShopStatus !== +shopStatus) {
      return false;
    }

    return true;
  } else {
    return false;
  }
};

// eslint-disable-next-line max-lines-per-function
const OrganizationOrStoreSelectModal: FC<OrganizationOrStoreSelectModalProps> = ({
  open,
  type,
  options,
  onOk,
  onCancel,
  value,
  defaultExpandAll = true,
  defaultExpandedKeys,
}) => {
  const [checkedKeys, setCheckedKeys] = useState<any>([]);
  const [form] = ProForm.useForm();
  const organization = ProForm.useWatch('organization', form);
  const name = ProForm.useWatch('name', form);
  const shopType = ProForm.useWatch('shopType', form);
  const shopStatus = ProForm.useWatch('shopStatus', form);
  const [batchModalProps, setBatchModal] = useState<{ open: boolean }>({
    open: false,
  });

  useEffect(() => {
    if (open && value?.length) {
      setCheckedKeys(value?.map(({ value }) => value) || []);
    }
  }, [open, value]);

  const filterOptions = useMemo(() => {
    const filterData = filter(options, [
      (option: any) => {
        const { value } = option;

        if (value?.includes(ModalType.ORGANIZATION)) {
          return option;
        }
      },
    ]);

    return filterData;
  }, [options]);
  const orgMap = useMemo(() => {
    const map: any = {};
    const filter = (options: any, parents?: any[]) => {
      let descendant: any[] = [];

      const son = options?.map((option: any) => {
        const parentIds = parents || [];
        const { value, children } = option;

        map[value] = { ...option, children: undefined, parentIds };

        if (children?.length) {
          const { son: subSon, descendant: subDescendant } = filter(children, parentIds?.concat([value]));

          descendant = descendant.concat(subSon, subDescendant);
          map[value]['son'] = subSon;
          map[value]['descendant'] = Array.from(new Set((subSon || []).concat(subDescendant || [])));
        }

        return value;
      });

      return { son, descendant };
    };

    filter(options);

    return map;
  }, [options]);

  // 新纳入门店数据来源是接口详情，可能不在中台数据中，因此外部传入数据时，如果有权限则同步更新从中台缓存的组织树数据,否则生成扩展数据
  const otherShops = useMemo(() => {
    const others = [];

    if (value?.length) {
      value.forEach(({ value, shopOpened, label, deleted }) => {
        if (orgMap[value]) {
          orgMap[value].shopOpened = shopOpened;
        } else {
          others.push({
            shopOpened,
            key: value,
            value,
            label,
            title: label,
            deleted,
          });
        }
      });
    }

    return others;
  }, [value, orgMap]);

  const filterTreeData = useMemo(() => {
    let treeData = type === ModalType.ORGANIZATION ? filterOptions : options;

    if (organization?.length) {
      const key = organization[organization.length - 1];
      const getChildren = (keys: any) => {
        const filterKeys =
          type === ModalType.ORGANIZATION ? keys?.filter((key) => key.includes(ModalType.ORGANIZATION)) : keys;

        return filterKeys?.map((key) => ({ ...orgMap[key], children: getChildren(orgMap[key]?.son) }));
      };

      treeData = [{ ...orgMap[key], children: getChildren(orgMap[key]?.son) }];
    }

    if (name || shopType || shopStatus) {
      const findData: any[] = [];
      const loop = (filterData: any[], parent?: any) => {
        const children: any = [];

        filterData.forEach((item: any) => {
          if (check({ item, name, shopType, shopStatus })) {
            parent && findData.push(parent);
            children.push({
              ...item,
            });
          } else {
            let sub: any[] = [];

            if (item?.children?.length > 0) {
              sub = loop(item.children, item);
            }

            if (sub.length > 0) {
              children.push({ ...item, children: sub });
            }
          }
        });

        return children;
      };

      return loop(treeData);
    }

    return treeData;
  }, [orgMap, filterOptions, options, type, organization, name, shopType, shopStatus]);

  const filterTreeDataKeys = useMemo(() => {
    const keys = [];
    const loopTreeData = (nodes: any[]) => {
      nodes?.forEach(({ value, children }) => {
        keys.push(value);
        children && loopTreeData(children);
      });
    };

    loopTreeData(filterTreeData);

    return keys;
  }, [filterTreeData]);

  const showCheckedKeys = useMemo(() => {
    const eliminate = (keys: any[]) => {
      let finishKeys = [];

      const recursion = (nodes: any[]) => {
        if (nodes.length > 0) {
          const shiftKey = nodes.shift();
          const result = nodes.filter((key) => !orgMap[shiftKey]?.descendant?.includes(key));

          finishKeys = finishKeys.filter((key) => !orgMap[shiftKey]?.descendant?.includes(key));
          finishKeys.push(shiftKey);

          recursion(result);
        }
      };

      recursion(
        keys.sort((prev, next) => {
          const prevId = +prev.split('-')[1];
          const nextId = +next.split('-')[1];

          return prevId - nextId;
        }),
      );

      return finishKeys;
    };

    if (type === ModalType.ORGANIZATION) {
      return eliminate(cloneDeep(checkedKeys));
    } else {
      let shops = checkedKeys.filter((key) => orgMap?.[key] && key.includes('SHOP')) || [];

      eliminate(checkedKeys.filter((key) => !key.includes('SHOP'))).forEach((key) => {
        if (key.includes('SHOP')) {
          shops.push(key);
        } else {
          if (orgMap[key]?.descendant) {
            shops = shops.concat(
              orgMap[key]?.descendant?.filter((key) => {
                return check({ item: orgMap[key], name, shopType, shopStatus });
              }),
            );
          }
        }
      });

      return Array.from(new Set(shops));
    }
  }, [checkedKeys, name, shopType, shopStatus]);

  const reset = () => {
    setCheckedKeys([]);
    form.resetFields();
  };

  return (
    <>
      <Modal
        open={open}
        title={`选择${ModalTypeCN[type]}`}
        width={856}
        onOk={() => {
          onOk?.(
            showCheckedKeys
              ?.filter((key) => !!orgMap?.[key])
              ?.map((key) => orgMap[key])
              .concat(otherShops) || [],
          );
          reset();
        }}
        onCancel={() => {
          reset();
          onCancel?.();
        }}
        style={{ minWidth: 856 }}
        destroyOnClose
      >
        <div>
          <div className="mb-5 flex">
            <ProForm layout="inline" submitter={false} form={form}>
              <ProFormCascader
                width="sm"
                placeholder="全部组织"
                fieldProps={{ options: filterOptions, showSearch: true, changeOnSelect: true }}
                name="organization"
              />
              {type === ModalType.STORE && (
                <>
                  <ProFormSelect width="xs" placeholder="门店类型" name="shopType" valueEnum={StoreTypeCN} />
                  <ProFormSelect width="xs" placeholder="门店状态" name="shopStatus" valueEnum={StoreStatusCN} />
                  <ProFormText width="sm" placeholder="请输入门店名称或门店编号" name="name" />
                  <a
                    className="leading-8"
                    onClick={() => {
                      setBatchModal({
                        open: true,
                      });
                    }}
                  >
                    批量选择门店
                  </a>
                </>
              )}
            </ProForm>
          </div>
          <div className="flex">
            <div className="min-w-[400px] h-[400px] border border-solid border-gray-200 rounded p-2">
              <Tree
                treeData={filterTreeData}
                height={384}
                //   checkStrictly
                checkedKeys={checkedKeys.filter((key) => filterTreeDataKeys?.includes(key) && orgMap?.[key])}
                checkable
                defaultExpandAll={defaultExpandAll}
                defaultExpandedKeys={defaultExpandedKeys}
                autoExpandParent={true}
                onCheck={(keys: any) => {
                  if (organization?.length > 0 || !!name || !!shopType || !!shopStatus) {
                    // 如果有查询条件，则代表组织树非全量数据
                    const newCheckedKeys = cloneDeep(checkedKeys);
                    // 先清空筛选条件中的所有项
                    const filterNewCheckedKeys = newCheckedKeys.filter((key) => !filterTreeDataKeys?.includes(key));
                    const shopKeys = keys?.filter((key) => key.includes(TissueType.SHOP));

                    let noOrgKes = keys;

                    if (type === ModalType.STORE) {
                      // 有筛选条件的情况下，组织节点的子节点展示非全量节点，因此会造成选择子节点，同时冒泡选中更高级节点，导致清除筛选条件时，选择结果显示为高级节点选中效果
                      // 因此对组织节点的数据做进行门店数据比对
                      noOrgKes = keys.filter(
                        (key) =>
                          key.includes(TissueType.SHOP) ||
                          (key.includes(TissueType.ORGANIZATION) &&
                            orgMap?.[key].descendant
                              ?.filter((subkey) => subkey.includes(TissueType.SHOP))
                              .every((subkey: string) => shopKeys.includes(subkey))),
                      );
                    }

                    setCheckedKeys(filterNewCheckedKeys.concat(noOrgKes));
                  } else {
                    setCheckedKeys(keys);
                  }
                }}
              />
            </div>
            <div className="min-w-[400px] ml-2 border border-solid border-gray-200 rounded py-2 ">
              <div className="flex justify-between px-2">
                <span>
                  已选<a className="mx-2">{showCheckedKeys?.length + (otherShops?.length || 0) || 0}</a>个
                  {ModalTypeCN[type]}
                </span>
                <a
                  onClick={() => {
                    setCheckedKeys([]);
                  }}
                >
                  全部删除
                </a>
              </div>
              <List
                data={showCheckedKeys
                  ?.filter((key) => !!orgMap?.[key])
                  .map((key) => orgMap?.[key])
                  .concat(
                    otherShops.map((shop) => {
                      return {
                        ...shop,
                        isOther: true,
                      };
                    }),
                  )}
                height={341}
                itemHeight={32}
                className="mt-2"
                itemKey="value"
              >
                {({ key, title, shopOpened, isOther }) => {
                  return (
                    <div className="flex justify-between leading-8 pl-2 pr-4" key={key}>
                      <div>
                        <span>{title}</span>
                        {shopOpened && (
                          <Tag color="red" className="ml-3">
                            自动纳入
                          </Tag>
                        )}
                      </div>
                      {!isOther && (
                        <CloseOutlined
                          className="cursor-pointer"
                          onClick={() => {
                            const cloneCheckeds = cloneDeep(checkedKeys);
                            const deleteKeys = [key].concat(orgMap[key]?.descendant || [], orgMap[key]?.parentIds);

                            setCheckedKeys(cloneCheckeds.filter((key) => !deleteKeys?.includes(key)));
                          }}
                        />
                      )}
                    </div>
                  );
                }}
              </List>
            </div>
          </div>
        </div>
      </Modal>
      <ModalForm
        title="批量选择门店"
        open={batchModalProps.open}
        width={480}
        modalProps={{
          destroyOnClose: true,
        }}
        onFinish={(values) => {
          const { name } = values;
          const keyArr = name?.trim()?.split(/,|，|\n|\r|\r\n|\n\r/) || [];

          const selectKeys = [];
          const notFounds = [];

          keyArr.forEach((key) => {
            const value = `SHOP-${key}`;

            if (orgMap[value]) {
              selectKeys.push(value);
            } else {
              notFounds.push(key);
            }
          });

          if (notFounds?.length) {
            message.error(`未找到${notFounds?.join('、')}等门店，请重新确认`);
          }

          setCheckedKeys(checkedKeys?.concat(selectKeys));

          return Promise.resolve(true);
        }}
        onOpenChange={(value) => {
          if (!value) {
            setBatchModal({
              open: false,
            });
          }
        }}
      >
        <ProFormTextArea width="lg" placeholder="请输入要添加的门店编号，用中英文逗号隔开或换行隔开" name="name" />
      </ModalForm>
    </>
  );
};

export default OrganizationOrStoreSelectModal;
