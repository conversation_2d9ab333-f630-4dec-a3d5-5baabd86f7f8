import { FC } from 'react';
import dayjs from 'dayjs';
import { StudyStatusCN } from '@/constants/task';

export interface StudyInfoProps {
  completeTime?: string;
  nickname: string;
  status: string;
  studyProjectId: string;
  studyProjectName: string;
  itemList?: {
    id: string;
    name: string;
    score: number;
    passed?: boolean;
    examTime?: string;
  }[];
}

const StudyInfo: FC<StudyInfoProps> = ({ nickname, studyProjectName, status, completeTime, itemList }) => {
  return (
    <div className="text-sm leading-7 text-[#3D3D3D]">
      <div>
        <span className="mr-3">学员:</span>
        <span>{nickname}</span>
      </div>
      <div>
        <span className="mr-3">学习状态:</span>
        <span>{status ? StudyStatusCN[status] : '-'}</span>
      </div>
      <div>
        <span className="mr-3">学习时间:</span>
        <span>{completeTime ? dayjs(completeTime).format('YYYY-MM-DD HH:mm') : '-'}</span>
      </div>
      <div>
        <span className="mr-3">学习项目:</span>
        <a>{studyProjectName}</a>
      </div>
      <div>
        <span className="mr-3">考试结果:</span>
        <a>{!itemList?.length && '-'}</a>
      </div>
      {itemList?.map(({ name, score, passed, examTime }) => (
        <div className="indent-[2em]">
          <span className="mr-2">名称:</span>
          <span>{name}</span>，<span className="mx-2">结果分数:</span>
          <span>{score}分</span>
          {typeof passed === 'boolean' && (
            <>
              <span>，</span>
              <span className="ml-2">{passed ? '通过' : '不通过'}</span>
            </>
          )}
          {examTime && (
            <>
              <span>，</span>
              <span className="ml-2">{dayjs(examTime).format('YYYY-MM-DD HH:mm')}</span>
            </>
          )}
        </div>
      ))}
    </div>
  );
};

export default StudyInfo;
