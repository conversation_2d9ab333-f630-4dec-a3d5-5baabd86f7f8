import { FC } from 'react';
import { List, ListProps } from 'antd';
import StudyInfo, { StudyInfoProps } from './study-info';

export interface StudyInfoListProps<T> extends ListProps<T> {}

const StudyInfoList: FC<StudyInfoListProps<StudyInfoProps>> = ({ dataSource }) => {
  return (
    <List
      // bordered
      dataSource={dataSource}
      renderItem={(item: StudyInfoProps) => (
        <List.Item>
          <StudyInfo {...item} />
        </List.Item>
      )}
    />
  );
};

export default StudyInfoList;
