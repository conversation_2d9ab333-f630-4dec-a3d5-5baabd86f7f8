import {
  ModalForm,
  ModalFormProps,
  ProFormRadio,
  ProFormSelect,
} from "@ant-design/pro-components";
import { Form, message } from "antd/es";
import { FC } from "react";

interface BatchSetTagModalFormProps extends ModalFormProps {
  tagOptions: { label: React.ReactNode; value?: string | number | null }[];
}
const BatchSetTagModalForm: FC<BatchSetTagModalFormProps> = ({
  onFinish,
  onOpenChange,
  open,
  tagOptions,
}) => {
  const [form] = Form.useForm();

  return (
    <ModalForm
      title="批量设置标签"
      form={form}
      open={open}
      onFinish={onFinish}
      onOpenChange={onOpenChange}
      layout="horizontal"
      width={560}
      modalProps={{
        destroyOnClose: true,
      }}
    >
      <ProFormSelect
        width="md"
        label="标签设置"
        name="labelIds"
        required
        options={tagOptions}
        mode="multiple"
        showSearch
        allowClear
        placeholder="请选择标签"
        rules={[
          {
            required: true,
            message: "请选择标签",
          },
          {
            validator: (_: any, value: any) => {
              if (value?.length > 10) {
                message.error("一个检查表最多支持选择10个标签");
                form.setFieldValue("labelIds", value?.splice(0, 10));
              }
              return Promise.resolve();
            },
          },
        ]}
      />

      <ProFormRadio.Group
        name="bindMethod"
        label="处理方式"
        initialValue="COVER"
        required
        options={[
          {
            label: "覆盖处理（以当前所选标签为准）",
            value: "COVER",
          },
          {
            label: "追加处理（保留已有标签，追加当前所选标签）",
            value: "APPEND",
          },
        ]}
      />
    </ModalForm>
  );
};

export default BatchSetTagModalForm;
