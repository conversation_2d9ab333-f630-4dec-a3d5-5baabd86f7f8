type TreeWith<T = object> = T & { children?: TreeWith<T>[] };

export type PlatformShopInfo = {
  address: string;
  city: string;
  dimPshopId: number;
  id: number;
  name: string;
  platformShopId: number;
  latitude: number;
  longitude: number;
  shopId: number;
  tagId: number;
  shopValidFlag: 0 | 1;
};

export type ShopInfo = {
  shopId: string;
  shopName: string;
  province: string;
  org: string;
  platformShops: PlatformShopInfo[];
  id: number;
  name: string;
};

export type GroupTreeItem = {
  id: number;
  brandId: number;
  name: string;
  parentId?: number;
  shopInfos?: ShopInfo[];
};

export type OrganizationNode = TreeWith<GroupTreeItem>;

export type ArchitectureNode = TreeWith<
  Omit<OrganizationNode, 'children' | 'shops'> & {
    key: string;
    title: string;
  }
>;

export type ArchitectureDataNode = {
  type: 'group' | 'shop';
  value: string;
  title: string;
  children?: ArchitectureDataNode[];
};
