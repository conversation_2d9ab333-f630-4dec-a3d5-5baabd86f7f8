export type TreeNode<T extends { [key: string]: any }> = T & { children?: TreeNode<T>[] };

const tree2list = (node: TreeNode<any>, arr: any[], pid: any = 0) => {
  const newNode = { ...node };
  if (newNode.children) {
    newNode.children.forEach((item: any) => {
      tree2list(item, arr, newNode.id);
    });
  }
  delete newNode.children;

  arr.push({ ...newNode, pid });
};

/**
 * 把树形结构展开为列表
 * @param nodes 树形节点列表
 * @returns 展开的树节点列表
 */
export const convertTreeToList = <T extends Record<string, any>>(nodes: TreeNode<T>[]): T[] => {
  const result: T[] = [];
  nodes.forEach((node) => {
    tree2list(node, result, 0);
  });

  return result;
};
