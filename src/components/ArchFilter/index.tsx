import { useState } from 'react';
import type { ArchitectureNode } from './api.type';
import { useArchitectureModule } from './useArchitectureModule';
import { Avatar, Spin, Tree, TreeSelect } from 'antd';
import classNames from 'classnames';

export interface ArchFilterProps {
  value?: number;
  onChange?: (value?: number) => void;
  /** 树形数据 */
  treeData?: ArchitectureNode[];
  /** 搜索下面的node节点 */
  searchNextNode?: React.ReactNode;
}

const brandName = '塔斯汀';

export const ArchFilter = (props: ArchFilterProps) => {
  const { onChange, value, searchNextNode } = props;
  const [expandedKeys, setExpandedKeys] = useState<React.Key[]>([]);
  const [checkedKeys, setCheckedKeys] = useState<React.Key[]>(() =>
    typeof value === 'undefined' ? undefined : [value]
  );

  const [autoExpandParent, setAutoExpandParent] = useState<boolean>(true);
  const { data: treeData, loading } = useArchitectureModule();

  const onExpand = (expandedKeysValue: React.Key[]) => {
    setExpandedKeys(expandedKeysValue);
    setAutoExpandParent(false);
  };

  return (
    <div className="w-full">
      <TreeSelect
        placeholder="搜索名称"
        // value={null as any}
        value={value}
        onChange={(v) => {
          setExpandedKeys([v]);
          setCheckedKeys([v]);
          onChange?.(v);
          setAutoExpandParent(true);

          // const node = nodeMap.get(v);
          // if (node) {
          //   const pids = node.pids;
          //   const expandedKeys = pids
          //     .split(',')
          //     .map((v) => parseInt(v))
          //     .filter((v) => !!v);
          //   ref.current?.setExpandedKeys(expandedKeys);
          // }
          // onChange?.(parseInt(v));
        }}
        treeData={treeData}
        showSearch
        className="w-full mb-2"
        filterTreeNode={(keyword, node) => {
          if (node.title) {
            return node.title.toString().indexOf(keyword) > -1;
          } else {
            return false;
          }
        }}
      />
      {searchNextNode}
      <div className="overflow-x-auto">
        <Spin spinning={loading}>
          <div
            className={classNames('flex items-center mb-2 p-1.5 rounded cursor-pointer', {
              'bg-primary/30': !value,
              'text-white': !value
            })}
            onClick={() => {
              onChange?.(undefined);
            }}
          >
            <Avatar size="small" className="bg-primary mr-2" shape="square">
              {brandName.slice(0, 1)}
            </Avatar>
            <span className="text-xs">{brandName}</span>
          </div>
          <Tree
            defaultExpandParent
            treeData={treeData}
            checkable
            selectable={false}
            checkStrictly
            titleRender={(node: any) => {
              return (
                <div className="min-w-30">
                  {/* TODO: */}
                  {/* <IconFont type="icon-dept" /> */}
                  <span> {node.name}</span>
                </div>
              );
            }}
            onCheck={(e) => {
              if (!Array.isArray(e)) {
                const { checked } = e;
                if (checked.length) {
                  setCheckedKeys([parseInt((checked as any)[checked.length - 1])]);
                  onChange?.(parseInt((checked as any)[checked.length - 1]));
                } else {
                  setCheckedKeys([]);
                  onChange?.(undefined);
                }
              }
            }}
            expandedKeys={expandedKeys}
            checkedKeys={checkedKeys}
            onExpand={onExpand}
            autoExpandParent={autoExpandParent}
          />
        </Spin>
      </div>
    </div>
  );
};
