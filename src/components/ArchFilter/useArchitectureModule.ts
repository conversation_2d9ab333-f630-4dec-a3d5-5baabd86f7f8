import { useRequest } from 'ahooks';
import { getGroupTree } from './api';
import type { OrganizationNode } from './api.type';
import { useMemo, useRef } from 'react';
import { convertTreeToList } from './utils';
import { debounce } from 'lodash';

const dig = (tags?: OrganizationNode[], parent?: string, isBrand?: boolean) => {
  if (tags === undefined || tags.length === 0) {
    return undefined;
  }

  return tags.map((node) => {
    const { children, id, name } = node;

    const org = isBrand ? '' : `${parent ? parent + ' - ' : ''}${name}`;

    return {
      ...node,
      key: id as any,
      title: `${name}`,
      value: id,
      children: dig(children, org, false)
      // shops: shopInfos && processShopList(shopInfos, { org }),
    } as any;
  });
};

export const useArchitectureModule = () => {
  const { params, loading, data, run, cancel } = useRequest(async () => {
    const result = await getGroupTree({
      groupType: 2,
      privilegeCode: 1
    });

    return dig(
      (Array.isArray(result)
        ? result
        : typeof result === 'object'
          ? [{ ...result }]
          : undefined) as unknown as OrganizationNode[]
    );
  });

  const { tagIdObj, nodeList, nodeMap } = useMemo(() => {
    const tagIdObj = {} as { [key: number]: OrganizationNode };
    const dig = (tree?: OrganizationNode[]) =>
      tree &&
      tree.forEach((item) => {
        dig(item.children as any);
        tagIdObj[item.id] = item;
      });

    dig((data as any)!);

    const nodeList = data ? convertTreeToList(data) : [];

    const nodeMap = nodeList.reduce((acc, node) => {
      acc.set(node.id, node as any);
      return acc;
    }, new Map<number, OrganizationNode>());

    return { tagIdObj, nodeList, nodeMap };
  }, [data]);

  const dataRef = useRef({ params, data, tagIdObj, loading });

  const methods = useMemo(
    () => ({
      fetch: debounce(run, 200),
      reload: () => {
        const { params } = dataRef.current;
        run(...params!);
      },
      // getShops: (tagId: number) => getShopsFromNode(dataRef.current.tagIdObj[tagId]),
      getTagInfo: (tagId: number) => dataRef.current.tagIdObj[tagId]
    }),
    []
  );

  return {
    tagIdObj,
    params,
    loading,
    cancel,
    data: data || [],
    nodeList,
    nodeMap,
    ...methods
  };
};
