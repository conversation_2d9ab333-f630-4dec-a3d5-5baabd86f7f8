import { TreeSelect } from 'antd';
import { TreeSelectProps, SelectValue } from 'antd/es/tree-select';
import useRemoteTreeSelectProps from './useRemoteTreeSelectProps';

export type OptionType = { title: string | number; value: string | number; [key: string]: any };

export interface RemoteTreeSelectProps<T, R> extends TreeSelectProps<T> {
  active?: boolean;
  useSource(): Promise<R[]>;
  postData?(result: R[]): OptionType[];
}

function RemoteTreeSelect<T extends SelectValue, R>(props: RemoteTreeSelectProps<T, R>) {
  const selectProps = useRemoteTreeSelectProps(props);

  return <TreeSelect {...selectProps} />;
}

RemoteTreeSelect.defaultProps = { active: true };

export default RemoteTreeSelect;

export { useRemoteTreeSelectProps };
