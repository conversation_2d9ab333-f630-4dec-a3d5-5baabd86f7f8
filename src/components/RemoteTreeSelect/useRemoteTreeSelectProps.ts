import { TreeSelectProps } from 'antd/es/tree-select';
import { RemoteTreeSelectProps, OptionType } from '.';
import { useRequest } from 'ahooks';

const useRemoteTreeSelectProps = <T = any, R = any> ({
  active = true,
  useSource,
  postData,
  ...restProps
}: RemoteTreeSelectProps<T, R>) => {
  const { loading, data } = useRequest(
    async () => {
      if (!active || data?.length) throw null;
      const source = await useSource();
      return postData ? postData(source) : (source as any as OptionType);
    },
    {
      refreshDeps: [active]
    }
  );

  return {
    treeNodeFilterProp: 'title',
    treeCheckable: true,
    showSearch: true,
    allowClear: true,
    ...restProps,
    loading,
    treeData: data!
  } as TreeSelectProps<T>;
};

export default useRemoteTreeSelectProps;
