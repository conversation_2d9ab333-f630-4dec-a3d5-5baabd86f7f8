import { FC, useEffect } from 'react';
import { ModalForm, ModalFormProps, ProFormText } from '@ant-design/pro-components';
import Form, { RuleObject } from 'antd/es/form';
import { StoreValue } from 'antd/es/form/interface';

interface TagFormModalProps extends ModalFormProps {
  open: boolean;
  onFinish: (values: any) => Promise<any>;
  initialValues?: any;
  /** 标签名称 默认为标签名称 */
  tagLabel?: string;
  validator?: (rule: RuleObject, value: StoreValue, callback: (error?: string) => void) => Promise<void | any> | void;
}

const TagFormModal: FC<TagFormModalProps> = ({
  initialValues,
  title,
  open,
  tagLabel,
  onOpenChange,
  onFinish,
  validator,
}) => {
  const [form] = Form.useForm<{ name: string }>();

  useEffect(() => {
    Object.keys(initialValues || {}).forEach((key: string) => {
      form.setFieldValue(key, initialValues[key]);
    });
  }, [initialValues]);

  const tagNameLabel = tagLabel || '标签名称';

  return (
    <ModalForm
      title={title}
      form={form}
      width={530}
      modalProps={{
        destroyOnClose: true,
      }}
      layout="inline"
      open={open}
      onOpenChange={onOpenChange}
      onFinish={async (values: any) => {
        return onFinish?.(values).then(() => {
          return true;
        });
      }}
    >
      <ProFormText
        width={400}
        name="name"
        label={tagNameLabel}
        rules={[
          {
            required: true,
            message: `请输入${tagNameLabel}`,
          },
          {
            validator,
          },
        ]}
        placeholder={`请输入${tagNameLabel}`}
        fieldProps={{
          maxLength: 20,
          showCount: true,
        }}
      />
    </ModalForm>
  );
};

export default TagFormModal;
