import React, { ReactNode, useCallback, useEffect, useState } from 'react';
import { UploadOutlined } from '@ant-design/icons';
import { Button, message, Upload, UploadProps } from 'antd';
import { UploadFile } from 'antd/es/upload/interface';
import { useOSSClient } from '@/hooks/use-oss-client';
import { getToken } from '@/utils/authorization';

interface IUploadProps extends Pick<UploadProps, 'accept' | 'beforeUpload'> {
  maxCount?: number;
  children?: ReactNode;
  value?: UploadFile<any>[];
  onChange?(fileList: UploadFile<any>[]): void;
  onError?(e?: any): void;
  listType?: 'text' | 'picture' | 'picture-card';
  disabled?: boolean;
  data?: any;
  max?: number;
}

const OssUpload = React.forwardRef<any, IUploadProps>(
  ({ accept = '.xlsx,.csv', onChange, value, maxCount, onError, children, max = 50, data, ...restProps }, ref) => {
    const [fileList, setFileList] = useState<UploadFile<any>[]>([]);

    useEffect(() => {
      setFileList(value!);
    }, [value]);

    const { uploadFile } = useCallback(useOSSClient, [])('SOP');

    const beforeUpload = (file: File) => {
      const { name } = file;
      const nameArr = name.split('.');
      const type = `.${nameArr[nameArr.length - 1].toLowerCase()}`;
      const acceptArr = accept.toLowerCase().split(',');

      if (file.size / 1024 / 1024 > max) {
        message.error(`${file.name}文件大小超过${max}MB`);

        return Upload.LIST_IGNORE;
      }

      if (!acceptArr?.includes(type)) {
        message.error(`${file.name}不是${accept}其中的类型`);

        return Upload.LIST_IGNORE;
      }

      return Promise.resolve('上传成功!');
    };

    const customRequest = async (e: any) => {
      try {
        const response = await uploadFile(e.file, data?.watermark, data?.imageSource);

        e.onSuccess(response);
      } catch (error) {
        e.onError(error);
      }
    };

    return (
      <Upload
        ref={ref}
        // action="/api/file/uploadImage"
        accept={accept}
        headers={{ 'user-token': getToken() || '' }}
        showUploadList={{
          showPreviewIcon: false,
        }}
        // itemRender={(originNode, file: any, _fileList, actions) =>
        //   file && file.contentType ? null : originNode
        // }
        {...restProps}
        beforeUpload={beforeUpload}
        customRequest={customRequest}
        onChange={({ fileList, file }) => {
          let nextFileList = fileList || [];

          // if (file.status === 'error') {
          //   nextFileList = fileList.filter((f) => f !== file);
          //   onError && onError(file.response);
          // } else
          if (file.status === 'done') {
            const { response } = file;
            const { id, url, name, contentType } = response;

            nextFileList = fileList.map((f) => (f === file ? { uid: id, name, url, contentType } : f));
          }

          setFileList(nextFileList);

          if (file.status !== 'uploading') {
            onChange && onChange(nextFileList);
          }
        }}
        fileList={fileList || []}
      >
        {fileList && fileList.length >= maxCount!
          ? null
          : children || <Button icon={<UploadOutlined />}>点击选择文件</Button>}
      </Upload>
    );
  },
);

OssUpload.defaultProps = {
  maxCount: 1,
};

export default OssUpload;
