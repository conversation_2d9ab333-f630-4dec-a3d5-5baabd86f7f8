import { useMemo } from 'react';
import { Cascader, CascaderProps, Tooltip } from 'antd';
import classNames from 'classnames';
import useRoleCascader from './useRoleCascader';

// 全部、其他分组-其他角色，是前端整合的数据
export type RoleCascaderProps = CascaderProps & {
  onChange?: (value: any) => void;
  needDisplayRender?: boolean;
  multiple?: any;
};

const RoleCascader: React.FC<RoleCascaderProps> = ({
  onFocus,
  multiple,
  disabled,
  placeholder = '请选择角色',
  className,
  style,
  showCheckedStrategy,
  showSearch,
  allowClear,
  options,
  onChange,
  value: propValue,
  needDisplayRender = false,
  maxTagPlaceholder,
  ...restProps
}: RoleCascaderProps) => {
  const { getValueFromMap, formatSelectedToValue } = useRoleCascader({
    options,
    multiple,
  });

  const value = useMemo(() => {
    return getValueFromMap(propValue);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [propValue, options]);

  const displayRender: CascaderProps<any>['displayRender'] = (labels, selectedOptions = []) => {
    return (
      <Tooltip title={labels.join(' / ')}>
        <span key={selectedOptions?.pop()?.value}>{selectedOptions?.pop()?.label}</span>
      </Tooltip>
    );
  };

  return (
    <Cascader
      {...restProps}
      onFocus={onFocus}
      expandTrigger="hover"
      multiple={multiple}
      disabled={disabled}
      placeholder={placeholder}
      className={classNames(className, 'shrink grow')}
      style={style}
      showCheckedStrategy={showCheckedStrategy}
      showSearch={showSearch}
      value={value}
      allowClear={allowClear}
      maxTagCount="responsive"
      options={options}
      onChange={(_: any, selectedOptions: any) => {
        onChange?.(formatSelectedToValue(selectedOptions));
      }}
      maxTagPlaceholder={
        maxTagPlaceholder
          ? maxTagPlaceholder
          : (omittedValues) => {
              return (
                <Tooltip
                  title={omittedValues
                    ?.map((m: any) => {
                      return m?.label?.props?.children?.props?.children;
                    })
                    ?.join('、')}
                >
                  +{omittedValues?.length}...
                </Tooltip>
              );
            }
      }
      displayRender={needDisplayRender ? displayRender : undefined}
    />
  );
};

export default RoleCascader;
