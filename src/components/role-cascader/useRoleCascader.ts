import { DefaultOptionType } from 'antd/es/cascader';
import Item from 'antd/es/list/Item';
import { useMemo } from 'react';

const useRoleCascader = ({ options = [], multiple }: { options?: DefaultOptionType[]; multiple?: boolean }) => {
  //生成最后一级与所有父节的映射
  const genParentsMap = (options: DefaultOptionType[]) => {
    let map: any = {};
    const loop = (nodes: DefaultOptionType[] = [], parentValues?: any[]) => {
      nodes?.forEach(({ value, children }: DefaultOptionType) => {
        let parentValuesProxy = parentValues || [];
        if (children && children?.length > 0) {
          loop(children, parentValuesProxy?.concat([value]));
        } else {
          map[value!] = parentValuesProxy?.concat([value]);
        }
      });
    };

    loop(options);
    return map;
  };

  const parentIdsMap = useMemo(() => genParentsMap(options), [options, multiple]);

  const getValueFromMap = (value: any) => {
    if (multiple) {
      //多选-二维数组
      if (Array.isArray(value)) {
        return value
          ?.map((num: number) => {
            return parentIdsMap[num];
          })
          .filter((item) => !!item); //角色被删除等无法找到角色情况下，过滤不显示
      } else {
        return [];
      }
    } else {
      return parentIdsMap[value];
    }
  };

  const formatSelectedToValue = (selectedOptions: any) => {
    if (multiple) {
      //多选-二维数组
      let result: any[] = [];
      const loop = (nodes: DefaultOptionType[] = []) => {
        nodes.forEach(({ value, children }: DefaultOptionType) => {
          if (children && children?.length > 0) {
            loop(children);
          } else {
            result.push(value);
          }
        });
      };
      if (Array.isArray(selectedOptions)) {
        selectedOptions.forEach((subOptions: DefaultOptionType[]) => {
          loop([subOptions[subOptions?.length - 1]]);
        });
      }
      return result;
    } else {
      //单选-一维数组
      if (selectedOptions?.length > 0) {
        return selectedOptions[selectedOptions?.length - 1].value;
      }
      return;
    }
  };
  return {
    getValueFromMap,
    formatSelectedToValue
  };
};

export default useRoleCascader;
