import { ProTableProps } from '@ant-design/pro-components';
import { merge } from 'lodash';
import ChecklistSelect from '../checklist-select';
import LimitRangePicker from '../limit-range-picker';
import RoleCascader from '../role-cascader';
import StoreSelect from '../store-select';
import { StoreTypeCN } from '@/constants/common';

export type GlobalQueryConfigProps = Pick<
  ProTableProps<any, any, any>,
  'search' | 'manualRequest' | 'options' | 'columns'
>;

export type QueryConfigParams = {
  extra?: QueryConfigProps;
  searchColumns?: SearchColumns[];
};

type QueryConfigProps = GlobalQueryConfigProps & Pick<ProTableProps<any, any, any>, 'columns'>;

type SearchColumns = {
  name: string;
  props?: any;
  extra?: any;
};

export enum GlobalComponentName {
  STORE = 'STORE', // 门店
  STORETYPE = 'STORETYPE', // 门店类型
  PERSON = 'PERSON', // 各种人-创建人、点评人、巡检人
  ROLE = 'ROLE', // 角色
  CHECKLIST = 'CHECKLIST', // 检查表
  DATERANGE = 'DATERANGE', // 日期区间
}

export const GlobalComponentMap: Record<GlobalComponentName, any> = {
  [GlobalComponentName.STORE]: (props: any, extra?: any) => {
    return merge(
      {
        title: '门店',
        dataIndex: 'store',
        hideInTable: true,
        colSize: 2,
        renderFormItem: () => {
          return (
            <StoreSelect
              organizationOptions={props?.organizationOptions || []}
              storeOptions={props?.storeOptions || []}
              onStoreFocus={props?.onStoreFocus}
            />
          );
        },
      },
      extra,
    );
  },
  [GlobalComponentName.STORETYPE]: (_: any, extra?: any) => {
    return merge(
      {
        title: '门店类型',
        dataIndex: 'storeType',
        hideInTable: true,
        colSize: 1,
        valueType: 'select',
        valueEnum: StoreTypeCN,
      },
      extra,
    );
  },
  [GlobalComponentName.PERSON]: (props: any, extra?: any) => {
    return merge(
      {
        title: '创建人',
        dataIndex: 'person',
        hideInTable: true,
        colSize: 1,
        valueType: 'select',
        fieldProps: {
          mode: props?.mode,
          options: props?.options,
          showSearch: true,
          optionFilterProp: 'label',
          loading: props?.loading,
          onFocus: () => {
            props?.onFocus?.();
          },
        },
      },
      extra,
    );
  },
  [GlobalComponentName.ROLE]: (props: any, extra?: any) => {
    return merge(
      {
        title: '角色',
        dataIndex: 'role',
        hideInTable: true,
        colSize: 1,
        renderFormItem: () => {
          return <RoleCascader options={props?.options} multiple={props?.multiple} />;
        },
      },
      extra,
    );
  },
  [GlobalComponentName.CHECKLIST]: (props: any, extra?: any) => {
    return merge(
      {
        title: '检查表',
        dataIndex: 'sheet',
        hideInTable: true,
        colSize: 2,
        renderFormItem: () => {
          return (
            <ChecklistSelect
              onTagChange={props?.onTagChange}
              tagOptions={props?.tagOptions || []}
              checklistOptions={props?.checklistOptions}
            />
          );
        },
      },
      extra,
    );
  },
  [GlobalComponentName.DATERANGE]: (props: any, extra?: any) => {
    return merge(
      {
        title: '报告提交时间',
        dataIndex: 'dateRange',
        hideInTable: true,
        colSize: 2,
        renderFormItem: () => (
          <LimitRangePicker limit={props?.limit || 89} showTime={props?.showTime} allowClear={false} />
        ),
      },
      extra,
    );
  },
};

const getGlobalQueryConfig = (
  params?: QueryConfigParams,
  componentMap: any = GlobalComponentMap,
): GlobalQueryConfigProps => {
  let columns: any[] | undefined =
    params?.searchColumns?.map(({ name, props, extra }: SearchColumns) => componentMap[name](props, extra)) || [];

  if (Array.isArray(params?.extra?.columns)) {
    columns = params?.extra?.columns?.map((node) => merge(node, { hideInSearch: true }))?.concat(columns);
  }

  return merge(
    {
      search: {
        labelWidth: 'auto', // 标签宽度自适应
        span: {
          xs: 24,
          sm: 24,
          md: 12,
          lg: 8,
          xl: 6,
          xxl: 4,
        },
        collapseRender: false,
        collapsed: false,
        optionRender: (_s, _f, dom) => {
          return dom.reverse();
        },
      },
      manualRequest: false, // 第一次request，手动触发
      options: false,
      columns,
    },
    params?.extra,
  );
};

export default getGlobalQueryConfig;
