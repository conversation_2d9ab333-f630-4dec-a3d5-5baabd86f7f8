import { Select, SelectProps } from 'antd';
import classnames from 'classnames';
import { FC } from 'react';

export interface SopSelectProps {
  tagOptions?: any[];
  sopOptions?: any[];
  onTagChange?: (value: any) => void;
  sopMode?: SelectProps['mode'];
  onChange?: (value: any) => void;
  onSopChange?: (value: any) => void;
  style?: SelectProps['style'];
  value?: any;
  disabled?: boolean;
  sopClassName?: string;
}

const SopSelect: FC<SopSelectProps> = ({
  tagOptions,
  sopOptions,
  disabled,
  value,
  onTagChange,
  onChange,
  onSopChange,
  sopMode,
  sopClassName,
}) => {
  return (
    <div className={classnames('flex', sopClassName)}>
      <Select
        options={tagOptions}
        style={{ width: '160px' }}
        mode="multiple"
        placeholder="请选择标签"
        showSearch
        filterOption={(val, opt) => opt.label.toLowerCase().includes(val.toLowerCase())}
        disabled={disabled}
        allowClear
        value={value?.tags}
        maxTagCount="responsive"
        onChange={(val) => {
          onChange?.({
            tags: val,
            sop: undefined,
          });
          onTagChange?.(val);
        }}
      />
      <Select
        options={sopOptions}
        mode={sopMode}
        placeholder="请选择SOP"
        showSearch
        filterOption={(val, opt) => opt.label.toLowerCase().includes(val.toLowerCase())}
        disabled={disabled}
        allowClear
        value={value?.sop}
        maxTagCount="responsive"
        onChange={(val) => {
          onChange?.({
            ...value,
            sop: val,
          });
          onSopChange?.(val);
        }}
      />
    </div>
  );
};

export default SopSelect;
