import React, { useMemo } from 'react';
import { accDiv, accMul } from './utils';
import IEcharts from '../IEcharts';

type ChartOptionsProps = {
  title?: string;
  count?: number;
  color?: string[];
  unit?: string;
  min?: number;
  max?: number;
  style?: React.CSSProperties;
  importance?: Nullable<number>;
};

// tooltip在gauge是展示在指针上的
const ChartOptions: React.FC<ChartOptionsProps> = (props) => {
  const { style, ...chartsProps } = props;

  const option = useMemo(() => {
    const { title, count, unit, color, min, max } = chartsProps;

    return {
      color,
      series: [
        {
          type: 'gauge',
          startAngle: 180,
          endAngle: 0,
          center: ['50%', '50%'],
          min,
          max,
          name: title,
          pointer: {
            show: false,
          },
          progress: {
            show: true,
            overlap: false,
            itemStyle: {
              // borderWidth: 1,
              // borderColor: '#464646',
            },
          },
          axisLine: {
            lineStyle: {
              width: 20,
            },
          },
          splitLine: {
            show: false,
            distance: 0,
            length: 10,
          },
          axisTick: {
            show: false,
          },
          axisLabel: {
            show: false,
            distance: 50,
          },
          data: [
            {
              value: count,
            },
          ],
          title: {
            offsetCenter: ['0%', '-30%'],
            fontSize: 14,
          },
          detail: {
            offsetCenter: ['0%', '-20%'],
            formatter(value) {
              const formatterValue =
                unit === '分'
                  ? `${accMul(accDiv(value, max), 100).toFixed(2)}`
                  : `${accMul(accDiv(value, max), 100)}${unit}`;

              return formatterValue;
            },
            fontSize: 24,
          },
        },
      ],
      // tooltip: {
      //   formatter: `权重${importance || 20}%`,
      // },
    };
  }, [chartsProps]);

  return <IEcharts option={option} style={{ height: 200, marginBottom: -90, ...style }} />;
};

ChartOptions.defaultProps = {
  color: ['#6bd389', '#f0f2f5'],
  min: 0,
  max: 100,
  unit: '',
};

export default ChartOptions;
