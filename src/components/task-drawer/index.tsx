import { FC } from 'react';
import { Drawer, DrawerProps } from 'antd';
import TaskDetail, { TaskDetailProps } from '../task-detail';
import TaskReview, { TaskReviewProps } from '../task-review';

// eslint-disable-next-line react-refresh/only-export-components
export enum ShowTaskType {
  Self = 'Self', // 自检做任务
  Patrol = 'Patrol', // 巡检做任务
  Review = 'Review', // 点评
  Detail = 'Detail', // 详情
}

export interface TaskDrawerProps extends DrawerProps, TaskDetailProps, TaskReviewProps {
  showType: ShowTaskType;
}

const TaskDrawer: FC<TaskDrawerProps> = ({
  open,
  onClose,
  destroyOnClose,
  showType = ShowTaskType.Detail,
  taskId,
  needReviewSummary,
  needStatistics,
  needExam,
  title,
  hasReview,
  onPreview,
  reviewSumUp,
  notFilledItemHandleType,
  footer,
  showOperationRecord,
  saveReviewItem,
}) => {
  return (
    <Drawer
      destroyOnClose={destroyOnClose}
      width={650}
      title={title}
      open={open}
      onClose={onClose}
      className="task-detail-drawer flex"
    >
      {showType === ShowTaskType.Detail && (
        <TaskDetail
          taskId={taskId}
          needReviewSummary={needReviewSummary}
          needStatistics={needStatistics}
          needExam={needExam}
          reviewSumUp={reviewSumUp}
          notFilledItemHandleType={notFilledItemHandleType}
          footer={footer}
          showOperationRecord={showOperationRecord}
        />
      )}
      {showType === ShowTaskType.Review && (
        <TaskReview
          taskId={taskId}
          needReviewSummary={needReviewSummary}
          hasReview={hasReview}
          onPreview={onPreview}
          saveReviewItem={saveReviewItem}
        />
      )}
    </Drawer>
  );
};

export default TaskDrawer;
