.tabs {
  position: relative;
  display: flex;
  flex-wrap: nowrap;
  overflow-x: auto;
  &::-webkit-scrollbar-track {
    /*滚动条里面轨道*/
    box-shadow: inset 0 0 2px rgba(255, 255, 255, 0.2);
    border-radius: 6px;
    background: rgba(255, 255, 255, 0.1);
  }
  &::-webkit-scrollbar-thumb {
    /*滚动条里面小方块*/
    border-radius: 6px;
    box-shadow: inset 0 0 4px rgba(144, 147, 153, 0.2);
    background: rgba(144, 147, 153, 0.2);
  }
  &::-webkit-scrollbar {
    width: 6px;
    height: 5px;
  }
  > div {
    display: flex;
    align-items: center;
    padding: 8px 0;
    cursor: pointer;

    &:hover {
      color: darken(#d9001b, 5%);
    }

    &.active {
      color: #d9001b;
    }

    &:not(:last-of-type) {
      margin-right: 16px;
    }
  }

  > span {
    position: absolute;
    bottom: 0;
    height: 2px;
    background-color: #d9001b;
    transition:
      width 0.3s,
      left 0.3s;
  }
}
