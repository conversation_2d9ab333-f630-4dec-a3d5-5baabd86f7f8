import classNames from 'classnames';
import React, { useState, useEffect, useRef } from 'react';

import styles from './index.module.scss';

type Key = number | string;

type SimpleTabsProps = {
  className?: string;
  style?: React.CSSProperties;
  tabs: { key: Key; tab: React.ReactNode }[];
  defaultActiveKey?: Key;
  activeKey?: Key;
  onChange?(activeKey: Key): void;
};

const SimpleTabs: React.FC<SimpleTabsProps> = (props) => {
  const { tabs, onChange, className, style } = props;
  const controlled = 'activeKey' in props;

  const wrapDomRef = useRef<HTMLDivElement>(null);

  const [innerActiveKey, setInnerActiveKey] = useState(() => props.activeKey || props.defaultActiveKey);
  const activeKey = controlled ? props.activeKey : innerActiveKey;
  const triggerChange = (key: Key) => {
    if (!controlled) {
      setInnerActiveKey(key);
    }
    onChange && onChange(key);
  };

  const [activeTabLine, setActiveTabLine] = useState({ left: 0, width: 0 });

  useEffect(() => {
    const index = tabs.findIndex((item) => item.key === activeKey);

    if (index === -1) return;

    const domNode = wrapDomRef.current!.childNodes[index] as HTMLDivElement;

    setActiveTabLine({ left: domNode.offsetLeft, width: domNode.offsetWidth });
  }, [activeKey]);

  return (
    <div className={classNames(styles.tabs, className)} style={style} ref={wrapDomRef}>
      {tabs.map(({ key, tab }) => (
        <div
          key={key}
          className={classNames({ [styles.active]: activeKey === key })}
          onClick={() => triggerChange(key)}
        >
          {tab}
        </div>
      ))}
      <span style={activeTabLine}></span>
    </div>
  );
};

export default SimpleTabs;
