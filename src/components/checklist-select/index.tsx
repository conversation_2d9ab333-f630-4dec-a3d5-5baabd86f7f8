import { FC } from 'react';
import { Select, SelectProps, Tooltip, TreeSelectProps } from 'antd';
import classnames from 'classnames';

export interface ChecklistSelectProps {
  tagOptions?: any[];
  checklistOptions?: any[];
  // checklistTreeData?: any[];
  onTagChange?: (value: any) => void;
  multiple?: TreeSelectProps['multiple'];
  onChange?: (value: any) => void;
  onChecklistChange?: (value: any) => void;
  style?: TreeSelectProps['style'];
  value?: any;
  disabled?: boolean;
  checklistClassName?: string;
  labelInValue?: SelectProps['labelInValue'];
  must?: boolean;
}

const ChecklistSelect: FC<ChecklistSelectProps> = ({
  multiple,
  onChange,
  value,
  disabled,
  tagOptions = [],
  // checklistTreeData = [],
  checklistOptions,
  checklistClassName,
  onTagChange,
  onChecklistChange,
  labelInValue,
  must,
}) => {
  return (
    <div className={classnames('flex w-full')}>
      <Select
        style={{
          flexGrow: 1,
          width: 0,
        }}
        options={tagOptions}
        mode="multiple"
        placeholder="请选择标签"
        showSearch
        disabled={disabled}
        allowClear
        value={value?.tags}
        optionFilterProp="label"
        maxTagCount="responsive"
        onChange={(val) => {
          onChange?.({
            tags: val,
            checklist: undefined,
          });
          onTagChange?.(val);
        }}
      />
      <Select
        style={{
          flexGrow: 2,
          width: 0,
        }}
        disabled={disabled}
        maxTagCount="responsive"
        mode={multiple ? 'multiple' : undefined}
        // style={{ width: '240px' }}
        options={checklistOptions}
        showSearch={true}
        allowClear
        labelInValue={labelInValue}
        optionFilterProp="label"
        placeholder="请选择检查表"
        value={value?.checklist}
        onChange={(val: any) => {
          onChange?.({
            ...value,
            checklist: val,
          });
          onChecklistChange?.(val);
        }}
        labelRender={(props) => {
          const { label, value } = props;

          return (
            <Tooltip title={label}>
              <span key={value}>{label}</span>
            </Tooltip>
          );
        }}
      />
      {must ? (
        <div className="flex flex-row items-center justify-between flex-grow w-0 pl-2">
          <div className="w-[120px]">
            <span>必填/选填</span>
          </div>
          <Select
            value={value?.selectorSwitch}
            onChange={(val) => {
              onChange?.({
                ...value,
                selectorSwitch: val,
              });
            }}
            options={[
              { value: 0, label: '必填' },
              { value: 1, label: '选填' },
            ]}
          />
        </div>
      ) : null}
    </div>
  );
};

export default ChecklistSelect;
