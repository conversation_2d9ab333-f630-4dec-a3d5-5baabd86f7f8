import { LeftOutlined, RightOutlined } from '@ant-design/icons';
import { throttle } from 'lodash';
import { ReactNode, useEffect, useRef, useState } from 'react';

type IProps = {
  children: ReactNode | (() => ReactNode);
};

const HorizontalScroll = ({ children }: IProps) => {
  const [showLeft, setShowLeft] = useState(false);
  const [showRight, setShowRight] = useState(false);
  const ref: any = useRef();

  const setShowArrow = () => {
    if (ref?.current) {
      setShowLeft(ref.current.scrollLeft > 0);
      setShowRight(ref.current.scrollLeft + ref.current.clientWidth < ref.current.scrollWidth);
    }
  };

  const move = throttle((distance: number = 100) => {
    if (typeof ref?.current?.scrollLeft === 'number') {
      ref.current.scrollBy({
        left: distance,
        behavior: 'smooth'
      });
    }
  }, 110);

  useEffect(() => {
    setShowArrow();
    ref?.current?.addEventListener('wheel', (e: any) => move(e.deltaY));
    ref?.current?.addEventListener('scroll', () => setShowArrow());
    return () => {
      ref?.current?.removeEventListener('wheel', (e: any) => move(e.deltaY));
      ref?.current?.removeEventListener('scroll', () => setShowArrow());
    };
  }, []);

  return (
    <div className="flex items-center select-none">
      {showLeft && (
        <div className="cursor-pointer" onClick={() => move(-100)}>
          <LeftOutlined />
        </div>
      )}
      <div className="overflow-y-auto" ref={ref}>
        {children}
      </div>
      {showRight && (
        <div className="cursor-pointer" onClick={() => move(+100)}>
          <RightOutlined />
        </div>
      )}
    </div>
  );
};

export default HorizontalScroll;
