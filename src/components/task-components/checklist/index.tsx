import { FC, ReactNode, useEffect, useState } from 'react';
import { Collapse, Tabs } from 'antd';
import styles from './index.module.scss';

export interface ChecklistProps {
  title?: ReactNode;
  extra?: ReactNode;
  worksheetOptions?: any[];
  categoryOptions?: any[];
  renderWorksheetLabel?: (data: any) => ReactNode;
  renderWorksheetExtra?: () => ReactNode;
  renderCategoryLabel?: (data: any) => ReactNode;
  renderCollapseLabel?: (data: any) => ReactNode;
  renderCollapseExtra?: (data: any) => ReactNode;
  renderCheckItem?: (data: any) => ReactNode;
  onWorksheetChange?: (id: any) => void;
  activeWorksheetId?: number | string;
  activeCategoryId?: number | string;
  onCategoryChange?: (id: any) => void;
  categoryPanels?: any[];
  renderBody?: () => ReactNode;
}

const Checklist: FC<ChecklistProps> = ({
  title,
  extra,
  worksheetOptions,
  renderWorksheetLabel,
  renderWorksheetExtra,
  renderCategoryLabel,
  renderCollapseLabel,
  renderCollapseExtra,
  renderCheckItem,
  onWorksheetChange,
  onCategoryChange,
  activeWorksheetId,
  activeCategoryId,
  categoryOptions,
  categoryPanels,
  renderBody,
}) => {
  const [collapseActiveKey, setCollapseActiveKey] = useState<number[]>([]);

  useEffect(() => {
    setCollapseActiveKey(categoryPanels?.map((item) => item?.id));
  }, [categoryPanels]);

  return (
    <div className="flex flex-col bg-white mb-2.5 px-3">
      <div className="flex justify-between mt-4">
        {title || <div className="text-base font-bold shrink-0 leading-8">检查表明细</div>}
        <div>{extra}</div>
      </div>
      {renderBody?.() ? (
        renderBody?.()
      ) : (
        <div className="flex flex-col mt-2">
          <Tabs
            type="card"
            className={styles.TabBar}
            activeKey={activeWorksheetId?.toString()}
            items={worksheetOptions?.map((item) => {
              return {
                label: renderWorksheetLabel?.(item) || item?.name,
                key: item?.id?.toString(),
              };
            })}
            onChange={(value: any) => {
              onWorksheetChange?.(value);
            }}
          />
          <div className="flex-1">
            {renderWorksheetExtra?.()}
            {categoryOptions?.length > 1 && (
              <Tabs
                items={categoryOptions?.map((item: any) => {
                  return {
                    label: renderCategoryLabel?.(item) || item?.name,
                    key: item?.id?.toString(),
                  };
                })}
                activeKey={activeCategoryId?.toString()}
                onChange={(val: any) => {
                  onCategoryChange?.(val);
                }}
              />
            )}
            <div className="">
              {!!categoryPanels?.length && (
                <Collapse
                  expandIconPosition="end"
                  // ghost
                  activeKey={collapseActiveKey}
                  onChange={(e: string[]) => {
                    setCollapseActiveKey(e?.map((key) => Number(key)));
                  }}
                  // defaultActiveKey={categoryMap?.[activeCategoryId!]?.categorys?.map(({ value }) => value)}
                  items={categoryPanels?.map((item: any) => {
                    return {
                      label: renderCollapseLabel?.(item) || item.name,
                      extra: renderCollapseExtra?.(item),
                      key: item?.id?.toString(),
                      children: (
                        <div key={item?.id}>
                          {item?.itemOptions?.map((item: any) => {
                            return renderCheckItem?.(item);
                          })}
                        </div>
                      ),
                    };
                  })}
                />
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Checklist;
