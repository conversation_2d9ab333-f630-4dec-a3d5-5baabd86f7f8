import { useMemo } from 'react';

export interface useChecklistDataProps {
  worksheets?: any[];
  progressCheck?: (data: any) => boolean; // 进度校验
  filter?: (data: any) => boolean;
  worksheetId?: string | number;
}

export const allSuffix = '-all';

const useChecklistData = ({ worksheets, progressCheck, filter, worksheetId }: useChecklistDataProps) => {
  const { worksheetMap, worksheetOptions, worksheetCategoryMap, worksheetItemMap } = useMemo(() => {
    const map: any = {};
    const categoryMap: any = {};
    const itemMap: any = {};
    const options = worksheets?.map((worksheet: any, worksheetIndex: number) => {
      const categoryData = worksheet?.data;
      const categoryArr = categoryData?.map((categoryItem: any, categoryIndex: number) => {
        let progress: number = 0;

        const itemOptions = categoryItem?.data?.map((checklistItem: any, itemIndex: number) => {
          const worksheetItem = checklistItem?.worksheetItemSnap && JSON.parse(checklistItem?.worksheetItemSnap);

          const itemOption = {
            ...checklistItem,
            worksheetItem,
            supportOverdue: worksheet?.supportOverdue,
            worksheetIndex,
            worksheetCategoryIndex: categoryIndex,
            worksheetItemIndex: itemIndex,
          };

          if (progressCheck?.(itemOption)) {
            ++progress;
          }

          const hidden: boolean = filter?.(itemOption) || false;

          itemMap[checklistItem?.worksheetItemId] = { ...itemOption, hidden };

          return { ...itemOption, hidden };
        });

        categoryMap[categoryItem?.id] = {
          ...categoryItem,
          progress,
          categorys: [{ id: categoryItem?.id?.toString(), name: categoryItem?.name, itemOptions, progress }],
        };

        return { ...categoryItem, progress, itemOptions };
      });

      const categoryOptions: any = categoryArr;
      const allProgress = categoryArr?.reduce((pre: number, cur: any) => pre + cur?.progress, 0);

      if (categoryArr?.length > 1) {
        const key: string = `${worksheet?.id}${allSuffix}`;

        let allItemOptions: any = [];

        const categorys = categoryArr?.map((category: any) => {
          allItemOptions = allItemOptions.concat(category?.itemOptions);

          return {
            id: category?.id,
            name: category?.name,
            itemOptions: category?.itemOptions,
            progress: category?.progress,
          };
        });

        categoryMap[key] = {
          id: key,
          name: '全部',

          categorys,
        };
        categoryOptions.unshift({ id: key, name: '全部', progress: allProgress, itemOptions: allItemOptions });
      }

      map[worksheet?.id] = {
        ...worksheet,
        id: worksheet?.id?.toString(),

        categoryOptions: categoryArr,
      };

      return { ...worksheet, data: undefined, value: worksheet?.id?.toString() };
    });

    return {
      worksheetMap: map,
      worksheetOptions: worksheetId
        ? options?.filter(({ worksheetId: id }) => worksheetId?.toString() === id?.toString())
        : options,
      worksheetCategoryMap: categoryMap,
      worksheetItemMap: itemMap,
    };
  }, [worksheets, filter, progressCheck, worksheetId]);

  return { worksheetMap, worksheetOptions, worksheetCategoryMap, worksheetItemMap };
};

export default useChecklistData;
