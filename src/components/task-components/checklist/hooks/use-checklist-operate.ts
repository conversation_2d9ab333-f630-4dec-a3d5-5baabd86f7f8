import { useCallback, useEffect, useState } from 'react';
import { allSuffix } from './use-checklist-data';

const useChecklistOperate = ({ worksheetMap, worksheetOptions }) => {
  const [activeWorksheetId, setActiveWorksheetId] = useState<string>();
  const [activeCategoryId, setActiveCategoryId] = useState<string>();

  const switchCategory = useCallback(
    (worksheetId: string) => {
      const categories = worksheetMap?.[worksheetId]?.categoryOptions;

      if (categories?.length > 1) {
        setActiveCategoryId(`${worksheetId}${allSuffix}`);
      } else {
        setActiveCategoryId(categories?.[0]?.id?.toString());
      }
    },
    [worksheetMap],
  );

  useEffect(() => {
    if (!activeWorksheetId && !activeCategoryId) {
      const worksheetId = worksheetOptions?.[0]?.id;

      setActiveWorksheetId(worksheetId?.toString());

      switchCategory(worksheetId);
    }
  }, [worksheetOptions, activeWorksheetId, activeCategoryId, worksheetMap, switchCategory]);

  return { activeWorksheetId, setActiveWorksheetId, activeCategoryId, setActiveCategoryId, switchCategory };
};

export default useChecklistOperate;
