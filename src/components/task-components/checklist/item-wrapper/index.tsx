import { FC, memo, ReactNode, useMemo } from 'react';
import { TaskEventEmitter, TaskNotice, TaskNoticeType } from '../../utils/emit';
import CheckItemContextProvider from '../context/CheckItemContext';

export interface ItemWrapperProps {
  value?: string;
  children?: ReactNode;
}

const ItemWrapper: FC<ItemWrapperProps> = memo(({ value, children }: any) => {
  const json = useMemo(() => {
    if (value) {
      return JSON.parse(value);
    }

    return {};
  }, [value]);

  return (
    <CheckItemContextProvider
      value={{
        ...json,
        onSave: (dto) => {
          console.log('🚀 ~ constItemWrapper:FC<ItemWrapperProps>=memo ~ dto:', dto);
          TaskEventEmitter.emit(TaskNotice, { dto, type: TaskNoticeType.UpdateWorksheetItem, worksheetItem: json });
        },
      }}
    >
      {children}
    </CheckItemContextProvider>
  );
});

export default ItemWrapper;
