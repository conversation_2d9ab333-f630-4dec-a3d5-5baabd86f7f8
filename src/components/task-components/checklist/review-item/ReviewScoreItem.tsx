import { Button } from 'antd';
import classNames from 'classnames';
import { isBoolean } from 'lodash';
import { useCheckItemContext } from '../context/CheckItemContext';
import { ReviewItemProps } from '.';
import useDisqualificationModal from '@/components/strategy-task-detail/use-disqualification-modal';

export interface ReviewScoreItemProps extends ReviewItemProps {}

const ReviewScoreItem = ({
  onInappropriate,
  onDisqualified,
  onQualified,
  isDisqualified,
  isQualified,
  isInappropriate,
}: ReviewScoreItemProps) => {
  const { reviewItemSnap, reviewResult, modalStyle } = useCheckItemContext();
  const { showModal } = useDisqualificationModal();

  return (
    <>
      <div>
        <Button
          className={classNames('mr-3  rounded border-[0.5px] border-solid border-[#DCDCDC]  px-4 py-2 h-8 text-sm', {
            'bg-[#f53f3f] border-[#f53f3f] text-white': isDisqualified,
          })}
          onClick={() => {
            showModal({
              title: '情况说明',
              showScore: true,
              reviewItem: reviewItemSnap,
              reviewResult,
              onOk: (values) => {
                onDisqualified?.({ ...values });

                return Promise.resolve();
              },
              style: {
                ...modalStyle,
              },
            });
          }}
        >
          {isBoolean(reviewResult?.itemJudge) && !reviewResult?.itemJudge && reviewResult?.itemScore
            ? `${reviewResult?.itemScore} 分`
            : '打分'}
        </Button>
        <Button
          className={classNames('mr-3  rounded border-[0.5px] border-solid border-[#DCDCDC]   px-4 py-2 h-8 text-sm', {
            'bg-[#378bff] border-[#378bff] text-white': isQualified,
          })}
          onClick={() => {
            onQualified?.({ itemScore: reviewItemSnap?.scoreMax });
          }}
        >
          {reviewItemSnap?.displayScore ? `合格 (${reviewItemSnap?.scoreMax}分)` : '合格'}
        </Button>
        <Button
          className={classNames(
            'rounded border-[0.5px] border-solid  px-4 py-2 h-8',
            isInappropriate ? 'bg-[#378bff] border-[#378bff] text-white' : 'border-[#DCDCDC] bg-white ',
          )}
          onClick={onInappropriate}
        >
          不适用
        </Button>
      </div>
    </>
  );
};

export default ReviewScoreItem;
