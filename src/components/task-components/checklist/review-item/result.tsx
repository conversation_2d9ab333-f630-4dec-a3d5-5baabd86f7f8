import { FC, ReactNode, useMemo, useState } from 'react';
import { <PERSON><PERSON>, Drawer } from 'antd';
import classNames from 'classnames';
import { AppealStatusTextMap } from './const';
import { useCheckItemContext } from '../context/CheckItemContext';
import { reasonListText } from '@/components/strategy-task-detail/review-item-result';
import { StrategyReviewType, StrategyTaskStatus, StrategyTaskType } from '@/constants/strategy';
import UnqualifiedResonModal from '@/pages/report/components/patrolReportDetail/OptionsDetail/unqualifiedResonModal';
import ComplaintsDescTacticsModal from '@/pages/report/components/selfReportDetail/OptionsDetail/ComplaintsDescTacticsModal';
import { AppealStatus } from '@/pages/task/complaints/const';

const ReviewItemResult: FC = () => {
  const { taskStatus, itemType, reviewItemSnap, result, drawerPlacement, reviewType, data, reportAppealInfoData } =
    useCheckItemContext();

  const [drawerProps, setDrawerProps] = useState<{ open: boolean; title?: string; content?: ReactNode }>({
    open: false,
  });

  const currentAppealItem = useMemo(() => {
    return reportAppealInfoData?.reportAppealItems?.find((f) => f.taskItemId === data?.taskItemId);
  }, [data?.taskItemId, reportAppealInfoData?.reportAppealItems]);

  // 申诉信息
  const renderAppealInfo = ({ appealStatus }: { appealStatus: AppealStatus }) => {
    return (
      <div className="flex gap-x-1 items-center my-3">
        <span className=" text-red-400">{AppealStatusTextMap[appealStatus]}</span>
        {[AppealStatus.通过, AppealStatus.驳回, AppealStatus.已过期].includes(appealStatus) && (
          <Button
            type="primary"
            size="small"
            onClick={(e) => {
              e.stopPropagation();

              ComplaintsDescTacticsModal.showModal({
                data: {
                  taskId: currentAppealItem?.taskId,
                  baseTaskId: reportAppealInfoData?.baseTaskId,
                  taskItemId: currentAppealItem?.taskItemId,
                },
              });
            }}
          >
            点击查看详情
          </Button>
        )}
      </div>
    );
  };

  // 是否为ai识别
  /* const isAiIdentify = result?.some((s: { autoReviewed: boolean }) => s?.autoReviewed);

  const resultData = useMemo(() => {
    if (isAiIdentify) {
      // 一点为ai识别且为合格 则无需展示后续点评结果
      return result?.[0]?.autoReviewed && result?.[0]?.itemJudge ? [result?.[0] || {}] : [result?.[1] || {}];
    } else {
      return result;
    }
  }, [isAiIdentify, result]); */

  return (
    <>
      {/* 没有点评快照，则无需点评,效期检查表没有点评 */}
      {taskStatus !== StrategyTaskStatus.EXPIRED &&
        itemType !== StrategyTaskType.VALIDITY &&
        reviewType === StrategyReviewType.NO_REVIEWS_NEEDED && (
          <div className=" h-8 rounded border-[0.5px] border-solid border-[#DCDCDC] bg-[#DCDCDC] px-4 leading-8 text-center text-sm text-white mb-2.5 inline-block min-w-[50%]">
            无需点评
          </div>
        )}

      {result?.map(
        ({ itemJudge, itemRemark, itemOtherReason, itemReason, itemScore, hasApply }: any, index: number) => {
          const allReasons = itemOtherReason ? [...(itemReason || []), itemOtherReason] : itemReason || [];

          return (
            <div className="mb-2.5">
              {/* 点评标识+序号 */}
              <div className="mb-2.5 rounded-sm border-[0.5px] border-[#FAFAFA] px-2 py-1 text-sm font-medium">
                点评{index + 1}
              </div>
              {/* 点评说明+不合格原因 */}
              {(itemRemark || itemOtherReason || itemReason) && (
                <div className="mb-2.5 rounded-lg bg-[#FAFAFA] px-[9px] py-[7px] text-sm leading-6 text-[#858585]">
                  {itemRemark && <span className="text-sm">{itemRemark}</span>}
                  {allReasons?.length > 0 && (
                    <div
                      className="break-all  ellipsis-2 "
                      onClick={() => {
                        UnqualifiedResonModal.showModal({
                          data: {
                            selectReasons: allReasons,
                            style: {
                              left: 10,
                              margin: 0,
                            },
                          },
                        });
                      }}
                    >
                      {reasonListText(allReasons)}
                    </div>
                  )}
                </div>
              )}
              {reviewItemSnap?.standard ? (
                <div
                  className="mb-2.5 text-sm text-primary cursor-pointer"
                  onClick={() => {
                    setDrawerProps({
                      open: true,
                      title: '点评标准',
                      content: reviewItemSnap?.standard,
                    });
                  }}
                >
                  点评标准
                </div>
              ) : null}
              {/* 点评结果 */}
              {/* 判断型 */}
              {reviewItemSnap?.type === StrategyReviewType.MANUAL_JUDGMENT &&
                (!hasApply ? (
                  <div
                    className={classNames(
                      'text-white bg-[#2da55d] h-8 rounded text-center leading-8 px-3 min-w-[50%] inline-block',
                    )}
                  >
                    不适用
                  </div>
                ) : reviewItemSnap?.displayScore ? (
                  itemJudge ? ( // 显示分数-合格
                    <div
                      className={classNames(
                        'text-white bg-[#2da55d] h-8 rounded text-center leading-8 px-3 min-w-[50%] inline-block',
                      )}
                    >
                      {`${reviewItemSnap?.normalButtonName} (${itemScore})分`}
                    </div>
                  ) : (
                    // 显示分数-不合格
                    <div
                      className={classNames(
                        'text-white bg-[#f53f3f] h-8 rounded text-center leading-8 px-3 min-w-[50%] inline-block',
                      )}
                    >
                      {`${reviewItemSnap?.abnormalButtonName} (0)分`}
                    </div>
                  )
                ) : itemJudge ? ( // 不显示分数-合格
                  <div
                    className={classNames(
                      'text-white bg-[#2da55d] h-8 rounded text-center leading-8 px-3 min-w-[50%] inline-block',
                    )}
                  >
                    {`${reviewItemSnap?.normalButtonName}`}
                  </div>
                ) : (
                  // 不显示分数-不合格
                  <div
                    className={classNames(
                      'text-white bg-[#f53f3f] h-8 rounded text-center leading-8 px-3 min-w-[50%] inline-block',
                    )}
                  >
                    {`${reviewItemSnap?.abnormalButtonName}`}
                  </div>
                ))}
              {reviewItemSnap?.type === StrategyReviewType.MANUAL_SCORING &&
                (!hasApply ? (
                  <div
                    className={classNames(
                      'text-white bg-[#2da55d] h-8 rounded text-center leading-8 px-3 min-w-[50%] inline-block',
                    )}
                  >
                    不适用
                  </div>
                ) : itemJudge ? ( // 显示分数-合格
                  <div
                    className={classNames(
                      'text-white bg-[#2da55d] h-8 rounded text-center leading-8 px-3 min-w-[50%] inline-block',
                    )}
                  >
                    合格
                  </div>
                ) : (
                  // 显示分数-不合格
                  <div
                    className={classNames(
                      'text-white bg-[#f53f3f] h-8 rounded text-center leading-8 px-3 min-w-[50%] inline-block',
                    )}
                  >
                    {`${itemScore}分`}
                  </div>
                ))}
              {!!currentAppealItem &&
                index === 0 &&
                renderAppealInfo({ appealStatus: currentAppealItem?.appealStatus })}
              <div />
            </div>
          );
        },
      )}

      <UnqualifiedResonModal />

      <Drawer
        open={drawerProps?.open}
        title={drawerProps?.title}
        width={400}
        placement={drawerPlacement}
        onClose={() => {
          setDrawerProps({ open: false });
        }}
      >
        {drawerProps?.content}
      </Drawer>

      <ComplaintsDescTacticsModal />
    </>
  );
};

export default ReviewItemResult;
