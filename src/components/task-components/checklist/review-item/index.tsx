import { FC, ReactNode, useMemo, useState } from 'react';
import { Drawer } from 'antd';
import { isBoolean } from 'lodash';
import ReviewJudgeItem from './ReviewJudgeItem';
import ReviewScoreItem from './ReviewScoreItem';
import { useCheckItemContext } from '../context/CheckItemContext';
import { StrategyReviewType } from '@/constants/strategy';

export interface ReviewItemProps {
  onInappropriate?: () => void;
  onDisqualified?: (values: any) => void;
  onQualified?: (values: any) => void;
  isDisqualified?: boolean;
  isQualified?: boolean;
  isInappropriate?: boolean;
}

const ReviewItem: FC = () => {
  const { reviewItemSnap, onSave, reviewTaskId, id, reviewResult, drawerPlacement } = useCheckItemContext();

  const [drawerProps, setDrawerProps] = useState<{ open: boolean; title?: string; content?: ReactNode }>({
    open: false,
  });

  const handleSave = (dto: any) => {
    onSave?.({ ...dto, taskId: reviewTaskId, taskItemId: id });
  };

  const onDisqualified = (dto: any) => {
    handleSave?.({ ...dto, hasApply: true, itemJudge: false });
  };

  const onQualified = (dto: any) => {
    handleSave?.({ ...dto, hasApply: true, itemJudge: true });
  };

  const onInappropriate = () => {
    handleSave?.({ hasApply: false, itemJudge: undefined, itemScore: undefined });
  };
  const { isDisqualified, isQualified, isInappropriate } = useMemo(() => {
    return {
      isDisqualified: isBoolean(reviewResult?.itemJudge) && !reviewResult?.itemJudge,
      isQualified: isBoolean(reviewResult?.itemJudge) && reviewResult?.itemJudge,
      isInappropriate: isBoolean(reviewResult?.hasApply) && !reviewResult?.hasApply,
    };
  }, [reviewResult]);

  return (
    <div className="mb-2.5">
      {reviewItemSnap ? (
        <div className="mb-2.5">
          {reviewItemSnap?.type === StrategyReviewType.MANUAL_JUDGMENT && (
            <ReviewJudgeItem
              onDisqualified={onDisqualified}
              onQualified={onQualified}
              onInappropriate={onInappropriate}
              isDisqualified={isDisqualified}
              isQualified={isQualified}
              isInappropriate={isInappropriate}
            />
          )}
          {reviewItemSnap?.type === StrategyReviewType.MANUAL_SCORING && (
            <ReviewScoreItem
              onDisqualified={onDisqualified}
              onQualified={onQualified}
              onInappropriate={onInappropriate}
              isDisqualified={isDisqualified}
              isQualified={isQualified}
              isInappropriate={isInappropriate}
            />
          )}
        </div>
      ) : (
        <div className=" h-8 rounded border-[0.5px] border-solid border-[#DCDCDC] bg-[#DCDCDC] px-4 py-2 mb-2.5 ">
          <div className="text-sm text-white inline-block  min-w-[50%]">无需点评</div>
        </div>
      )}
      {reviewItemSnap?.standard ? (
        <div
          className="mb-2.5 text-sm text-primary cursor-pointer"
          onClick={() => {
            setDrawerProps({
              title: '点评标准',
              open: true,
              content: reviewItemSnap?.standard,
            });
          }}
        >
          点评标准
        </div>
      ) : null}
      <Drawer
        open={drawerProps?.open}
        title={drawerProps?.title}
        width={400}
        placement={drawerPlacement}
        onClose={() => {
          setDrawerProps({ open: false });
        }}
      >
        {drawerProps?.content}
      </Drawer>
    </div>
  );
};

export default ReviewItem;
