import { FC } from 'react';
import { Button } from 'antd';
import classNames from 'classnames';
import { useCheckItemContext } from '../context/CheckItemContext';
import { ReviewItemProps } from '.';
import useDisqualificationModal from '@/components/strategy-task-detail/use-disqualification-modal';

export interface ReviewJudgeItemProps extends ReviewItemProps {}

const ReviewJudgeItem: FC<ReviewJudgeItemProps> = ({
  onDisqualified,
  onQualified,
  onInappropriate,
  isDisqualified,
  isQualified,
  isInappropriate,
}) => {
  const { reviewItemSnap, reviewResult, copyUserOptions, hasNeedCommitIssueInstructions, modalStyle } =
    useCheckItemContext();
  const { abnormalButtonName, displayScore, normalButtonName, scoreMax } = reviewItemSnap;

  const { showModal } = useDisqualificationModal();

  return (
    <>
      <div>
        <Button
          className={classNames(
            'rounded border-[0.5px] border-solid  mr-3 px-4 py-2 h-8',
            isDisqualified ? 'bg-[#f53f3f] border-[#f53f3f] text-white' : 'border-[#DCDCDC] bg-white',
          )}
          onClick={() => {
            showModal({
              title: `${hasNeedCommitIssueInstructions ? '整改' : '情况'}说明`,
              reviewItem: reviewItemSnap,
              reviewResult,
              copyUserOptions,
              hasNeedCommitIssueInstructions,
              onOk: (values) => {
                onDisqualified?.({ itemScore: 0, ...values });

                return Promise.resolve();
              },
              style: {
                ...modalStyle,
              },
            });
          }}
        >
          {displayScore ? `${abnormalButtonName} (0分)` : abnormalButtonName}
        </Button>
        <Button
          className={classNames(
            'rounded border-[0.5px] border-solid  mr-3 px-4 py-2 h-8',
            isQualified ? 'bg-[#378bff] border-[#378bff] text-white' : 'border-[#DCDCDC] bg-white',
          )}
          onClick={() => {
            onQualified?.({ itemScore: scoreMax });
          }}
        >
          {displayScore ? `${normalButtonName} (${scoreMax}分)` : normalButtonName}
        </Button>
        <Button
          className={classNames(
            'rounded border-[0.5px] border-solid  px-4 py-2 h-8',
            isInappropriate ? 'bg-[#378bff] border-[#378bff] text-white' : 'border-[#DCDCDC] bg-white ',
          )}
          onClick={onInappropriate}
        >
          不适用
        </Button>
      </div>
    </>
  );
};

export default ReviewJudgeItem;
