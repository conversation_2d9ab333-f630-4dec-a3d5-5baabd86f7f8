import { PlusOutlined } from '@ant-design/icons';
import { checkAllowAlbum } from './PatrolItem';
import { useCheckItemContext } from '../context/CheckItemContext';
import MediaCard from '@/components/MediaCard';
import useSituationDescriptionModal from '@/pages/taskCenter/cloud/hooks/use-situation-description-modal';

export type SceneRectifyProps = {
  onRectify?: (data: any) => void;
};

const SceneRectify = ({ onRectify }: SceneRectifyProps) => {
  const { worksheetItem, systemConfig, data: taskResult, issueItemSnap } = useCheckItemContext();
  const { showModal } = useSituationDescriptionModal();

  const { uploadImageMethod } = worksheetItem;

  return (
    <div
      className="mb-2.5 overflow-hidden rounded bg-[#FAFAFA] px-2 py-3"
      onClick={() => {
        showModal({
          title: '当场整改情况说明',
          showConfig: {
            camera: true,
            remark: true,
          },
          mustConfig: {
            camera: issueItemSnap?.reformMustUpload,
            remark: false,
            disabledAlbum: !checkAllowAlbum(uploadImageMethod, systemConfig), // 是否允许使用相册
          },
          initialValues: {
            itemImages: taskResult?.issueAtOnceImages?.map((item: any) => {
              return {
                type: item?.contentType,
                uid: item?.id,
                status: 'done',
                thumbUrl: item?.snapshotUrl,
                url: null,
                response: {
                  contentType: item?.contentType,
                  id: item?.id,
                  key: item?.id,
                  name: item?.name,
                  snapshotUrl: item?.snapshotUrl,
                  url: item?.url,
                },
              };
            }),
            itemRemark: taskResult?.issueAtOnceRemark,
          },
          onOk: (data) => {
            onRectify?.({
              issueAtOnceImages: data?.itemImages?.map(({ response }: any) => ({
                ...response,
              })),
              issueAtOnceRemark: data?.itemRemark,
            });

            return Promise.resolve();
          },
        });
      }}
    >
      <div className="mb-3.5  text-sm text-[#5B6A91]">
        {taskResult?.issueAtOnceRemark ? taskResult?.issueAtOnceRemark : '当场整改情况说明'}
      </div>
      <div className="flex flex-row gap-1">
        {taskResult?.issueAtOnceImages?.map((item) => (
          <MediaCard file={item} key={item.id} fileList={taskResult?.issueAtOnceImages} width={55} height={55} />
        ))}
        <div className="flex size-14 items-center justify-center rounded border-[0.5px] border-solid border-[#DCDCDC]">
          <PlusOutlined />
        </div>
      </div>
    </div>
  );
};

export default SceneRectify;
