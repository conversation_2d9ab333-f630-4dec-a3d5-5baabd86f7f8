import { FC } from 'react';
import { Button } from 'antd';
import classNames from 'classnames';
import { PatrolItemProps } from './PatrolItem';
import { useCheckItemContext } from '../context/CheckItemContext';

// 根据检查项配置和后台配置，综合判断是否允许使用相册上传

const PatrolJudgeItem: FC<PatrolItemProps> = ({
  isDisqualified,
  isQualified,
  isInappropriate,
  onDisqualified,
  onInappropriate,
  onQualified,
}) => {
  const { worksheetItem } = useCheckItemContext();
  const { unqualifiedButtonName, normalButtonName, displayScore, scoreMax } = worksheetItem;

  return (
    <>
      <div className="flex gap-3 flex-wrap">
        <Button
          className={classNames(
            'rounded border-[0.5px] border-solid   px-4 py-2 h-8',
            isDisqualified ? 'bg-[#f53f3f] border-[#f53f3f] text-white' : 'border-[#DCDCDC] bg-white',
          )}
          onClick={() => {
            onDisqualified?.();
          }}
        >
          {displayScore ? `${unqualifiedButtonName} (0分)` : unqualifiedButtonName}
        </Button>
        <Button
          className={classNames(
            'rounded border-[0.5px] border-solid   px-4 py-2 h-8',
            isQualified ? 'bg-[#378bff] border-[#378bff] text-white' : 'border-[#DCDCDC] bg-white',
          )}
          onClick={() => {
            onQualified?.();
          }}
        >
          {displayScore ? `${normalButtonName} (${scoreMax}分)` : normalButtonName}
        </Button>
        <Button
          className={classNames(
            'rounded border-[0.5px] border-solid  px-4 py-2 h-8',
            isInappropriate ? 'bg-[#378bff] border-[#378bff] text-white' : 'border-[#DCDCDC] bg-white ',
          )}
          onClick={onInappropriate}
        >
          不适用
        </Button>
      </div>
    </>
  );
};

export default PatrolJudgeItem;
