import { FC } from 'react';
import Patrol from './patrol';
import Self from './self';
import Validity from './validity';
import { useCheckItemContext } from '../../context/CheckItemContext';
import { StrategyTaskType } from '@/constants/strategy';

const CheckItemResult: FC = () => {
  const { itemType } = useCheckItemContext();

  return (
    <>
      {itemType === StrategyTaskType.SELF && <Self />}
      {itemType === StrategyTaskType.PATROL && <Patrol />}
      {itemType === StrategyTaskType.VALIDITY && <Validity />}
    </>
  );
};

export default CheckItemResult;
