import { FC } from 'react';
import { Button } from 'antd';
import { isBoolean } from 'lodash';
import { useCheckItemContext } from '../../context/CheckItemContext';
import MediaCard from '@/components/MediaCard';
import { SelfCheckItemActionType, SelfCheckItemAfterActionMethod } from '@/constants/strategy';

// 自检结果
const Self: FC = () => {
  const checkItem = useCheckItemContext();

  const { data: taskResult, worksheetItem, differentItemLabel } = checkItem || {};

  const { normalButtonName, actionType, unqualifiedButtonName, afterActionMethod, afterActionType } = worksheetItem;

  return (
    <>
      {(actionType === SelfCheckItemActionType.PHOTOGRAPH ||
        (actionType === SelfCheckItemActionType.JUDGE &&
          afterActionType === SelfCheckItemActionType.PHOTOGRAPH &&
          (afterActionMethod === SelfCheckItemAfterActionMethod.ALL ||
            afterActionMethod === SelfCheckItemAfterActionMethod.WHEN_ABNORMAL) &&
          isBoolean(taskResult?.itemJudge) &&
          !taskResult?.itemJudge)) &&
        (taskResult?.itemImages?.length || taskResult?.itemRemark) && (
          <div className="mb-2.5 flex gap-2 bg-[#f2f2f2] p-2 flex-wrap">
            {taskResult?.itemImages?.map((item) => (
              <MediaCard file={item} key={item.id} fileList={taskResult?.itemImages} width={155} height={155} />
            ))}
            {taskResult?.itemRemark && <div className=" text-sm font-medium">{taskResult?.itemRemark}</div>}
          </div>
        )}
      {actionType === SelfCheckItemActionType.JUDGE &&
        afterActionType === SelfCheckItemActionType.PHOTOGRAPH &&
        (afterActionMethod === SelfCheckItemAfterActionMethod.ALL ||
          afterActionMethod === SelfCheckItemAfterActionMethod.WHEN_NORMAL) &&
        isBoolean(taskResult?.itemJudge) &&
        taskResult?.itemJudge &&
        (taskResult?.qualifiedItemImages?.length || taskResult?.qualifiedRemark) && (
          <div className="mb-2.5 flex gap-2 flex-wrap">
            {taskResult?.qualifiedItemImages?.map((item) => (
              <MediaCard file={item} key={item.id} fileList={taskResult?.qualifiedItemImages} width={65} height={65} />
            ))}
            {taskResult?.qualifiedRemark && <div className=" text-sm font-medium">{taskResult?.qualifiedRemark}</div>}
          </div>
        )}

      {actionType === SelfCheckItemActionType.JUDGE &&
        (taskResult?.itemJudge ? (
          <div className="text-white bg-[#2da55d] h-8 rounded text-center leading-8 px-3 min-w-[50%] inline-block mb-2.5">
            {normalButtonName}
          </div>
        ) : (
          <>
            <div className="text-white bg-[#f53f3f] h-8 rounded text-center leading-8 px-3 min-w-[50%] inline-block mb-2.5">
              {unqualifiedButtonName}
            </div>
            {(taskResult?.itemOtherReason || taskResult?.itemReason?.length) && (
              <div className="mb-2.5 text-sm font-medium">
                {taskResult?.itemOtherReason || taskResult?.itemReason?.[0]}
              </div>
            )}
          </>
        ))}
      {(actionType === SelfCheckItemActionType.GAP_FILLING ||
        (actionType === SelfCheckItemActionType.JUDGE &&
          afterActionType === SelfCheckItemActionType.GAP_FILLING &&
          (afterActionMethod === SelfCheckItemAfterActionMethod.ALL ||
            afterActionMethod === SelfCheckItemAfterActionMethod.WHEN_ABNORMAL) &&
          isBoolean(taskResult?.itemJudge) &&
          !taskResult?.itemJudge)) && <div className="mb-2.5 text-sm font-medium">{taskResult?.itemRemark}</div>}
      {actionType === SelfCheckItemActionType.JUDGE &&
        afterActionType === SelfCheckItemActionType.GAP_FILLING &&
        (afterActionMethod === SelfCheckItemAfterActionMethod.ALL ||
          afterActionMethod === SelfCheckItemAfterActionMethod.WHEN_NORMAL) &&
        isBoolean(taskResult?.itemJudge) &&
        taskResult?.itemJudge && <div className="mb-2.5 text-sm font-medium">{taskResult?.qualifiedRemark}</div>}

      {differentItemLabel === 'DIFFERENT' && isBoolean(taskResult?.hasApply) && !taskResult?.hasApply && (
        <div>
          <Button disabled className="mb-2">
            不适用
          </Button>
        </div>
      )}
    </>
  );
};

export default Self;
