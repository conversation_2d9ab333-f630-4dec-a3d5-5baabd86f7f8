import { FC, ReactNode, useMemo, useState } from 'react';
import { <PERSON><PERSON>, Drawer, Image } from 'antd';
import { isBoolean } from 'lodash';
import { useCheckItemContext } from '../../context/CheckItemContext';
import MediaCard from '@/components/MediaCard';
import { PatrolCheckItemType, StrategyRectificationMethod, StrategyRoutineType } from '@/constants/strategy';
import { getFoodSafetyTutorItemDetail } from '@/http/apis/task-center';

// 巡检结果
const Patrol: FC = () => {
  const {
    data: taskResult,
    worksheetItem,
    issueItemSnap,
    drawerPlacement,
    baseTaskId,
    id,
    taskSubType,
    newWindow,
  } = useCheckItemContext();
  const { type, normalButtonName, unqualifiedButtonName } = worksheetItem;
  const [drawerProps, setDrawerProps] = useState<{ open: boolean; content?: ReactNode }>({
    open: false,
  });

  const reasons = useMemo(() => {
    return taskResult?.itemOtherReason
      ? [...(taskResult?.itemReason || []), taskResult?.itemOtherReason]
      : taskResult?.itemReason || [];
  }, [taskResult?.itemReason, taskResult?.itemOtherReason]);

  return (
    <>
      <div className="mb-2.5">
        {type === PatrolCheckItemType.JUDGE &&
          isBoolean(taskResult?.itemJudge) &&
          (taskResult?.itemJudge ? (
            <div className="text-white bg-[#2da55d] h-8 rounded text-center leading-8 px-3 min-w-[50%] inline-block">
              {normalButtonName}
            </div>
          ) : (
            <div className="text-white bg-[#f53f3f] h-8 rounded text-center leading-8 px-3 min-w-[50%] inline-block">
              {unqualifiedButtonName}
            </div>
          ))}
        {type === PatrolCheckItemType.SCORE &&
          isBoolean(taskResult?.itemJudge) &&
          (taskResult?.itemJudge ? (
            <div className="text-white bg-[#2da55d] h-8 rounded text-center leading-8 px-3 min-w-[50%] inline-block">
              {`${normalButtonName} (${taskResult?.itemScore}分)`}
            </div>
          ) : (
            <div className="text-white bg-[#f53f3f] h-8 rounded text-center leading-8 px-3 min-w-[50%] inline-block">
              {`${unqualifiedButtonName} (${taskResult?.itemScore}分)`}
            </div>
          ))}
        {[PatrolCheckItemType.JUDGE, PatrolCheckItemType.SCORE].includes(type as PatrolCheckItemType) &&
          !taskResult?.hasApply && (
            <div className="text-white bg-[#2da55d] h-8 rounded text-center leading-8 px-3 min-w-[50%] inline-block">
              不适用
            </div>
          )}
      </div>
      {/* 巡检说明+不合格原因 */}
      {((isBoolean(taskResult?.itemJudge) &&
        ((!taskResult?.itemJudge && taskResult?.itemRemark) ||
          (taskResult?.itemJudge && taskResult?.qualifiedRemark))) ||
        (isBoolean(taskResult?.itemJudge) &&
          !taskResult.itemJudge &&
          (taskResult?.itemOtherReason || taskResult?.itemReason))) && (
        <div className="mb-2.5 rounded-lg bg-[#FAFAFA] px-[9px] py-[7px] text-sm leading-6 text-[#858585] break-words">
          {isBoolean(taskResult?.itemJudge) &&
            ((!taskResult?.itemJudge && taskResult?.itemRemark) ||
              (taskResult?.itemJudge && taskResult?.qualifiedRemark)) && (
              <div className="mb-1 text-sm">
                {taskResult?.itemJudge ? taskResult?.qualifiedRemark : taskResult?.itemRemark}
              </div>
            )}
          {isBoolean(taskResult?.itemJudge) && !taskResult.itemJudge && reasons?.length ? (
            <div
              className="flex-row flex-wrap"
              onClick={() => {
                setDrawerProps({
                  open: true,
                  content: (
                    <>
                      {reasons?.map((reason: string) => {
                        return <div className="text-sm mb-1 whitespace-pre-wrap break-words">{reason}</div>;
                      })}
                    </>
                  ),
                });
              }}
            >
              {reasons?.map((reason: string) => {
                return <div className="mr-1.5 text-sm text-[#378BFF] whitespace-pre-wrap break-words">#{reason}</div>;
              })}
            </div>
          ) : undefined}
        </div>
      )}
      {isBoolean(taskResult?.itemJudge) &&
        ((!taskResult?.itemJudge && taskResult?.itemImages?.length) ||
          (taskResult?.itemJudge && taskResult?.qualifiedItemImages)) && (
          <div className="flex flex-wrap gap-2.5 mb-2.5">
            {(taskResult?.itemJudge ? taskResult?.qualifiedItemImages : taskResult?.itemImages)?.map((item) => (
              <MediaCard
                file={item}
                key={item.id}
                fileList={taskResult?.itemJudge ? taskResult?.qualifiedItemImages : taskResult?.itemImages}
                width={100}
                height={100}
                newWindow={newWindow}
              />
            ))}
          </div>
        )}
      {taskResult?.itemJudge === false &&
        (StrategyRectificationMethod.AT_ONCE_ISSUE === issueItemSnap?.issueType ||
          (issueItemSnap?.issueType === StrategyRectificationMethod.SELECT_ISSUE &&
            taskResult?.issueConfigDTO?.issueType === 'SELECT_AT_ONCE_ISSUE')) && (
          <div className="mt-3 overflow-hidden rounded bg-[#FAFAFA] px-2 py-3">
            <div className="mb-2.5 text-sm leading-4">当场整改情况说明</div>
            {taskResult?.issueAtOnceRemark && (
              <div className="mb-3 text-sm leading-4 text-[#5B6A91]">{taskResult?.issueAtOnceRemark}</div>
            )}
            <div className="mb-2.5 flex gap-2">
              {taskResult?.issueAtOnceImages?.map((item) => (
                <MediaCard
                  file={item}
                  key={item.id}
                  fileList={taskResult?.issueAtOnceImages}
                  width={100}
                  height={100}
                  newWindow={newWindow}
                />
              ))}
            </div>
          </div>
        )}

      {taskSubType === StrategyRoutineType.FOOD_SAFETY_ARRIVE_SHOP && (
        <Button
          className="mb-3"
          type="primary"
          onClick={() => {
            getFoodSafetyTutorItemDetail({ id: baseTaskId, taskItemId: id }).then((data: any) => {
              setDrawerProps({
                open: true,
                content: (
                  <>
                    {data?.map((item) => {
                      return item?.itemRemark ||
                        item.itemReason?.length ||
                        item?.itemOtherReason ||
                        item?.itemImages?.length ? (
                        <div className="bg-[#FAFAFA] p-3 mb-4 rounded-md w-[356px]">
                          {item?.itemRemark ? <div className="break-words">{item.itemRemark}</div> : null}
                          {item.itemReason ? (
                            <div className="flex flex-row flex-wrap">
                              {item.itemReason.map((reason) => (
                                <div className="text-[#5B6A91] mr-2 break-words">#{reason}</div>
                              ))}
                            </div>
                          ) : null}
                          {item.itemOtherReason ? (
                            <div className="text-[#5B6A91] mr-2 break-words">#{item.itemOtherReason}</div>
                          ) : null}
                          <div className="mt-2 gap-1 flex flex-row flex-wrap">
                            {item?.itemImages?.length > 0
                              ? item?.itemImages.map((img) => (
                                  <Image
                                    width={80}
                                    className="rounded-md"
                                    src={img.snapshotUrl}
                                    preview={{
                                      src: img?.url,
                                    }}
                                  />
                                ))
                              : null}
                          </div>
                        </div>
                      ) : undefined;
                    })}
                  </>
                ),
              });
            });
          }}
        >
          不合格原因
        </Button>
      )}

      <Drawer
        open={drawerProps?.open}
        title="不合格原因"
        placement={drawerPlacement}
        push={false}
        width={400}
        onClose={() => {
          setDrawerProps({ open: false });
        }}
      >
        {drawerProps?.content}
      </Drawer>
    </>
  );
};

export default Patrol;
