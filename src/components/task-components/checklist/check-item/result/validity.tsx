import { FC } from 'react';
import { CheckCircleOutlined } from '@ant-design/icons';
import { Image, Tag } from 'antd';
import classNames from 'classnames';
import dayjs from 'dayjs';
import { useCheckItemContext } from '../../context/CheckItemContext';
import { ValidityStatusCN } from '@/components/strategy-task-detail/check-item-result';

export enum ValidityStatus {
  VALID = 'VALID',
  TODAY_PAST_DUE = 'TODAY_PAST_DUE',
  PAST_DUE = 'PAST_DUE',
}
export enum SaleStatus {
  UNSOLD = 'UNSOLD',
  STOCKOUT = 'STOCKOUT',
  FILLED = 'FILLED',
  NOT_FILLED = 'NOT_FILLED',
}

const SaleStatusCN = {
  [SaleStatus.UNSOLD]: '未售',
  [SaleStatus.STOCKOUT]: '缺货',
};

// 效期结果
const Validity: FC = () => {
  const { data: taskResult, remark, worksheetItemContent, validityDetails } = useCheckItemContext();
  const { validityStatus } = taskResult || {};

  return (
    <div>
      <div className="text-sm text-[#000000] mb-2">{worksheetItemContent}</div>
      {remark && <div className="text-xs text-[#858585] mb-2">{remark}</div>}
      {SaleStatusCN?.[validityStatus] && (
        <div className="mb-2">
          <Tag color="warning">{SaleStatusCN?.[validityStatus]}</Tag>
        </div>
      )}
      {validityDetails?.map((item: any, sequence: number) => {
        const time = item.expirationTime ? dayjs(item.expirationTime).format('YYYY/MM/DD HH:mm') : '';

        return (
          <>
            <div
              key={`${worksheetItemContent}_${item.expirationTime}_${sequence}`}
              className={classNames(' mb-2 mt-0 flex-row items-center justify-between rounded-md p-2', {
                'bg-[#F5F5F5]': item.status === ValidityStatus.VALID,
                'bg-[#fef2f2]': item.status === ValidityStatus.PAST_DUE,
                'bg-[#fff8ef]': item.status === ValidityStatus.TODAY_PAST_DUE,
              })}
            >
              <div className="flex justify-between items-center">
                <div
                  className={classNames('ml-2 text-sm font-medium ', {
                    'text-[#EA0000]': item.status === ValidityStatus.PAST_DUE,
                    'text-[#FBA238]': item.status === ValidityStatus.TODAY_PAST_DUE,
                    'text-[#3DB86D]': item.status === ValidityStatus.VALID || item?.imageId || item?.imageUrl,
                  })}
                >
                  {item.imageId ? (
                    <div className="flex items-center text-[#3DB86D]">
                      <CheckCircleOutlined className="mr-2" />
                      已过期且处理
                    </div>
                  ) : (
                    ValidityStatusCN[item.status as ValidityStatus]
                  )}
                </div>
                <div className="flex-row items-center">
                  <div className="ml-sm text-sm text-[#B8B8B8]">{time || '请核对识别结果'}</div>
                </div>
              </div>
            </div>
            {item?.image && (
              <div className="flex items-center pb-2">
                <Image width={200} src={item?.image?.url} />
              </div>
            )}
          </>
        );
      })}
    </div>
  );
};

export default Validity;
