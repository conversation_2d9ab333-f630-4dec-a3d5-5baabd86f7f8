import { Button } from 'antd';
import classNames from 'classnames';
import { isBoolean } from 'lodash';
import { PatrolItemProps } from './PatrolItem';
import { useCheckItemContext } from '../context/CheckItemContext';

const PatrolScoreItem = ({
  isDisqualified,
  isQualified,
  isInappropriate,
  onDisqualified,
  onInappropriate,
  onQualified,
}: PatrolItemProps) => {
  const { worksheetItem, data: taskResult } = useCheckItemContext();

  const { displayScore, scoreMax } = worksheetItem;

  return (
    <>
      <div className="flex gap-3 flex-wrap">
        <Button
          className={classNames('rounded border-[0.5px] border-solid border-[#DCDCDC]  px-4 py-2 h-8 text-sm', {
            'bg-[#f53f3f] border-[#f53f3f] text-white': isDisqualified,
          })}
          onClick={() => {
            onDisqualified?.({ showScore: true });
          }}
        >
          {isBoolean(taskResult?.itemJudge) && !taskResult?.itemJudge && taskResult?.itemScore
            ? `${taskResult?.itemScore} 分`
            : '打分'}
        </Button>
        <Button
          className={classNames('rounded border-[0.5px] border-solid border-[#DCDCDC]   px-4 py-2 h-8 text-sm', {
            'bg-[#378bff] border-[#378bff] text-white': isQualified,
          })}
          onClick={() => {
            onQualified?.();
          }}
        >
          {displayScore ? `合格 (${scoreMax}分)` : '合格'}
        </Button>
        <Button
          className={classNames(
            'rounded border-[0.5px] border-solid  px-4 py-2 h-8',
            isInappropriate ? 'bg-[#378bff] border-[#378bff] text-white' : 'border-[#DCDCDC] bg-white ',
          )}
          onClick={onInappropriate}
        >
          不适用
        </Button>
      </div>
    </>
  );
};

export default PatrolScoreItem;
