import { FC, useMemo } from 'react';
import { isBoolean, isNumber } from 'lodash';
import PatrolJudgeItem from './PatrolJudgeItem';
import PatrolScoreItem from './PatrolScoreItem';
import SceneRectify from './SceneRectify';
import { useCheckItemContext } from '../context/CheckItemContext';
import { PatrolCheckItemType, StrategyRectificationMethod } from '@/constants/strategy';
import useQuerySearchParams from '@/hooks/use-query-search-params';
import useSituationDescriptionModal from '@/pages/taskCenter/cloud/hooks/use-situation-description-modal';
import { parserParams } from '@/utils/convert';

export interface PatrolItemProps {
  isDisqualified?: boolean;
  isQualified?: boolean;
  isInappropriate?: boolean;
  onInappropriate?: () => void;
  onDisqualified?: (data?: any) => void;
  onQualified?: (data?: any) => void;
}

// eslint-disable-next-line react-refresh/only-export-components
export const checkAllowAlbum = (uploadImageMethod: any, config: any) => {
  if (uploadImageMethod === 'ALLOW') {
    return true;
  } else if (uploadImageMethod === 'NOT_ALLOW') {
    return false;
  } else {
    if (config.ALLOW_ALBUM === '1') {
      return true;
    } else {
      return false;
    }
  }
};

const PatrolItem: FC = () => {
  const {
    data: taskResult,
    worksheetItem,
    onSave,
    baseTaskId,
    id,
    copyUsers,
    issueItemSnap,
    systemConfig,
    newWindow,
    modalWidth,
    drawerPlacement,
  } = useCheckItemContext();
  const {
    unqualifiedMustUpload,
    normalMustUpload,
    unqualifiedReasons,
    unqualifiedMustSelectReason,
    uploadImageMethod,
    scoreMax,
  } = worksheetItem;

  const [searchParams] = useQuerySearchParams();
  const urlParams = parserParams(searchParams || {});

  const { showModal } = useSituationDescriptionModal();

  const showDisqualified = ({ showScore }: { showScore?: boolean }) => {
    showModal({
      newWindow,
      title: '不合格情况说明',
      style: {
        left: 10,
        margin: 0,
      },
      width: modalWidth,
      drawerPlacement,
      scoreMax,
      showConfig: {
        camera: true,
        remark: true,
        rectify: issueItemSnap?.reformEnable && issueItemSnap?.issueType !== StrategyRectificationMethod.AT_ONCE_ISSUE,
        reason: true,
        score: showScore,
      },
      mustConfig: {
        camera: unqualifiedMustUpload,
        remark: false,
        reason: unqualifiedMustSelectReason,
        disabledAlbum: !checkAllowAlbum(uploadImageMethod, systemConfig), // 是否允许使用相册
        issueType: issueItemSnap?.issueType === StrategyRectificationMethod.SELECT_ISSUE,
      },
      reasonList: unqualifiedReasons?.map((item: any) => ({
        label: item?.reason,
        value: item?.reason,
      })),
      readOnlyReformLimit: true,
      initialValues: {
        ...taskResult,
        /**
        reformLimit:
          taskResult?.reformLimit ||
          urlParams?.reformLimit ||
          systemConfig?.IN_STORE_INSPECTION_NOT_QUALIFIED_ISSUE_ITEM_DEFAULT_DEADLINE,
        */
        reformLimit: systemConfig?.REPORT_VIDEO_NOT_QUALIFIED_ISSUE_ITEM_DEFAULT_RECTIFY_DEADLINE,
        itemReason: taskResult?.itemOtherReason ? [...(taskResult?.itemReason || []), 'OTHER'] : taskResult?.itemReason,
        issueType: taskResult?.issueConfigDTO?.issueType,
        loopRectifyType: taskResult?.issueConfigDTO?.loopRectifyType,
        isLoopRectify: isBoolean(taskResult?.issueConfigDTO?.isLoopRectify)
          ? Number(taskResult?.issueConfigDTO?.isLoopRectify)
          : undefined,
        loopEndTime: taskResult?.issueConfigDTO?.loopEndTime,
        itemImages: taskResult?.itemImages?.map((item: any) => {
          return {
            type: item?.contentType,
            uid: item?.id,
            status: 'done',
            thumbUrl: item?.snapshotUrl,
            url: null,
            response: {
              contentType: item?.contentType,
              id: item?.id,
              key: item?.id,
              name: item?.name,
              snapshotUrl: item?.snapshotUrl,
              url: item?.url,
            },
          };
        }),
      },
      copyUsers,
      onOk: (values) => {
        handleSave({
          ...taskResult,
          ...values,
          itemReason: values?.itemReason?.filter((reason: string) => reason !== 'OTHER'),
          itemOtherReason: values?.itemOtherReason,
          itemScore: showScore ? values?.itemScore : 0,
          itemImages: values?.itemImages?.map((item: any) => ({ ...item?.response })),
          issueConfigDTO: {
            issueType: values?.issueType,
            loopRectifyType: values?.loopRectifyType,
            isLoopRectify: isNumber(values?.isLoopRectify) ? !!values?.isLoopRectify : undefined,
            loopEndTime: values?.loopEndTime,
          },
          hasApply: true,
          itemJudge: false,
        });

        return Promise.resolve();
      },
    });
  };

  const showQualified = () => {
    showModal({
      newWindow,
      drawerPlacement,
      title: '合格情况说明',
      showConfig: {
        camera: true,
        remark: true,
        rectify: false,
      },
      style: {
        left: 10,
        margin: 0,
      },
      width: modalWidth,
      mustConfig: {
        camera: normalMustUpload,
        remark: false,
        disabledAlbum: !checkAllowAlbum(uploadImageMethod, systemConfig), // 是否允许使用相册
      },
      initialValues: {
        ...taskResult,
        itemImages: taskResult?.qualifiedItemImages?.map((item: any) => {
          return {
            type: item?.contentType,
            uid: item?.id,
            status: 'done',
            thumbUrl: item?.snapshotUrl,
            url: null,
            response: {
              contentType: item?.contentType,
              id: item?.id,
              key: item?.id,
              name: item?.name,
              snapshotUrl: item?.snapshotUrl,
              url: item?.url,
            },
          };
        }),
        itemRemark: taskResult?.qualifiedRemark,
      },
      onOk: (values) => {
        handleSave?.({
          ...taskResult,
          ...values,
          itemScore: scoreMax,
          itemImages: taskResult?.itemImages,
          itemRemark: taskResult?.itemRemark,
          qualifiedItemImages: values?.itemImages?.map((item: any) => ({ ...item?.response })),
          qualifiedRemark: values?.itemRemark,
          hasApply: true,
          itemJudge: true,
        });

        return Promise.resolve();
      },
    });
  };

  const { isDisqualified, isQualified, isInappropriate } = useMemo(() => {
    return {
      isDisqualified: isBoolean(taskResult?.itemJudge) && !taskResult?.itemJudge,
      isQualified: isBoolean(taskResult?.itemJudge) && taskResult?.itemJudge,
      isInappropriate: isBoolean(taskResult?.hasApply) && !taskResult?.hasApply,
    };
  }, [taskResult]);

  const handleSave = (dto: any) => {
    onSave?.({ ...dto, baseTaskId, id });
  };

  const onInappropriate = () => {
    handleSave?.({ hasApply: false, itemJudge: undefined, itemScore: undefined });
  };

  return (
    <>
      <div className="mb-2.5">
        {worksheetItem?.type === PatrolCheckItemType.JUDGE && (
          <PatrolJudgeItem
            isDisqualified={isDisqualified}
            isQualified={isQualified}
            isInappropriate={isInappropriate}
            onInappropriate={onInappropriate}
            onDisqualified={() => {
              showDisqualified?.({ showScore: false });
            }}
            onQualified={() => {
              if (normalMustUpload) {
                showQualified();
              } else {
                handleSave({ ...taskResult, itemScore: scoreMax, hasApply: true, itemJudge: true });
              }
            }}
          />
        )}
        {worksheetItem?.type === PatrolCheckItemType.SCORE && (
          <PatrolScoreItem
            isDisqualified={isDisqualified}
            isQualified={isQualified}
            isInappropriate={isInappropriate}
            onInappropriate={onInappropriate}
            onDisqualified={() => {
              showDisqualified?.({ showScore: true });
            }}
            onQualified={() => {
              if (normalMustUpload) {
                showQualified();
              } else {
                handleSave({ ...taskResult, itemScore: scoreMax, hasApply: true, itemJudge: true });
              }
            }}
          />
        )}
      </div>
      {taskResult?.itemJudge === false &&
        (StrategyRectificationMethod.AT_ONCE_ISSUE === issueItemSnap?.issueType ||
          (issueItemSnap?.issueType === StrategyRectificationMethod.SELECT_ISSUE &&
            taskResult?.issueConfigDTO?.issueType === 'SELECT_AT_ONCE_ISSUE')) && (
          <SceneRectify
            onRectify={(values: any) => {
              handleSave?.({ ...taskResult, ...values });
            }}
          />
        )}
    </>
  );
};

export default PatrolItem;
