import { ReactNode, useRef } from 'react';
import { Button, Modal, ModalProps } from 'antd';
import { NotFilledItemHandleType } from '@/constants/strategy';

const usePreviewModal = () => {
  const modalRef: any = useRef();

  const showModal = ({
    title,
    content,
    onOk,
    validate,
    style,
  }: {
    title: ModalProps['title'];
    content: ReactNode;
    onOk: (value: NotFilledItemHandleType) => void;
    validate: (value?: NotFilledItemHandleType) => Promise<boolean>;
    style?: React.CSSProperties;
  }) => {
    return (modalRef.current = Modal.info({
      title,
      closable: true,
      okText: '保存',
      cancelText: '取消',
      icon: null,
      width: 600,
      content,
      footer: (
        <div className="flex justify-end mt-4">
          <Button
            type="primary"
            className="mr-2"
            onClick={() => {
              onOk?.(NotFilledItemHandleType.SET_FULL_SCORE);
              modalRef?.current?.destroy();
            }}
          >
            设为"合格"(得满分)
          </Button>
          <Button
            className="mr-2"
            onClick={() => {
              validate?.(NotFilledItemHandleType.SET_NOT_APPLY).then((result: boolean) => {
                if (result) {
                  onOk?.(NotFilledItemHandleType.SET_NOT_APPLY);
                  modalRef?.current?.destroy();
                }
              });
            }}
          >
            设为"不适用"(不计分)
          </Button>
          <Button
            onClick={() => {
              modalRef?.current?.destroy();
            }}
          >
            返回继续填写
          </Button>
        </div>
      ),
      style,
    }));
  };

  return {
    showModal,
  };
};

export default usePreviewModal;
