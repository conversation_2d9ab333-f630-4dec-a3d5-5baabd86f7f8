import { FC } from 'react';
import AnnularChart from './annular-chart';
import StripChart from './strip-chart';
import useConfig from '@/mobx/config';

export interface SummaryProps {
  data?: any;
}

enum ConfigEnum {
  item = 'item',
  category = 'category',
}

const ConfigEnumTotalCalc: Record<ConfigEnum, (data: any) => string> = {
  [ConfigEnum.item]: (data) => {
    // 检查项合格率=满分的检查项数目 / 总的检查项数目
    return ((data?.qualifiedItemCount / data?.itemCount) * 100)?.toFixed(2)?.toString();
  },
  [ConfigEnum.category]: (data) => {
    // 分类合格率=满分的分类数目 / 总的分类数目
    let qualifiedCount = 0;

    data?.categoryStatistics?.forEach(({ score, totalScore }: any) => {
      if (score === totalScore) {
        qualifiedCount++;
      }
    });

    return ((qualifiedCount / data?.categoryStatistics?.length) * 100)?.toFixed(2)?.toString();
  },
};

const ConfigEnumText: Record<ConfigEnum, (data: any) => string> = {
  [ConfigEnum.item]: (data) => {
    return `合格项${data?.qualifiedItemCount}/${data?.itemCount}`;
  },
  [ConfigEnum.category]: (data) => {
    // 分类合格率=满分的分类数目 / 总的分类数目
    let qualifiedCount = 0;

    data?.categoryStatistics?.forEach(({ score, totalScore }: any) => {
      if (score === totalScore) {
        qualifiedCount++;
      }
    });

    return `合格分类${qualifiedCount}/${data?.categoryStatistics?.length}`;
  },
};
const Summary: FC<SummaryProps> = ({ data }) => {
  const { config }: any = useConfig();

  return (
    <div className="bg-white px-4 pt-4 pb-3 mb-2.5">
      <div className="flex justify-between leading-4">
        <h4 className="text-base leading-4 text-[#141414] font-medium">
          汇总
          <span className="ml-[6px] bg-[url('@/assets/images/info-circle.svg')] inline-block w-[14px] h-[14px] bg-no-repeat bg-contain" />
        </h4>
        <span className="text-[#858585] text-xs leading-4">得分已自动折算为百分制</span>
      </div>
      <div className="flex justify-center mb-2">
        <div className="mr-[40px]">
          <AnnularChart
            color="#378BFF"
            label="报告得分"
            progress={data?.percentScore}
            integer={data?.percentScore?.toFixed(2)?.toString()?.split('.')?.[0]}
            decimals={data?.percentScore?.toFixed(2)?.toString()?.split('.')?.[1]}
          />
          <div className="mt-3 text-center text-xs text-[#5E5E5E]">
            实际分数{data?.score}/{data?.totalScore}
          </div>
        </div>
        <div>
          <AnnularChart
            color="#FFAE4E"
            label="合格率"
            progress={+ConfigEnumTotalCalc?.[config?.REPORT_SHOW_PASS_RATE as ConfigEnum]?.(data) || 0}
            integer={ConfigEnumTotalCalc?.[config?.REPORT_SHOW_PASS_RATE as ConfigEnum]?.(data)?.split('.')?.[0]}
            decimals={ConfigEnumTotalCalc?.[config?.REPORT_SHOW_PASS_RATE as ConfigEnum]?.(data)?.split('.')?.[1]}
            symbol="%"
          />
          <div className="mt-3 text-center text-xs text-[#5E5E5E]">
            {ConfigEnumText?.[config?.REPORT_SHOW_PASS_RATE as ConfigEnum]?.(data)}
          </div>
        </div>
      </div>
      <div className=" mx-[10px] text-[#5E5E5E]">
        <div className=" text-xs leading-3 flex justify-between">
          <span>得分</span>
          <span>合格率</span>
        </div>
        {data?.categoryStatistics?.map(({ itemCount, name, qualifiedItemCount, score, totalScore, id }: any) => {
          return (
            <StripChart
              key={id}
              leftPercent={(score / totalScore) * 100}
              rightPercent={(qualifiedItemCount / itemCount) * 100}
              title={name}
              leftLabel={`${score}/${totalScore}`}
              rightLabel={`${((qualifiedItemCount / itemCount) * 100)?.toFixed(2)}%`}
            />
          );
        })}
      </div>
    </div>
  );
};

export default Summary;
