import { FC } from 'react';
import { Input, Tag } from 'antd';

export interface ExamDetailProps {
  confirm?: boolean;
  remark?: string;
}

const ExamDetail: FC<ExamDetailProps> = ({ confirm, remark }) => {
  return (
    <div className="mt-4 p-3 bg-white mb-3">
      <h4 className="text-base">考试情况确认</h4>
      <div className="my-4">
        <span className="text-[#f50]">*</span>
        <span className="mr-4">请确认门店是否都已完成并通过考试</span>
        {confirm ? <Tag color="#87d068">已确认</Tag> : <Tag color="#f50">未确认</Tag>}
      </div>
      <div className="flex">
        <span className="inline-flex w-[80px] leading-8">备注说明：</span>
        <Input.TextArea style={{ width: '100%', marginTop: 5 }} disabled={true} autoComplete="off" value={remark} />
      </div>
    </div>
  );
};

export default ExamDetail;
