import { FC } from 'react';
import { Input } from 'antd';

const { TextArea } = Input;

export interface SumUpResultProps {
  label: string;
  value: string;
}

const SumUpResult: FC<SumUpResultProps> = ({ label, value }) => {
  return (
    <div className="mb-2.5 w-full flex-1 bg-white p-3">
      <div className="mb-2.5">{label}</div>
      <TextArea
        className=" w-full rounded-md bg-[#FAFAFA] p-2 text-sm text-black break-words"
        disabled={true}
        value={value}
      />
    </div>
  );
};

export default SumUpResult;
