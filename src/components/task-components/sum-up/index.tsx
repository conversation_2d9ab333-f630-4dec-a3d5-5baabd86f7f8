import TextArea from 'antd/es/input/TextArea';
import classNames from 'classnames';

const SumUp = ({ value, onChange, label, className, placeholder, onBlur }: any) => {
  return (
    <div className={classNames('p-3 bg-white mb-2.5', className)}>
      <div className="mb-2.5">{label || '点评总结'}</div>
      <TextArea value={value} onChange={onChange} placeholder={placeholder} onBlur={onBlur} maxLength={300} />
    </div>
  );
};

export default SumUp;
