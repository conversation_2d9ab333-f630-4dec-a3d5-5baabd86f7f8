import { FC } from 'react';
import dayjs from 'dayjs';
import TaskInfoCell from './cell';
import { StrategyTaskStatusCN } from '@/constants/strategy';
import { SelfTaskStatus } from '@/pages/rectificationStrategy/self';

export interface TaskInfoBodyProps {
  data?: any;
}

const dateFormat: string = 'YYYY-MM-DD HH:mm:ss';
const TaskInfoBody: FC<TaskInfoBodyProps> = ({ data }) => {
  return (
    <>
      <TaskInfoCell label="任务单号" value={data?.taskId} />
      <TaskInfoCell label="任务名称" value={data?.taskName} />
      <TaskInfoCell
        label="任务状态"
        value={data?.taskStatus && StrategyTaskStatusCN[data?.taskStatus as SelfTaskStatus]}
      />
      <TaskInfoCell
        label="执行时段"
        value={`${dayjs(data?.taskStartTime).format(dateFormat)}~${dayjs(data?.taskEndTime).format(dateFormat)}`}
      />
      <TaskInfoCell label="签到时间" value={data?.signedTime ? `${dayjs(data?.signedTime).format(dateFormat)}` : '-'} />
      <TaskInfoCell label="签离时间" value={data?.leaveTime ? `${dayjs(data?.leaveTime).format(dateFormat)}` : '-'} />
      {data?.transferApplicant && data?.transferProcessor && (
        <TaskInfoCell
          label=""
          value={`原${data?.transferApplicant?.nickname}应处理任务,由${data?.transferProcessor?.nickname}转派`}
        />
      )}
      {data?.hikvisionUserName && (
        <TaskInfoCell label="被鉴定人" value={`${data?.hikvisionUserName}(${data?.hikUserFeishuNo})`} />
      )}
    </>
  );
};

export default TaskInfoBody;
