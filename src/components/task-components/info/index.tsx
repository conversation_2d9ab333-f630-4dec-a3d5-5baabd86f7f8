import { FC, ReactNode } from 'react';
import classNames from 'classnames';

export interface TaskInfoProps {
  className?: string;
  extra?: ReactNode;
  children?: ReactNode;
  data?: any;
  renderHeader?: () => ReactNode;
  renderBody?: () => ReactNode;
  renderFooter?: () => ReactNode;
}

const TaskInfo: FC<TaskInfoProps> = ({ className, extra, data, children, renderHeader, renderBody, renderFooter }) => {
  return (
    <div className="bg-white mb-2.5">
      <div className={classNames('p-3', className)}>
        {renderHeader?.()}
        <div className="flex justify-between">
          <h3 className="text-base font-medium leading-7 text-[#141414]">
            {data?.shopId} {data?.shopName}
          </h3>
          <div>{extra}</div>
        </div>
        {renderBody?.()}
        <div className="mt-1 rounded-lg bg-[#FAFAFA] px-[9px] py-[7px] text-sm leading-6 text-[#858585]">
          {children}
        </div>
      </div>
      {renderFooter?.()}
    </div>
  );
};

export default TaskInfo;
