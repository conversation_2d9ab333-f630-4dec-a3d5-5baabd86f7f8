import dayjs from 'dayjs';
import { cloneDeep, isBoolean } from 'lodash';
import { CheckItemAttribute } from '@/constants/checklist';
import {
  SelfCheckItemActionType,
  StrategyRectificationMethod,
  StrategyReviewType,
  StrategyTaskType,
} from '@/constants/strategy';

export const verifyReviewResult = (worksheets: any[]) => {
  // 已点评非必检项数量
  let reviewedOptionalCount = 0;
  // 未点评非必检项数量
  let notReviewedOptionalCount = 0;
  // 已点评必检项数量
  let reviewedNecessaryCount = 0;
  // 未点评必检项数量
  let notReviewedNecessaryCount = 0;

  worksheets.forEach((sheetItem) =>
    sheetItem.data.forEach((item: any) =>
      item?.data.forEach((subItem: any) => {
        const { reviewResult } = subItem;

        const reviewed = reviewResult?.hasApply === false || (reviewResult && isBoolean(reviewResult?.itemJudge));

        if (subItem?.accentedTermTags?.includes(CheckItemAttribute.NECESSARY)) {
          if (subItem?.reviewItemSnap?.type !== StrategyReviewType.NO_REVIEWS_NEEDED) {
            if (reviewed) {
              reviewedNecessaryCount += 1;
            } else {
              notReviewedNecessaryCount += 1;
            }
          }
        } else {
          if (subItem?.reviewItemSnap?.type !== StrategyReviewType.NO_REVIEWS_NEEDED) {
            if (reviewed) {
              reviewedOptionalCount += 1;
            } else {
              notReviewedOptionalCount += 1;
            }
          }
        }
      }),
    ),
  );

  return { reviewedOptionalCount, notReviewedOptionalCount, reviewedNecessaryCount, notReviewedNecessaryCount };
};

export const verifyPatrolResult = (worksheets: any[]) => {
  // 非必检项数量
  let optionalCount = 0;
  // 已填非必填项数量
  let filledOptionalCount = 0;
  // 未填非必填项数量
  let notFilledOptionalCount = 0;
  // 必检项数量
  let necessaryCount = 0;
  // 已填必填项数量
  let filledNecessaryCount = 0;
  // 未填必填项数量
  let notFilledNecessaryCount = 0;

  // 当场整改提醒文案
  let rectifyContent = '';

  worksheets.forEach((sheetItem) =>
    sheetItem.data.forEach((item: any) =>
      item?.data.forEach((subItem: any) => {
        const { issueItemSnap, data: taskResult, worksheetItemContent } = subItem;
        const filled = isBoolean(taskResult?.itemJudge) || (isBoolean(taskResult?.hasApply) && !taskResult?.hasApply);

        if (subItem?.accentedTermTags?.includes(CheckItemAttribute.NECESSARY)) {
          necessaryCount += 1;

          if (filled) {
            filledNecessaryCount += 1;
          } else {
            notFilledNecessaryCount += 1;
          }
        } else {
          optionalCount += 1;

          if (filled) {
            filledOptionalCount += 1;
          } else {
            notFilledOptionalCount += 1;
          }
        }

        // 巡检不合格，如果整改方式是当场整改，或者巡检人选择当场整改，则对数据进行校验提醒
        if (
          !rectifyContent &&
          taskResult?.itemJudge === false &&
          (StrategyRectificationMethod.AT_ONCE_ISSUE === issueItemSnap?.issueType ||
            (issueItemSnap?.issueType === StrategyRectificationMethod.SELECT_ISSUE &&
              taskResult?.issueConfigDTO?.issueType === 'SELECT_AT_ONCE_ISSUE'))
        ) {
          // 整改必须上传整改照片或视频
          if (issueItemSnap?.reformMustUpload) {
            if (!taskResult?.issueAtOnceImages?.length) {
              rectifyContent = `检查项：${worksheetItemContent}，必须上传当场整改照片或视频`;
            }
          } else if (!taskResult?.issueAtOnceImages?.length && !taskResult?.issueAtOnceRemark) {
            rectifyContent = `检查项：${worksheetItemContent}，请输入整改情况说明或上传当场整改照片或视频`;
          }
        }
      }),
    ),
  );

  return {
    optionalCount,
    filledOptionalCount,
    notFilledOptionalCount,
    necessaryCount,
    filledNecessaryCount,
    notFilledNecessaryCount,
    rectifyContent,
  };
};

export const verifyCheckItem = (item: any) => {
  const { itemType, data } = item;

  if (itemType === StrategyTaskType.SELF) {
    if (item?.worksheetItem?.actionType === SelfCheckItemActionType.PHOTOGRAPH) {
      if (data?.itemImages?.length) {
        return true;
      }
    } else if (item?.worksheetItem?.actionType === SelfCheckItemActionType.JUDGE) {
      if (isBoolean(data?.itemJudge)) {
        return true;
      }
    } else if (item?.worksheetItem?.actionType === SelfCheckItemActionType.GAP_FILLING) {
      if (data?.itemRemark) {
        return true;
      }
    }
  } else if (itemType === StrategyTaskType.PATROL) {
    if (data?.hasApply && isBoolean(data?.itemJudge)) {
      return true;
    }

    if (isBoolean(data?.hasApply) && !data?.hasApply) {
      return true;
    }
  }

  return false;
};

export const verifyAllIsApply = (worksheets: any[], isReview: boolean) => {
  const worksheetsClone = cloneDeep(worksheets);

  // 先将所有项设置成不适用
  worksheetsClone?.forEach((sheetItem) => {
    sheetItem.data.forEach((item: any) =>
      item?.data.forEach((subItem: any) => {
        if (isReview) {
          if (subItem?.reviewItemSnap?.type !== StrategyReviewType.NO_REVIEWS_NEEDED) {
            if (!subItem.reviewResult) {
              subItem.reviewResult = { hasApply: false };
            } else {
              if (!isBoolean(subItem?.reviewResult?.itemJudge)) {
                subItem.reviewResult.hasApply = false;
              }
            }
          }
        } else {
          if (!subItem.data) {
            subItem.data = { hasApply: false };
          } else {
            if (!isBoolean(subItem?.data?.itemJudge)) {
              subItem.data.hasApply = false;
            }
          }
        }
      }),
    );
  });

  // 再遍历所有项，只要有一张检查表全部是不适用，就提示“不能所有项都是不适用，请返回修改”
  const allIsNotApply: boolean = worksheetsClone?.some((sheetItem) => {
    return sheetItem?.data?.every((item: any) => {
      return item?.data?.every((subItem: any) => {
        if (isReview) {
          return subItem?.reviewResult?.hasApply === false;
        } else {
          const { data: taskResult } = subItem;

          return taskResult?.hasApply === false;
        }
      });
    });
  });

  return allIsNotApply;
};

// 校验当前时间是否超过点评时效
export const verifyOverdue = (endTime: number | string) => {
  return dayjs().diff(dayjs(endTime)) >= 0;
};
