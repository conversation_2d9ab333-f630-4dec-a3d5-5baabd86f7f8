import { isBoolean, isEmpty, isNil } from 'lodash';
import {
  SelfCheckItemActionType,
  StrategyReviewType,
  StrategyTaskStatus,
  StrategyTaskType,
} from '@/constants/strategy';

export const filterUnqualified = (item: any): boolean => {
  const { itemType, data } = item;

  if (itemType === StrategyTaskType.SELF) {
    if (item?.worksheetItem?.actionType === SelfCheckItemActionType.JUDGE) {
      if (data && isBoolean(data?.itemJudge) && !data?.itemJudge) {
        return false;
      }
    }
  } else if (itemType === StrategyTaskType.PATROL) {
    if (data && isBoolean(data?.itemJudge) && !data?.itemJudge) {
      return false;
    }
  }

  return true;
};

export const filterNotFilled = (item: any): boolean => {
  const { itemType, data } = item;

  if (itemType === StrategyTaskType.SELF) {
  } else if (itemType === StrategyTaskType.PATROL) {
    // if ((data?.hasApply === true || !data?.hasApply) && !isBoolean(data?.itemJudge)) {
    //   return false;
    // }

    if (isEmpty(data)) {
      return false;
    }

    if (!data.hasApply) {
      return true;
    }

    if (isNil(data.itemJudge)) {
      return false;
    }
  }

  return true;
};

export const filterReviewNotReview = (item: any, taskStatus: StrategyTaskStatus): boolean => {
  // 过滤无需点评的项
  const isNoReviewNeeded =
    taskStatus !== StrategyTaskStatus.EXPIRED &&
    item?.itemType !== StrategyTaskType.VALIDITY &&
    item?.reviewItemSnap?.type === StrategyReviewType.NO_REVIEWS_NEEDED;

  if (isNoReviewNeeded) {
    return true;
  }

  const { reviewResult } = item;

  // 未点评项
  if (isEmpty(reviewResult)) {
    return false;
  }

  // 已点评，但是选了不适用
  if (!reviewResult.hasApply) {
    return true;
  }

  // 已提交申请但未评判,返回false
  if (isNil(reviewResult.itemJudge)) {
    return false;
  }

  return true;
};

export const filterReportNotReviewed = (item: any): boolean => {
  if (item?.reviewType === StrategyReviewType.NO_REVIEWS_NEEDED) {
    return true;
  }

  if (
    [StrategyReviewType.MANUAL_JUDGMENT, StrategyReviewType.MANUAL_SCORING].includes(item?.reviewType) &&
    item?.result?.length > 0
  ) {
    return true;
  }

  return false;
};

export const filterReviewNotReviewAndUnqualified = (item: any): boolean => {
  const { reviewResult } = item;

  if (!filterUnqualified(item)) {
    if (!reviewResult?.hasApply || isNil(reviewResult?.itemJudge)) {
      return false;
    }
  }

  return true;
};
