import { FC } from 'react';

export interface PDFViewerProps {
  url: string;
  height?: string | number;
  width?: string | number;
}

const PDFViewer: FC<PDFViewerProps> = ({ url, height, width }) => {
  console.log(import.meta.env.VITE_BASE_URL, '=import.meta.env.VITE_BASE_URL');

  const src: string = `${import.meta.env.VITE_BASE_URL}pdf.html?pdfUrl=${btoa(url)}`;

  return <iframe title="PDF" src={src} width="100%" height="100%" style={{ height, width }} />;
};

export default PDFViewer;
