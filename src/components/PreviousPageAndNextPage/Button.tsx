import { useDebounceFn } from 'ahooks';
import { But<PERSON>, Select } from 'antd';
import { twMerge } from 'tailwind-merge';

/**
 * @description 上一页、下一页组件 - 适用于接口不返回 total 时使用
 * @param {object} props
 * @param {string} props.className 样式类名 - 可选
 * @param {number} props.dataLength 数据长度 - 必传
 * @param {number} props.pageNum 当前页码 - 必传
 * @param {number} props.pageSize 每页条数 - 可选 默认为 10条/页
 * @param {number} props.loading 按钮 loading - 可选
 * @param {number} props.showPageSizeChange 是否展示分页大小切换（默认展示）  - 可选
 * @param {({ pageNum, type }: { pageNum: number; type: 'prev' | 'next' }) => void} props.onChange 页码改变回调函数
 * @returns
 */
export default function PreviousPageAndNextPageButton({
  className,
  dataLength,
  pageNum = 1,
  pageSize = 10,
  loading = false,
  showPageSizeChange = true,
  onChange,
}: {
  className?: string;
  dataLength: number;
  pageNum: number;
  pageSize?: number;
  loading?: boolean;
  showPageSizeChange?: boolean;
  onChange: ({
    type,
    pageNum,
    pageSize,
  }: {
    type: 'prev' | 'next' | 'size';
    pageNum: number;
    pageSize?: number;
  }) => void;
}) {
  const { run: prev } = useDebounceFn(
    () => {
      onChange({ type: 'prev', pageNum: pageNum - 1, pageSize });
    },
    { wait: 500 },
  );

  const { run: next } = useDebounceFn(
    () => {
      onChange({ type: 'next', pageNum: pageNum + 1, pageSize });
    },
    { wait: 500 },
  );

  return (
    <div className={twMerge('flex gap-x-4 justify-end items-center mt-3', className)}>
      <Button type="primary" disabled={pageNum === 1 || loading} onClick={prev} loading={loading}>
        上一页
      </Button>
      <Button type="primary" disabled={dataLength < pageSize || loading} onClick={next} loading={loading}>
        下一页
      </Button>
      {showPageSizeChange && (
        <Select
          value={pageSize}
          loading={loading}
          disabled={loading}
          style={{
            width: 'auto',
          }}
          options={[10, 20, 50, 100].map((item) => ({ label: `${item} 条/页`, value: item }))}
          onChange={(pageSize) => {
            onChange({ type: 'size', pageNum, pageSize });
          }}
        />
      )}
    </div>
  );
}
