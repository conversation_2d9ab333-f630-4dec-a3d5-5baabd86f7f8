import { Button } from 'antd';
import { twMerge } from 'tailwind-merge';

/**
 * @description 上一页、下一页组件 - 适用于接口不返回 total 时使用
 * @param {object} props
 * @param {string} props.className 样式类名 - 可选
 * @param {number} props.dataLength 数据长度 - 必传
 * @param {number} props.pageNum 当前页码 - 必传
 * @param {number} props.pageSize 每页条数 - 可选 默认为 10条/页
 * @param {({ pageNum, type }: { pageNum: number; type: 'prev' | 'next' }) => void} props.onChange 页码改变回调函数
 * @returns
 */
export default function PreviousPageAndNextPage({
  className,
  dataLength,
  pageNum = 1,
  pageSize = 10,
  onChange,
}: {
  className?: string;
  dataLength: number;
  pageNum: number;
  pageSize?: number;
  showPageNum?: boolean;
  onChange: ({ pageNum, type }: { pageNum: number; type: 'prev' | 'next' }) => void;
}) {
  return (
    <div className={twMerge('flex gap-x-4 justify-end items-center', className)}>
      <Button
        className="p-0"
        type="link"
        disabled={pageNum === 1}
        onClick={() => {
          onChange({ type: 'prev', pageNum: pageNum - 1 });
        }}
      >
        上一页
      </Button>
      <Button
        className="p-0"
        type="link"
        disabled={dataLength < pageSize}
        onClick={() => {
          onChange({ type: 'next', pageNum: pageNum + 1 });
        }}
      >
        下一页
      </Button>
    </div>
  );
}
