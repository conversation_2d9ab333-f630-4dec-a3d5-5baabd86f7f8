import { FC } from 'react';
import { Input } from 'antd';

export interface PictureInputProps {
  onChange?: (file: File) => void;
}

const PictureInput: FC<PictureInputProps> = ({ onChange }) => {
  return (
    <Input
      placeholder="请粘贴图片"
      onPaste={async (evt) => {
        const clipboardItems = evt.clipboardData.items;
        const items: DataTransferItem[] = [].slice.call(clipboardItems).filter((item: DataTransferItem) => {
          return item.type.indexOf('image') !== -1;
        });

        if (!items?.length) {
          return;
        }

        const item = items?.[0];
        // Get the blob of image
        const blob = item.getAsFile();

        if (!blob) return;

        const file: any = new File([blob], blob.name, { type: item.type });

        onChange?.(file);
      }}
    />
  );
};

export default PictureInput;
