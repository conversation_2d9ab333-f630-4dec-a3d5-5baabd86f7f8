import { FC, ReactNode } from 'react';
import classNames from 'classnames';
import dayjs from 'dayjs';
import { StrategyTaskStatus, StrategyTaskStatusCN } from '@/constants/strategy';

export interface HeaderProps {
  data?: any;
  extra?: ReactNode;
  className?: string;
}

const Item = ({ label, value }: { label?: string; value?: ReactNode }) => {
  return (
    <div className="flex  flex-row">
      <span className="text-sm leading-6 text-[#858585] flex-shrink-0">{label && `${label}：`}</span>
      <span className="text-sm leading-6 text-[#858585]">{value}</span>
    </div>
  );
};

const dateFormat: string = 'YYYY-MM-DD HH:mm:ss';
const Header: FC<HeaderProps> = ({ data, extra, className }) => {
  return (
    <div className={classNames('bg-white p-3', className)}>
      <div className="flex flex-row justify-between">
        <span className="text-base font-medium leading-7 text-[#141414]">
          {data?.shopId} {data?.shopName}
        </span>
        {extra}
      </div>
      <div className="mt-1 rounded-lg bg-[#FAFAFA] px-[9px] py-[7px] text-sm leading-6 text-[#858585]">
        <Item label="任务单号" value={data?.taskId} />
        <Item label="任务名称" value={data?.taskName} />
        <Item
          label="任务状态"
          value={data?.taskStatus && StrategyTaskStatusCN[data?.taskStatus as StrategyTaskStatus]}
        />
        <Item
          label="执行时段"
          value={`${dayjs(data?.taskStartTime).format(dateFormat)}~${dayjs(data?.taskEndTime).format(dateFormat)}`}
        />
        {data?.transferApplicant && data?.transferProcessor && (
          <Item
            label=""
            value={`原${data?.transferApplicant?.nickname}应处理任务,由${data?.transferProcessor?.nickname}转派`}
          />
        )}
        {data?.hikvisionUserName && (
          <Item label="被鉴定人" value={`${data?.hikvisionUserName}(${data?.hikUserFeishuNo})`} />
        )}
      </div>
    </div>
  );
};

export default Header;
