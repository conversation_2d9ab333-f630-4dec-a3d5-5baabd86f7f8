import { FC } from 'react';

export interface SumUpResultProps {
  label: string;
  value: string;
}

const SumUpResult: FC<SumUpResultProps> = ({ label, value }) => {
  return (
    <div className="mb-2.5 w-full flex-1 bg-white p-3">
      <div className="mb-2.5">{label}</div>
      <div className=" w-full rounded-md bg-[#FAFAFA] p-2 text-sm text-black break-words">{value}</div>
    </div>
  );
};

export default SumUpResult;
