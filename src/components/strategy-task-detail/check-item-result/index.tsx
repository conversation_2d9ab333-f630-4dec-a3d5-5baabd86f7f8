import { FC, ReactNode } from 'react';
import { CheckCircleOutlined, ExclamationCircleOutlined } from '@ant-design/icons';
import { Image, Tag } from 'antd';
import classNames from 'classnames';
import dayjs from 'dayjs';
import { isBoolean } from 'lodash';
import { BelowStandardModal } from '../below-standard-modal';
import MediaCard from '@/components/MediaCard';
import {
  PatrolCheckItemType,
  SelfCheckItemActionType,
  SelfCheckItemAfterActionMethod,
  StrategyRectificationMethod,
  StrategyTaskType,
} from '@/constants/strategy';
import UnqualifiedResonModal from '@/pages/report/components/patrolReportDetail/OptionsDetail/unqualifiedResonModal';

export interface CheckItemResultProps {
  data: any;
  taskSubType: string;
}

export enum ValidityStatus {
  VALID = 'VALID',
  TODAY_PAST_DUE = 'TODAY_PAST_DUE',
  PAST_DUE = 'PAST_DUE',
}
export enum SaleStatus {
  UNSOLD = 'UNSOLD',
  STOCKOUT = 'STOCKOUT',
  FILLED = 'FILLED',
  NOT_FILLED = 'NOT_FILLED',
}

const SaleStatusCN = {
  [SaleStatus.UNSOLD]: '未售',
  [SaleStatus.STOCKOUT]: '缺货',
};

export const ValidityStatusCN: Record<ValidityStatus, string> = {
  [ValidityStatus.VALID]: '效期正常',
  [ValidityStatus.PAST_DUE]: '已过期',
  [ValidityStatus.TODAY_PAST_DUE]: '今日过期',
};

export const ValidityStatusNode: Record<ValidityStatus, ReactNode> = {
  [ValidityStatus.VALID]: <CheckCircleOutlined className="size-4" />,
  [ValidityStatus.PAST_DUE]: <ExclamationCircleOutlined className="size-4" />,
  [ValidityStatus.TODAY_PAST_DUE]: <ExclamationCircleOutlined className="size-4" />,
};

const CheckItemResultNode: Record<string, (data: CheckItemResultProps['data'], taskSubType: string) => ReactNode> = {
  [StrategyTaskType.SELF]: (data) => {
    const { data: taskResult, worksheetItem, differentItemLabel } = data;

    const { normalButtonName, actionType, unqualifiedButtonName, afterActionMethod, afterActionType } = worksheetItem;

    return (
      <>
        {(actionType === SelfCheckItemActionType.PHOTOGRAPH || // 拍照
          (actionType === SelfCheckItemActionType.JUDGE &&
            afterActionType === SelfCheckItemActionType.PHOTOGRAPH &&
            (afterActionMethod === SelfCheckItemAfterActionMethod.ALL || // 判断-都拍照
              (taskResult?.itemJudge && afterActionMethod === SelfCheckItemAfterActionMethod.WHEN_NORMAL) || // 判断-正常结果拍照
              (isBoolean(taskResult?.itemJudge) &&
                !taskResult?.itemJudge &&
                afterActionMethod === SelfCheckItemAfterActionMethod.WHEN_ABNORMAL)))) && // 判断-非正常结果拍照
          taskResult?.itemImages?.length > 0 && (
            <>
              <div className="mb-2.5 flex gap-2 bg-[#f2f2f2] p-2 flex-wrap">
                {taskResult?.itemImages?.map((item) => (
                  <MediaCard file={item} key={item.id} fileList={taskResult?.itemImages} width={155} height={155} />
                ))}
                {/* <Image.PreviewGroup>
                {taskResult?.itemImages?.map(({ url }) => <Image src={url} width={64} height={64} />)}
              </Image.PreviewGroup> */}
              </div>
              {taskResult?.itemRemark && <div className="my-2.5 text-sm font-medium">{taskResult?.itemRemark}</div>}
            </>
          )}
        {actionType === SelfCheckItemActionType.JUDGE &&
          taskResult &&
          (taskResult?.itemJudge ? (
            <div className="flex">
              <div
                className={classNames(
                  'rounded border-[0.5px] border-solid   px-4 flex items-center h-8 mb-2.5',
                  isBoolean(taskResult?.itemJudge) && taskResult?.itemJudge
                    ? 'bg-[#0928BB] border-[#0928BB]'
                    : 'border-[#DCDCDC] bg-white',
                )}
              >
                <span
                  className={classNames('text-xs leading-4', {
                    'text-white': isBoolean(taskResult?.itemJudge) && taskResult?.itemJudge,
                  })}
                >
                  {normalButtonName}
                </span>
              </div>
            </div>
          ) : (
            <>
              <div className="flex">
                <span
                  className={classNames(
                    'rounded border-[0.5px] border-solid   h-8 mb-2.5 flex items-center px-4 ',
                    isBoolean(taskResult?.itemJudge) && !taskResult?.itemJudge
                      ? 'bg-[#F93D39] border-[#F93D39]'
                      : 'border-[#DCDCDC] bg-white',
                  )}
                >
                  <span
                    className={classNames('text-xs leading-4', {
                      'text-white': isBoolean(taskResult?.itemJudge) && !taskResult?.temJudge,
                    })}
                  >
                    {unqualifiedButtonName}
                  </span>
                </span>
              </div>
              {(taskResult?.itemOtherReason || taskResult?.itemReason?.length) && (
                <div className="mb-2.5 text-sm font-medium">
                  {taskResult?.itemOtherReason || taskResult?.itemReason?.[0]}
                </div>
              )}
            </>
          ))}
        {(actionType === SelfCheckItemActionType.GAP_FILLING ||
          (actionType === SelfCheckItemActionType.JUDGE &&
            afterActionType === SelfCheckItemActionType.GAP_FILLING &&
            (afterActionMethod === SelfCheckItemAfterActionMethod.ALL || // 判断-都填空
              (taskResult?.itemJudge && afterActionMethod === SelfCheckItemAfterActionMethod.WHEN_NORMAL) || // 判断-正常结果填空
              (isBoolean(taskResult?.itemJudge) &&
                !taskResult?.itemJudge &&
                afterActionMethod === SelfCheckItemAfterActionMethod.WHEN_ABNORMAL)))) && (
          <div className="mb-2.5 text-sm font-medium">{taskResult?.itemRemark}</div>
        )}

        {differentItemLabel === 'DIFFERENT' &&
          isBoolean(taskResult?.hasApply) &&
          !taskResult?.hasApply &&
          !isBoolean(taskResult?.itemJudge) && (
            <div
              className={classNames(
                'rounded border-[0.5px] border-solid  px-4 flex h-8 mb-2.5  justify-center items-center bg-[#0928BB] border-[#0928BB]',
              )}
            >
              <span className={classNames('text-xs leading-4 text-white')}>不适用</span>
            </div>
          )}
      </>
    );
  },
  [StrategyTaskType.PATROL]: (data, taskSubType) => {
    const { data: taskResult, worksheetItem, issueItemSnap } = data;
    const { type, normalButtonName, unqualifiedButtonName } = worksheetItem;

    return (
      <>
        {type === PatrolCheckItemType.JUDGE &&
          isBoolean(taskResult?.itemJudge) &&
          (taskResult?.itemJudge ? (
            <div className="flex">
              <div
                className={classNames(
                  'rounded border-[0.5px] border-solid   px-4  h-8 mb-2.5 flex items-center',
                  isBoolean(taskResult?.itemJudge) && taskResult?.itemJudge
                    ? 'bg-[#0928BB] border-[#0928BB]'
                    : 'border-[#DCDCDC] bg-white',
                )}
              >
                <span
                  className={classNames('text-xs leading-4', {
                    'text-white': isBoolean(taskResult?.itemJudge) && taskResult?.itemJudge,
                  })}
                >
                  {normalButtonName}
                </span>
              </div>
            </div>
          ) : (
            <div className="flex">
              <div
                className={classNames(
                  'rounded border-[0.5px] border-solid px-4 h-8 mb-2.5 flex items-center',
                  isBoolean(taskResult?.itemJudge) && !taskResult?.itemJudge
                    ? 'bg-[#F93D39] border-[#F93D39]'
                    : 'border-[#DCDCDC] bg-white',
                )}
              >
                <span
                  className={classNames('text-xs leading-4', {
                    'text-white': isBoolean(taskResult?.itemJudge) && !taskResult?.temJudge,
                  })}
                >
                  {unqualifiedButtonName}
                </span>
              </div>
            </div>
          ))}
        {type === PatrolCheckItemType.SCORE &&
          isBoolean(taskResult?.itemJudge) &&
          (taskResult?.itemJudge ? (
            <div className="flex">
              <div
                className={classNames(
                  'rounded border-[0.5px] border-solid   px-4  h-8 mb-2.5 flex items-center',
                  isBoolean(taskResult?.itemJudge) && taskResult?.itemJudge
                    ? 'bg-[#0928BB] border-[#0928BB]'
                    : 'border-[#DCDCDC] bg-white',
                )}
              >
                <span
                  className={classNames('text-xs leading-4', {
                    'text-white': isBoolean(taskResult?.itemJudge) && taskResult?.itemJudge,
                  })}
                >
                  {`${normalButtonName} (${taskResult?.itemScore}分)`}
                </span>
              </div>
            </div>
          ) : (
            <div className="flex">
              <div
                className={classNames(
                  'rounded border-[0.5px] border-solid px-4  h-8 mb-2.5 flex items-center',
                  isBoolean(taskResult?.itemJudge) && !taskResult?.itemJudge
                    ? 'bg-[#F93D39] border-[#F93D39]'
                    : 'border-[#DCDCDC] bg-white',
                )}
              >
                <span
                  className={classNames('text-xs leading-4', {
                    'text-white': isBoolean(taskResult?.itemJudge) && !taskResult?.temJudge,
                  })}
                >
                  {`${unqualifiedButtonName} (${taskResult?.itemScore}分)`}
                </span>
              </div>
            </div>
          ))}
        {[PatrolCheckItemType.JUDGE, PatrolCheckItemType.SCORE].includes(type) &&
          isBoolean(taskResult?.hasApply) &&
          !taskResult?.hasApply && (
            <div
              className={classNames(
                'flex justify-center rounded border-[0.5px] border-solid bg-[#0928BB] border-[#0928BB]  px-4 py-2 h-8 mb-2.5 text-xs leading-4 text-white',
              )}
            >
              不适用
            </div>
          )}
        {/* 巡检说明+不合格原因 */}
        {(taskResult?.itemRemark || taskResult?.itemOtherReason || taskResult?.itemReason) && (
          <div className="mb-2.5 rounded-lg bg-[#FAFAFA] px-[9px] py-[7px] text-sm leading-6 text-[#858585]">
            {taskResult?.itemRemark && <div className="mb-1 text-sm">{taskResult?.itemRemark}</div>}
            {(taskResult?.itemOtherReason || taskResult?.itemReason) && (
              <div
                className="flex-row flex-wrap"
                onClick={() => {
                  const allReasons = taskResult?.itemOtherReason
                    ? [...(taskResult?.itemReason || []), taskResult?.itemOtherReason]
                    : taskResult?.itemReason;

                  UnqualifiedResonModal.showModal({
                    data: {
                      selectReasons: allReasons,
                    },
                  });
                }}
              >
                {([] as any)
                  .concat(
                    taskResult?.itemReason && [...taskResult?.itemReason],
                    taskResult?.itemOtherReason ? [taskResult?.itemOtherReason] : [],
                  )
                  .map((reason: string) => {
                    return <div className="mr-1.5 text-sm text-[#378BFF]">#{reason}</div>;
                  })}
              </div>
            )}
          </div>
        )}
        {taskResult?.itemImages?.length > 0 && (
          <div className="flex flex-wrap gap-2.5 mb-2.5">
            {taskResult?.itemImages?.map((item) => (
              <MediaCard file={item} key={item.id} fileList={taskResult?.itemImages} width={100} height={100} />
            ))}
          </div>
        )}
        {taskResult?.itemJudge === false &&
          (StrategyRectificationMethod.AT_ONCE_ISSUE === issueItemSnap?.issueType ||
            (issueItemSnap?.issueType === StrategyRectificationMethod.SELECT_ISSUE &&
              taskResult?.issueConfigDTO?.issueType === 'SELECT_AT_ONCE_ISSUE')) && (
            <div className="mt-3 overflow-hidden rounded bg-[#FAFAFA] px-2 py-3">
              <div className="mb-2.5 text-sm leading-4">当场整改情况说明</div>
              {taskResult?.issueAtOnceRemark && (
                <div className="mb-3 text-sm leading-4 text-[#5B6A91]">{taskResult?.issueAtOnceRemark}</div>
              )}
              <div className="mb-2.5 flex gap-2">
                {taskResult?.issueAtOnceImages?.map((item) => (
                  <MediaCard
                    file={item}
                    key={item.id}
                    fileList={taskResult?.issueAtOnceImages}
                    width={100}
                    height={100}
                  />
                ))}
              </div>
            </div>
          )}
        {taskSubType === 'FOOD_SAFETY_ARRIVE_SHOP' && (
          <BelowStandardModal id={data?.data?.baseTaskId} taskItemId={data?.data?.taskItemId} />
        )}
      </>
    );
  },
  [StrategyTaskType.VALIDITY]: (data) => {
    const {
      worksheetItemContent,
      remark,
      validityDetails,
      data: { validityStatus },
    } = data;

    console.log(data, '=data1111');

    return (
      <div>
        <div className="text-sm text-[#000000] mb-2">{worksheetItemContent}</div>
        {remark && <div className="text-xs text-[#858585] mb-2">{remark}</div>}
        {SaleStatusCN?.[validityStatus] && (
          <div className="mb-2">
            <Tag color="warning">{SaleStatusCN?.[validityStatus]}</Tag>
          </div>
        )}
        {validityDetails?.map((item: any, sequence: number) => {
          const time = item.expirationTime ? dayjs(item.expirationTime).format('YYYY/MM/DD HH:mm') : '';

          return (
            <>
              <div
                key={`${worksheetItemContent}_${item.expirationTime}_${sequence}`}
                className={classNames(' mb-2 mt-0 flex-row items-center justify-between rounded-md p-2', {
                  'bg-[#F5F5F5]': item.status === ValidityStatus.VALID,
                  'bg-[#fef2f2]': item.status === ValidityStatus.PAST_DUE,
                  'bg-[#fff8ef]': item.status === ValidityStatus.TODAY_PAST_DUE,
                })}
              >
                <div className="flex justify-between items-center">
                  <div
                    className={classNames('ml-2 text-sm font-medium ', {
                      'text-[#EA0000]': item.status === ValidityStatus.PAST_DUE,
                      'text-[#FBA238]': item.status === ValidityStatus.TODAY_PAST_DUE,
                      'text-[#3DB86D]': item.status === ValidityStatus.VALID || item?.imageId || item?.imageUrl,
                    })}
                  >
                    {item.imageId ? (
                      <div className="flex items-center text-[#3DB86D]">
                        <CheckCircleOutlined className="mr-2" />
                        已过期且处理
                      </div>
                    ) : (
                      ValidityStatusCN[item.status as ValidityStatus]
                    )}
                  </div>
                  <div className="flex-row items-center">
                    <div className="ml-sm text-sm text-[#B8B8B8]">{time || '请核对识别结果'}</div>
                  </div>
                </div>
              </div>
              {item?.image && (
                <div className="flex items-center pb-2">
                  <Image width={200} src={item?.image?.url} />
                </div>
              )}
            </>
          );
        })}
      </div>
    );
  },
};
const CheckItemResult: FC<CheckItemResultProps> = ({ data, taskSubType }) => {
  const { itemType } = data;

  return <>{CheckItemResultNode[itemType]?.(data, taskSubType)}</>;
};

export default CheckItemResult;
