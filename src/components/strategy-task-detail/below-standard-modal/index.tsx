import { useEffect, useState } from 'react';
import { useRequest } from 'ahooks';
import { Button, Image, Modal } from 'antd';
import { getFoodSafetyTutorItemDetail } from '@/http/apis/task-center';

interface BelowStandardModalProps {
  id: number;
  taskItemId: number;
}

export const BelowStandardModal = (props: BelowStandardModalProps) => {
  const [open, setOpen] = useState(false);
  const { data, refresh } = useRequest<any, any>(() => getFoodSafetyTutorItemDetail({ ...props }), {
    manual: true,
  });

  useEffect(() => {
    if (open) {
      refresh();
    }
  }, [open]);

  return (
    <>
      <Button className="mb-3" type="primary" onClick={() => setOpen(true)}>
        不合格原因
      </Button>
      <Modal title="不合格原因" open={open} footer={null} onCancel={() => setOpen(false)}>
        {data?.map((item) => (
          <div className="bg-[#FAFAFA] p-3 mt-4 rounded-md">
            {item.itemReason ? (
              <div className="flex flex-row flex-wrap">
                {item.itemReason.map((reason) => (
                  <div className="text-[#5B6A91] mr-2">#{reason}</div>
                ))}
              </div>
            ) : null}
            {item.itemOtherReason ? <div className="mt-1 text-[#5B6A91]">#{item.itemOtherReason}</div> : null}
            {item?.itemRemark ? <div className="mt-1">{item?.itemRemark}</div> : null}
            <div className="mt-2">
              {item?.itemImages?.length > 0
                ? item?.itemImages.map((img) => (
                    <Image
                      width={80}
                      className="rounded-md"
                      src={img.snapshotUrl}
                      preview={{
                        src: img?.url,
                      }}
                    />
                  ))
                : null}
            </div>
          </div>
        ))}
      </Modal>
    </>
  );
};
