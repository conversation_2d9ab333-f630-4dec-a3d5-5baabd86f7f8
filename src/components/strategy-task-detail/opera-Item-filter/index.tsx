import { FC } from 'react';
import { Checkbox } from 'antd';

export interface FilterProps {
  detailType: 'PATROL' | 'SELF';
  unqualified: boolean;
  setUnqualified: (v: boolean) => void;
}

const OperaItemFilter: FC<FilterProps> = ({ detailType, unqualified, setUnqualified }) => {
  return (
    <div className="flex justify-end pr-2">
      {detailType === 'PATROL' && (
        <Checkbox
          checked={unqualified}
          onChange={(e) => {
            setUnqualified(e.target.checked);
          }}
        >
          <div className="text-sm text-[#5E5E5E]">仅显示不合格项</div>
        </Checkbox>
      )}
    </div>
  );
};

export default OperaItemFilter;
