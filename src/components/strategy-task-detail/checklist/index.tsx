import './index.scss';
import { FC, ReactNode, useEffect, useMemo, useState } from 'react';
import { MenuFoldOutlined } from '@ant-design/icons';
import { Collapse, Tabs } from 'antd';
import classNames from 'classnames';
import dayjs from 'dayjs';
import { OperateStatusEnum } from '..';
import Notice from '@/assets/images/notice2.svg';
import { StrategyTaskStatus, StrategyTaskType } from '@/constants/strategy';

export interface ChecklistProps {
  data?: any;
  progressCheck?: (data: any) => boolean; // 进度校验
  filter?: (data: any, taskStatus?: StrategyTaskStatus) => boolean;
  onWorksheetChange?: (data: {
    id: number;
    type: StrategyTaskType;
    isOverdue: boolean;
    selfStatus?: string;
    isLastWorksheet?: boolean;
  }) => void;
  className?: string;
  taskStatus: StrategyTaskStatus;
  renderItem?: (item: any, index: number) => ReactNode;
  unqualified: boolean;
  operateStatus?: OperateStatusEnum;
  onWorksheetChangeCallback?: (data: any) => void;
  onlyShowNotReviewed?: boolean;
}

// 校验当前时间是否超过点评时效
export const verifyOverdue = (endTime: number | string) => {
  return dayjs().diff(dayjs(endTime)) >= 0;
};

const Checklist: FC<ChecklistProps> = ({
  data,
  progressCheck,
  filter,
  onWorksheetChange,
  className,
  taskStatus,
  renderItem,
  unqualified,
  operateStatus,
  onWorksheetChangeCallback,
  onlyShowNotReviewed,
}: ChecklistProps) => {
  const [showSidebar, setShowSidebar] = useState<boolean>(false);
  const [activeWorksheetId, setActiveWorksheetId] = useState<number>();
  const [activeCategoryId, setActiveCategoryId] = useState<number>();
  const [collapseActiveKey, setCollapseActiveKey] = useState<number[]>([]);
  const worksheets = useMemo(() => {
    // 检查表映射，key为id
    const worksheetMap: {
      [key: number | string]: {
        id: number;
        name?: string;
        categoryOptions?: any[];
        type: string;
        reviewExpiredTime?: string;
      };
    } = {};
    // 检查项分类映射，key为id
    const worksheetCategoryMap: {
      [key: number | string]: { id: number; name?: string; itemOptions?: any[] };
    } = {};
    // 检查项映射，key为id
    const worksheetItemMap: { [key: number | string]: any } = {};

    const worksheetOptions =
      data?.map(({ id, name, data: categories, supportOverdue, selfStatus, type, reviewExpiredTime }: any) => {
        worksheetMap[id] = {
          id,
          name,
          type,
          reviewExpiredTime,
          categoryOptions: categories?.map(({ id: categoryId, name: categoryName, data: items }: any) => {
            // 完成进度
            let progress: number = 0;

            const itemOptions = items?.map((item: any) => {
              worksheetItemMap[item?.worksheetItemId] = item;

              const worksheetItem = item?.worksheetItemSnap && JSON.parse(item?.worksheetItemSnap);

              const itemOption = {
                ...item,
                worksheetItem,
                supportOverdue,
              };

              if (progressCheck?.(itemOption)) {
                ++progress;
              }

              const hidden: boolean = filter?.(itemOption, taskStatus) || false;

              return { ...itemOption, hidden };
            });

            worksheetCategoryMap[categoryId] = {
              id: categoryId,
              name: categoryName,
              itemOptions,
            };

            return { value: categoryId!, label: categoryName!, itemOptions, progress };
          }),
        };

        return { label: name!, value: id!, supportOverdue, selfStatus, type, reviewExpiredTime };
      }) || [];

    // 只有一张检查表时，默认隐藏侧边栏，否则展示侧边栏
    if (worksheetOptions?.length === 1) {
      setShowSidebar(false);
    } else if (worksheetOptions?.length > 1) {
      setShowSidebar(true);
    }

    // 如果有未提交的项  默认打开第一个未提交的项，如果都已提交，默认打开第一项
    if (
      !activeWorksheetId &&
      !worksheetOptions?.some(({ value, selfStatus, type, reviewExpiredTime }) => {
        setActiveWorksheetId(value);
        onWorksheetChange?.({
          id: value,
          type,
          isOverdue: verifyOverdue(reviewExpiredTime!),
          selfStatus,
        });

        return selfStatus === 'UN_SUBMITTED';
      })
    ) {
      setActiveWorksheetId(worksheetOptions?.[0]?.value);

      onWorksheetChangeCallback?.(worksheetOptions?.[0]?.value);

      onWorksheetChange?.({
        id: worksheetOptions?.[0]?.value,
        type: worksheetOptions?.[0]?.type,
        isOverdue: verifyOverdue(worksheetOptions?.[0]?.reviewExpiredTime!),
        selfStatus: worksheetOptions?.[0]?.selfStatus,
      });
    }

    // if (worksheetOptions?.filter(({ selfStatus }) => selfStatus === 'UN_SUBMITTED')?.length <= 1) {
    //   setIsLastWorksheet(true);
    // }

    return { worksheetMap, worksheetCategoryMap, worksheetItemMap, worksheetOptions };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [data, activeWorksheetId, unqualified, onlyShowNotReviewed]);

  const { categoryOptions, categoryMap } = useMemo(() => {
    const categorys = worksheets?.worksheetMap?.[activeWorksheetId!]?.categoryOptions || [];

    const map: any = {};
    const allCategorys: any[] = [];
    const options = categorys?.map(({ value, label, itemOptions, progress }: any) => {
      allCategorys?.push({ value, label, itemOptions, progress });
      map[value] = { value, label, categorys: [{ value, label, itemOptions, progress }] };

      return { value, label };
    });

    if (categorys?.length > 1) {
      map[-1] = { label: '全部', value: -1, categorys: allCategorys };
      options?.unshift({ label: '全部', value: -1 });

      if (!activeCategoryId) {
        setActiveCategoryId(-1);
      }
    } else {
      if (!activeCategoryId) {
        setActiveCategoryId(categorys?.[0]?.value);
      }
    }

    return { categoryOptions: options, categoryMap: map };
  }, [activeWorksheetId, worksheets?.worksheetMap, activeCategoryId]);

  useEffect(() => {
    setCollapseActiveKey(categoryMap?.[activeCategoryId!]?.categorys?.map(({ value }) => value));
  }, [activeWorksheetId]);

  return (
    <div className="strategy-task-detail-checklist">
      <div className={classNames('flex flex-row flex-1 bg-white mb-2.5 min-h-[200px] relative p-3', className)}>
        {showSidebar && (
          <Tabs
            tabPosition="left"
            className="w-1/3"
            items={worksheets?.worksheetOptions?.map(
              ({ label, value, selfStatus, supportOverdue, reviewed, reviewExpiredTime }) => {
                return {
                  label: (
                    <div className="flex items-center max-w-full">
                      {operateStatus === OperateStatusEnum.REVIEW && (
                        <div
                          className={classNames('mr-2 size-[6px] rounded-md', {
                            'bg-[#3DB86D]': reviewed,
                            'bg-[#FBA238]': !reviewed,
                            'bg-[#EA0000]': reviewExpiredTime && dayjs(reviewExpiredTime).diff(dayjs()) < 0,
                          })}
                        />
                      )}
                      {operateStatus !== OperateStatusEnum.REVIEW && (
                        <div
                          className={classNames('mr-2 size-[6px] rounded-md', {
                            'bg-[#3DB86D]': selfStatus === 'SUBMITTED',
                            'bg-[#FBA238]': selfStatus === 'UN_SUBMITTED',
                            'bg-[#EA0000]':
                              !supportOverdue &&
                              selfStatus === 'UN_SUBMITTED' &&
                              [StrategyTaskStatus.EXPIRED, StrategyTaskStatus.EXPIRED_RUNNING].includes(taskStatus),
                          })}
                        />
                      )}
                      <div className="flex-1 flex-wrap">
                        <span
                          className={classNames('text-[14px] leading-[22px] text-[#141414] whitespace-pre-wrap', {
                            // 'text-[#378BFF] font-medium': isActive,
                          })}
                        >
                          {label}
                        </span>
                      </div>
                    </div>
                  ),
                  key: value,
                };
              },
            )}
            onChange={(value: any) => {
              const {
                categoryOptions: options,
                type,
                reviewExpiredTime,
                selfStatus,
              }: any = worksheets?.worksheetMap?.[value];

              setActiveWorksheetId(value);

              if (options?.length > 1) {
                setActiveCategoryId(-1);
              } else {
                setActiveCategoryId(options?.[0]?.value);
              }

              onWorksheetChange?.({
                id: value as number,
                type,
                isOverdue: verifyOverdue(reviewExpiredTime!),
                selfStatus,
              });
              onWorksheetChangeCallback?.(value);
            }}
          />
        )}
        <div className="flex-1 bg-white w-0">
          <div className="flex flex-col">
            {worksheets?.worksheetMap?.[activeWorksheetId!]?.reviewExpiredTime && (
              <div className="flex h-7 flex-row items-center gap-x-1 bg-[#FFF9ED] px-4">
                <img src={Notice} width={16} height={16} alt="" />
                {/* <Notice width={16} height={16} color="#FF7D00" /> */}
                <div className="text-xs text-[#FF7D00]">
                  须{dayjs(worksheets?.worksheetMap?.[activeWorksheetId!]?.reviewExpiredTime).format('MM月DD号 HH:mm')}
                  前完成点评
                </div>
              </div>
            )}
            {categoryOptions?.length > 1 && (
              <Tabs
                items={categoryOptions?.map(({ label, value, progress, itemOptions }: any) => {
                  return {
                    // label: `${label} (${progress}/${itemOptions?.length})`,
                    label,
                    key: value,
                  };
                })}
                onChange={(val: any) => {
                  setActiveCategoryId(val);
                }}
              />
            )}
            {!!categoryMap?.[activeCategoryId!]?.categorys?.length && (
              <Collapse
                expandIconPosition="end"
                ghost
                activeKey={collapseActiveKey}
                onChange={(e: string[]) => {
                  setCollapseActiveKey(e?.map((key) => Number(key)));
                }}
                // defaultActiveKey={categoryMap?.[activeCategoryId!]?.categorys?.map(({ value }) => value)}
                items={categoryMap?.[activeCategoryId!]?.categorys?.map(
                  ({ label, itemOptions, progress, value }: any) => {
                    return {
                      label,
                      key: `${value}`,
                      children: (
                        <div>
                          {itemOptions?.map((item: any, index: number) => {
                            return renderItem?.(item, index);
                          })}
                        </div>
                      ),
                    };
                  },
                )}
              />
            )}
          </div>
        </div>
        {/* 只有一张检查表的时候隐藏侧边栏开关 */}
        <div
          className={classNames('absolute bottom-24 left-2 z-10', {
            hidden: worksheets?.worksheetOptions?.length <= 1,
          })}
        >
          <MenuFoldOutlined
            onClick={() => {
              setShowSidebar(!showSidebar);
            }}
          />
        </div>
      </div>
    </div>
  );
};

export default Checklist;
