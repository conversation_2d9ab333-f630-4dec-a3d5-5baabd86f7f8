import { FC, useMemo } from 'react';
import { isBoolean } from 'lodash';
import LicenseComparePhoto from './LicenseComparePhoto';
import MediaCard from '@/components/MediaCard';
import { CheckItemAttributeTag } from '@/constants/checklist';
import { SelfCheckItemActionType, SelfCheckItemAfterActionMethod } from '@/constants/strategy';
import SopModal from '@/pages/report/components/patrolReportDetail/OptionsDetail/sopModal';

export interface CheckItemInfoProps {
  data: any;
  newWindow?: boolean;
  modalStyle?: any;
}

const CheckItemInfo: FC<CheckItemInfoProps> = ({ data, newWindow = false, modalStyle }) => {
  const {
    itemType,
    worksheetItem,
    accentedTermTags,
    sopId,
    worksheetItemContent,
    simpleImages,
    data: taskResult,
  } = data;

  const { actionType, afterActionMethod, afterActionType } = worksheetItem || {};

  const simpleImageList = useMemo(() => {
    // 有证照类型不展示示例图片
    if (data?.bizType) {
      return [];
    }

    return simpleImages?.map((v) => {
      return {
        ...v,
        contentType: 'IMAGE',
        name: '示例图片',
        id: v?.fileObject?.key,
      };
    });
  }, [data?.bizType, simpleImages]);

  return itemType !== 'VALIDITY' ? (
    <>
      {/* 左边是重点项，右边是SOP、参考标准等 */}
      <div className="flex flex-row justify-between">
        <div className="flex flex-row flex-wrap">{accentedTermTags?.map((key: any) => CheckItemAttributeTag[key])}</div>
        <div className="flex-row">
          {sopId ? (
            <span
              className="text-sm text-primary"
              onClick={() => {
                SopModal.showModal(
                  {
                    data: {
                      // standard: worksheetItem?.scoringStandard || '',
                      sopId,
                    },
                    style: modalStyle,
                  },
                  { title: '' },
                );
              }}
            >
              参考标准
            </span>
          ) : null}
          {worksheetItem?.scoringStandard ? (
            <span
              className="ml-2 text-sm text-primary"
              onClick={() => {
                SopModal.showModal(
                  {
                    data: {
                      standard: worksheetItem?.scoringStandard,
                    },
                    style: modalStyle,
                  },
                  { title: '评分标准' },
                );
              }}
            >
              评分标准
            </span>
          ) : null}
        </div>
      </div>
      {/* 检查项内容 */}
      <div className="my-2.5 flex flex-row items-center">
        <span className="text-sm font-medium leading-[22px] text-[#141414]">{worksheetItemContent}</span>
      </div>
      {/* 补充缺失的展示 */}
      {actionType === SelfCheckItemActionType.JUDGE &&
        afterActionType === SelfCheckItemActionType.PHOTOGRAPH &&
        (afterActionMethod === SelfCheckItemAfterActionMethod.ALL ||
          afterActionMethod === SelfCheckItemAfterActionMethod.WHEN_NORMAL) &&
        isBoolean(taskResult?.itemJudge) &&
        taskResult?.itemJudge &&
        (taskResult?.qualifiedItemImages?.length || taskResult?.qualifiedRemark) && (
          <div className="mb-2.5 flex gap-2 flex-wrap">
            {taskResult?.qualifiedItemImages?.map((item) => (
              <MediaCard file={item} key={item.id} fileList={taskResult?.qualifiedItemImages} width={65} height={65} />
            ))}
            {taskResult?.qualifiedRemark && <div className=" text-sm font-medium">{taskResult?.qualifiedRemark}</div>}
          </div>
        )}
      {/* 证照对比照片 */}
      <LicenseComparePhoto bizType={data?.bizType} newWindow={newWindow} shopLicenseDTO={data?.shopLicenseDTO} />
      {/* 示例图 */}
      {simpleImageList?.length > 0 && (
        <div className="flex flex-wrap gap-2 mb-5">
          {simpleImageList?.map((item) => {
            return (
              <MediaCard
                file={item}
                isSampleImg
                key={item.id}
                newWindow={newWindow}
                fileList={simpleImageList}
                width={50}
                height={50}
              />
            );
          })}
        </div>
      )}
    </>
  ) : null;
};

export default CheckItemInfo;
