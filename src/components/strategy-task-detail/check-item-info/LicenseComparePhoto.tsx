import { nanoid } from 'nanoid';
import MediaCard from '@/components/MediaCard';
import { BizTypeEnum, CheckItemProps } from '@/components/task-components/checklist/context/CheckItemContext';

export default function LicenseComparePhoto({
  bizType,
  shopLicenseDTO,
  newWindow,
}: Pick<CheckItemProps, 'bizType' | 'shopLicenseDTO'> & { newWindow?: boolean }) {
  if (!bizType || !shopLicenseDTO) {
    return null;
  }

  const licenseMap = {
    [BizTypeEnum.门店证照]: [
      {
        snapshotUrl: shopLicenseDTO?.shopLicenseUrl,
        url: shopLicenseDTO?.shopLicenseUrl,
      },
    ],
    [BizTypeEnum.食安证照]: [
      {
        snapshotUrl: shopLicenseDTO?.foodSafetyLicenseUrl,
        url: shopLicenseDTO?.foodSafetyLicenseUrl,
      },
    ],
    [BizTypeEnum['门店、食安证照']]: [
      {
        snapshotUrl: shopLicenseDTO?.shopLicenseUrl,
        url: shopLicenseDTO?.shopLicenseUrl,
      },
      {
        snapshotUrl: shopLicenseDTO?.foodSafetyLicenseUrl,
        url: shopLicenseDTO?.foodSafetyLicenseUrl,
      },
    ],
  };

  const simpleImages: any[] = (licenseMap?.[bizType] || [])?.map((m) => ({
    ...m,
    contentType: 'IMAGE',
    name: '比对图片',
    id: nanoid(),
  }));

  if (!simpleImages?.length) {
    return null;
  }

  return (
    <div className="flex flex-wrap gap-2 mb-5">
      {simpleImages?.map((item) => (
        <MediaCard
          file={item}
          isCompareImg
          key={item.id}
          newWindow={newWindow}
          fileList={simpleImages}
          width={50}
          height={50}
        />
      ))}
    </div>
  );
}
