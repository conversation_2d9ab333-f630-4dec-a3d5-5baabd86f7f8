import { useMemo } from 'react';
import { Button } from 'antd';
import classNames from 'classnames';
import { isBoolean, isNumber } from 'lodash';
import { checkAllowAlbum } from './PatrolJudgeItem';
import { SceneRectifyProps } from './SceneRectify';
import { StrategyRectificationMethod } from '@/constants/strategy';
import useConfig from '@/mobx/config';
import useSituationDescriptionModal from '@/pages/taskCenter/cloud/hooks/use-situation-description-modal';

export interface PatrolScoreItemProps {
  worksheetItem?: any;
  onInappropriate?: () => void;
  onDisqualified?: (values: any) => void;
  onQualified?: (values: any) => void;
  taskResult?: any;
  type?: string;
  taskId?: number;
  issueItemSnap?: any;
  copyUsers?: any[];
  onRectify?: SceneRectifyProps['onRectify'];
}

const PatrolScoreItem = ({
  worksheetItem,
  onInappropriate,
  onDisqualified,
  onQualified,
  taskResult,

  issueItemSnap,
  copyUsers,
}: PatrolScoreItemProps) => {
  const { showModal } = useSituationDescriptionModal();

  const { config } = useConfig();

  const { isDisqualified, isQualified, isInappropriate } = useMemo(() => {
    return {
      isDisqualified: isBoolean(taskResult?.itemJudge) && !taskResult?.itemJudge,
      isQualified: isBoolean(taskResult?.itemJudge) && taskResult?.itemJudge,
      isInappropriate: isBoolean(taskResult?.hasApply) && !taskResult?.hasApply,
    };
  }, [taskResult]);

  const {
    unqualifiedButtonName,
    normalButtonName,
    displayScore,
    scoreMax,
    unqualifiedMustUpload,
    normalMustUpload,
    unqualifiedReasons,
    unqualifiedMustSelectReason,
    uploadImageMethod,
  } = worksheetItem;

  const showDisqualified = () => {
    showModal({
      title: '不合格情况说明',
      style: {
        left: 10,
        margin: 0,
      },
      showConfig: {
        camera: true,
        remark: true,
        rectify: issueItemSnap?.reformEnable && issueItemSnap?.issueType !== StrategyRectificationMethod.AT_ONCE_ISSUE,
        reason: true,
        score: true,
      },
      scoreMax,
      mustConfig: {
        camera: unqualifiedMustUpload,
        remark: false,
        reason: unqualifiedMustSelectReason,
        disabledAlbum: !checkAllowAlbum(uploadImageMethod, config), // 是否允许使用相册
        issueType: issueItemSnap?.issueType === StrategyRectificationMethod.SELECT_ISSUE,
      },
      reasonList: unqualifiedReasons?.map((item: any) => ({
        label: item?.reason,
        value: item?.reason,
      })),
      copyUsers,
      initialValues: {
        ...taskResult,
        reformLimit: taskResult?.reformLimit || config?.IN_STORE_INSPECTION_NOT_QUALIFIED_ISSUE_ITEM_DEFAULT_DEADLINE,
        itemReason: taskResult?.itemOtherReason ? [...(taskResult?.itemReason || []), 'OTHER'] : taskResult?.itemReason,
        issueType: taskResult?.issueConfigDTO?.issueType,
        loopRectifyType: taskResult?.issueConfigDTO?.loopRectifyType,
        isLoopRectify: isBoolean(taskResult?.issueConfigDTO?.isLoopRectify)
          ? Number(taskResult?.issueConfigDTO?.isLoopRectify)
          : undefined,
        loopEndTime: taskResult?.issueConfigDTO?.loopEndTime,
        itemImages: taskResult?.itemImages?.map((item: any) => {
          return {
            type: item?.contentType,
            uid: item?.id,
            status: 'done',
            thumbUrl: item?.snapshotUrl,
            url: null,
            response: {
              contentType: item?.contentType,
              id: item?.id,
              key: item?.id,
              name: item?.name,
              snapshotUrl: item?.snapshotUrl,
              url: item?.url,
            },
          };
        }),
      },
      onOk: (values) => {
        onDisqualified({
          ...values,
          itemReason: values?.itemReason?.filter((reason: string) => reason !== 'OTHER'),
          itemOtherReason: values?.itemOtherReason,
          itemImages: values?.itemImages?.map((item: any) => ({ ...item?.response })),
          issueConfigDTO: {
            issueType: values?.issueType,
            loopRectifyType: values?.loopRectifyType,
            isLoopRectify: isNumber(values?.isLoopRectify) ? !!values?.isLoopRectify : undefined,
            loopEndTime: values?.loopEndTime,
          },
        });

        return Promise.resolve();
      },
    });
  };

  const showQualified = () => {
    showModal({
      title: '合格情况说明',
      showConfig: {
        camera: true,
        remark: true,
        rectify: false,
      },
      style: {
        left: 10,
        margin: 0,
      },
      mustConfig: {
        camera: normalMustUpload,
        remark: false,
        disabledAlbum: !checkAllowAlbum(uploadImageMethod, config), // 是否允许使用相册
      },
      initialValues: {
        ...taskResult,
      },
      onOk: (values) => {
        onQualified?.({
          ...values,
          itemScore: scoreMax,
          itemImages: values?.itemImages?.map((item: any) => ({ ...item?.response })),
        });

        return Promise.resolve();
      },
    });
  };

  return (
    <>
      <div>
        <Button
          className={classNames('mr-3  rounded border-[0.5px] border-solid border-[#DCDCDC]  px-4 py-2 h-8 text-sm', {
            'bg-[#f53f3f] border-[#f53f3f] text-white': isDisqualified,
          })}
          onClick={() => {
            showDisqualified();
          }}
        >
          {isBoolean(taskResult?.itemJudge) && !taskResult?.itemJudge && taskResult?.itemScore
            ? `${taskResult?.itemScore} 分`
            : '打分'}
        </Button>
        <Button
          className={classNames('mr-3  rounded border-[0.5px] border-solid border-[#DCDCDC]   px-4 py-2 h-8 text-sm', {
            'bg-[#378bff] border-[#378bff] text-white': isQualified,
          })}
          onClick={() => {
            if (normalMustUpload) {
              showQualified();
            } else {
              onQualified?.({ ...taskResult, itemScore: scoreMax });
            }
          }}
        >
          {taskResult?.displayScore ? `合格 (${taskResult?.scoreMax}分)` : '合格'}
        </Button>
        <Button
          className={classNames(
            'rounded border-[0.5px] border-solid  px-4 py-2 h-8',
            isInappropriate ? 'bg-[#378bff] border-[#378bff] text-white' : 'border-[#DCDCDC] bg-white ',
          )}
          onClick={onInappropriate}
        >
          不适用
        </Button>
      </div>
    </>
  );
};

export default PatrolScoreItem;
