import { PlusOutlined } from '@ant-design/icons';
import { checkAllowAlbum } from './PatrolJudgeItem';
import MediaCard from '@/components/MediaCard';
import useConfig from '@/mobx/config';
import useSituationDescriptionModal from '@/pages/taskCenter/cloud/hooks/use-situation-description-modal';

export type SceneRectifyProps = {
  taskId?: number;
  type?: string;
  issueAtOnceImages?: any[];
  disabled?: boolean;
  maxLength?: number;
  onRectify?: (data: any) => void;
  worksheetItem?: any;
  issueAtOnceRemark?: string;
  copyUsers?: any[];
  allowAlbum?: boolean; // 是否允许使用相册上传
  reformMustUpload?: boolean; // 是否必须上传图片
};

const SceneRectify = ({
  taskId,
  type,
  issueAtOnceImages,
  disabled,
  onRectify,
  issueAtOnceRemark,
  reformMustUpload,
  worksheetItem,
}: SceneRectifyProps) => {
  const { showModal } = useSituationDescriptionModal();

  const { config } = useConfig();
  const { uploadImageMethod } = worksheetItem;

  return (
    <div
      className="mt-3  overflow-hidden rounded bg-[#FAFAFA] px-2 py-3"
      onClick={() => {
        showModal({
          title: '当场整改情况说明',
          showConfig: {
            camera: true,
            remark: true,
          },
          mustConfig: {
            camera: reformMustUpload,
            remark: false,
            disabledAlbum: !checkAllowAlbum(uploadImageMethod, config), // 是否允许使用相册
          },
          initialValues: {
            itemImages: issueAtOnceImages?.map((item: any) => {
              return {
                type: item?.contentType,
                uid: item?.id,
                status: 'done',
                thumbUrl: item?.snapshotUrl,
                url: null,
                response: {
                  contentType: item?.contentType,
                  id: item?.id,
                  key: item?.id,
                  name: item?.name,
                  snapshotUrl: item?.snapshotUrl,
                  url: item?.url,
                },
              };
            }),
            itemRemark: issueAtOnceRemark,
          },
          onOk: (data) => {
            onRectify?.({
              issueAtOnceImages: data?.itemImages?.map(({ response }: any) => ({
                ...response,
              })),
              issueAtOnceRemark: data?.itemRemark,
            });

            return Promise.resolve();
          },
        });
      }}
    >
      <div className="mb-3.5  text-sm text-[#5B6A91]">{issueAtOnceRemark ? issueAtOnceRemark : '当场整改情况说明'}</div>
      <div className="flex flex-row gap-1">
        {issueAtOnceImages?.map((item) => (
          <MediaCard file={item} key={item.id} fileList={issueAtOnceImages} width={55} height={55} />
        ))}
        <div className="flex size-14 items-center justify-center rounded border-[0.5px] border-solid border-[#DCDCDC]">
          <PlusOutlined />
        </div>
      </div>
    </div>
  );
};

export default SceneRectify;
