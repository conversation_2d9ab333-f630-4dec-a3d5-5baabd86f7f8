import { FC } from 'react';
import { isBoolean } from 'lodash';
import PatrolJudgeItem from './PatrolJudgeItem';
import PatrolScoreItem from './PatrolScoreItem';
import SceneRectify from './SceneRectify';
import { StrategyRectificationMethod } from '@/constants/strategy';

export interface CheckItemProps {
  className?: string;
  data?: any;
  shopId?: string;
  onSave?: (data: any) => void;
  onUpdate?: (val: any) => void;
  disabled?: boolean;
  isValidity?: boolean;
  copyUsers: any[];
}

export enum CheckItemTypeEnum {
  SELF = 1,
  VALIDITY = 1 << 1,
  PATROL = 1 << 2,
}

export enum CheckItemActionTypeEnum {
  PHOTOGRAPH = 1 << 10,
  JUDGE = 1 << 11,
  GAP_FILLING = 1 << 12,
  SCORE = 1 << 13,
}

export const CheckItemProgressCheckFn = {
  [CheckItemTypeEnum.SELF | CheckItemActionTypeEnum.PHOTOGRAPH]: (data: any) => {
    const { data: taskResult } = data;

    if (taskResult?.itemImages?.length > 0) {
      return true;
    }

    return false;
  },
  [CheckItemTypeEnum.SELF | CheckItemActionTypeEnum.JUDGE]: (data: any) => {
    const { data: taskResult } = data;

    if (isBoolean(taskResult?.itemJudge)) {
      return true;
    }

    return false;
  },
  [CheckItemTypeEnum.SELF | CheckItemActionTypeEnum.GAP_FILLING]: (data: any) => {
    const { data: taskResult } = data;

    if (taskResult?.itemRemark) {
      return true;
    }

    return false;
  },
  [CheckItemTypeEnum.VALIDITY]: (data: any) => {
    if (data?.validityDetails?.length > 0) {
      return true;
    }

    return false;
  },
  [CheckItemTypeEnum.PATROL | CheckItemActionTypeEnum.JUDGE]: (data: any) => {
    const { data: taskResult } = data;

    if (isBoolean(taskResult?.itemJudge) || (isBoolean(taskResult?.hasApply) && !taskResult?.hasApply)) {
      return true;
    }

    return false;
  },
  [CheckItemTypeEnum.PATROL | CheckItemActionTypeEnum.SCORE]: (data: any) => {
    const { data: taskResult } = data;

    if (isBoolean(taskResult?.itemJudge) || (isBoolean(taskResult?.hasApply) && !taskResult?.hasApply)) {
      return true;
    }

    return false;
  },
};

const CheckItem: FC<CheckItemProps> = ({
  data,
  shopId,
  onSave,
  disabled = false,
  isValidity = false,
  onUpdate,
  copyUsers,
}) => {
  const {
    itemType,
    worksheetItem,
    baseTaskId,
    id,
    data: taskResult,
    worksheetItemContent,
    remark,
    validityDetails,
    issueItemSnap,
  } = data;

  const actionType = worksheetItem?.actionType || worksheetItem?.type;

  const operateType = (CheckItemTypeEnum[itemType] as any) | (CheckItemActionTypeEnum[actionType] as any);
  const handleSave = (taskData: any) => {
    onSave?.({ ...taskData, baseTaskId, id });
  };

  return (
    <>
      {/* 巡检判断 */}
      {operateType === (CheckItemTypeEnum.PATROL | CheckItemActionTypeEnum.JUDGE) && (
        <PatrolJudgeItem
          worksheetItem={worksheetItem}
          issueItemSnap={issueItemSnap}
          type={itemType}
          taskId={baseTaskId}
          onInappropriate={() => {
            handleSave?.({ hasApply: false, itemJudge: undefined, itemScore: undefined });
          }}
          copyUsers={copyUsers}
          onDisqualified={(values: any) => {
            handleSave?.({ ...values, hasApply: true, itemJudge: false });
          }}
          onQualified={(values: any) => {
            handleSave?.({
              ...values,
              hasApply: true,
              itemJudge: true,
              itemReason: undefined,
              itemOtherReason: undefined,
              issueConfigDTO: {
                issueType: undefined,
                loopRectifyType: undefined,
                isLoopRectify: undefined,
                loopEndTime: undefined,
              },
            });
          }}
          taskResult={taskResult}
        />
      )}
      {/* 巡检打分 */}
      {operateType === (CheckItemTypeEnum.PATROL | CheckItemActionTypeEnum.SCORE) && (
        <PatrolScoreItem
          taskId={baseTaskId}
          copyUsers={copyUsers}
          worksheetItem={worksheetItem}
          issueItemSnap={issueItemSnap}
          onInappropriate={() => {
            handleSave?.({ hasApply: false, itemJudge: undefined, itemScore: undefined });
          }}
          onDisqualified={(values: any) => {
            handleSave?.({ ...values, hasApply: true, itemJudge: false });
          }}
          onQualified={(values: any) => {
            handleSave?.({
              ...values,
              hasApply: true,
              itemJudge: true,
              itemReason: undefined,
              itemOtherReason: undefined,
              issueConfigDTO: {
                issueType: undefined,
                loopRectifyType: undefined,
                isLoopRectify: undefined,
                loopEndTime: undefined,
              },
            });
          }}
          taskResult={taskResult}
        />
      )}
      {taskResult?.itemJudge === false &&
        (StrategyRectificationMethod.AT_ONCE_ISSUE === issueItemSnap?.issueType ||
          (issueItemSnap?.issueType === StrategyRectificationMethod.SELECT_ISSUE &&
            taskResult?.issueConfigDTO?.issueType === 'SELECT_AT_ONCE_ISSUE')) && (
          <SceneRectify
            // taskId={taskId}
            // type={type}
            // allowAlbum={checkAllowAlbum(uploadImageMethod, config)}
            onRectify={(values: any) => {
              handleSave?.({ ...taskResult, ...values });
            }}
            worksheetItem={worksheetItem}
            issueAtOnceRemark={taskResult?.issueAtOnceRemark}
            issueAtOnceImages={taskResult?.issueAtOnceImages}
            reformMustUpload={issueItemSnap?.reformMustUpload}
          />
        )}
    </>
  );
};

export default CheckItem;
