import { useMemo, useRef } from 'react';
import {
  ProForm,
  ProFormDependency,
  ProFormDigit,
  ProFormItemProps,
  ProFormProps,
  ProFormRadio,
  ProFormSelect,
  ProFormText,
  ProFormTextArea,
} from '@ant-design/pro-components';
import { useRequest } from 'ahooks';
import { InputProps, Modal, ModalProps } from 'antd';
import { getUserInfoList } from '@/http/apis/center-control';
import { getSystemConfig } from '@/http/apis/system';
import { formatUserListToRoleOptions } from '@/utils/format';

const useDisqualificationModal = () => {
  const modalRef: any = useRef();
  const [form] = ProForm.useForm();
  const itemReason = ProForm.useWatch('itemReason', form);

  const { data: ccPersonOptions } = useRequest(
    async () => {
      const res = await getUserInfoList();

      if (!Array.isArray(res) || !res.length) {
        return [];
      }

      return formatUserListToRoleOptions(res);
    },
    {
      refreshDeps: [],
    },
  );

  const { data: systemConfig } = useRequest(async () => {
    const res = (await getSystemConfig()) as unknown as {
      id: number;
      key: string;
      value: string;
      type: string;
      createTime: string;
      updateTime: string;
    }[];

    return res;
  });

  // 自检不合格问题项的默认整改期限
  const selfCheckissueFixDaysConfig = useMemo(() => {
    return systemConfig?.find((f) => f?.key === 'ISSUE_FIX_DEFAULT_DAYS');
  }, [systemConfig]);

  const showModal = ({
    title,
    label,
    name,
    rules,
    maxLength,
    onOk,
    initialValues,
    placeholder,
    showScore,
    reviewItem,
    reviewResult,
    copyUserOptions,
    hasNeedCommitIssueInstructions,
    style,
    readOnlyReformLimit,
  }: {
    title: ModalProps['title'];
    maxLength?: InputProps['maxLength'];
    onOk?: (value: any) => Promise<any>;
    initialValues?: ProFormProps['initialValues'];
    placeholder?: string;
    showScore?: boolean;
    reviewItem: any;
    reviewResult: any;
    /** 抄送人列表选项 优先使用这个值 不传则使用 ccPersonOptions */
    copyUserOptions?: { label: string; value: number }[];
    /** 当前报告是否需要提交整改说明 */
    hasNeedCommitIssueInstructions?: boolean;
    style?: any;
    /** 整改期限是否为只读 */
    readOnlyReformLimit?: boolean;
  } & Pick<ProFormItemProps, 'label' | 'name' | 'rules'>) => {
    if (reviewResult) {
      if (reviewResult?.itemOtherReason) {
        reviewResult.itemReason?.push('OTHER');
      }

      form?.setFieldsValue({
        ...reviewResult,
        reformLimit: hasNeedCommitIssueInstructions
          ? (reviewResult?.reformLimit ?? selfCheckissueFixDaysConfig?.value)
          : undefined,
      });
    }

    const { abnormalReasons, abnormalMustSelectReason } = reviewItem || {};

    return (modalRef.current = Modal.confirm({
      title,
      style,
      closable: true,
      okText: '保存',
      cancelText: '取消',
      icon: null,
      content: (
        <ProForm submitter={false} form={form} layout="horizontal" className="mt-4" labelCol={{ span: 6 }}>
          {showScore && (
            <div className="flex ">
              <ProFormDigit
                label="分数"
                name="itemScore"
                width={'sm'}
                rules={[
                  {
                    required: true,
                    message: '请输入分数',
                  },
                ]}
              />
              <div className="leading-8 ml-2">分</div>
            </div>
          )}
          {!!abnormalReasons?.length && (
            <ProFormSelect
              mode="multiple"
              rules={[
                {
                  required: abnormalMustSelectReason,
                  message: '请选择不合格原因',
                },
              ]}
              options={abnormalReasons
                ?.map((v) => {
                  return {
                    label: v,
                    value: v,
                  };
                })
                .concat([
                  {
                    label: '其他',
                    value: 'OTHER',
                  },
                ])}
              label="不合格原因"
              name="itemReason"
            />
          )}

          <ProFormDependency name={['itemReason']}>
            {({ itemReason }) => {
              return (
                itemReason?.includes('OTHER') && (
                  <ProFormText
                    label="其他原因"
                    name="itemOtherReason"
                    placeholder="请输入其他原因"
                    rules={[
                      {
                        required: true,
                        message: '请输入其他原因',
                      },
                    ]}
                  />
                )
              );
            }}
          </ProFormDependency>

          {hasNeedCommitIssueInstructions && (
            <>
              <ProFormDigit
                label="整改期限"
                name="reformLimit"
                width={'sm'}
                fieldProps={{
                  suffix: '天',
                }}
                disabled={readOnlyReformLimit}
                rules={[
                  {
                    required: true,
                    message: '请输入整改期限',
                  },
                ]}
              />
              <ProFormRadio.Group
                label="处理人"
                options={[{ label: '门店所有员工', value: 1 }]}
                name="followName"
                fieldProps={{
                  defaultValue: 1,
                }}
              />
              <ProFormSelect
                label="抄送人"
                mode="multiple"
                name="copyUserIds"
                options={copyUserOptions ?? ccPersonOptions}
                showSearch
                fieldProps={{ maxTagCount: 2 }}
              />
            </>
          )}
          <ProFormTextArea label="备注" name="itemRemark" />
        </ProForm>
      ),
      onOk: async () => {
        return await form?.validateFields().then(() => {
          const values = form?.getFieldsValue();

          if (values?.itemReason?.includes('OTHER')) {
            values.itemReason = values?.itemReason?.filter((v) => v !== 'OTHER');
          }

          return onOk?.(values)?.then(() => {
            form?.resetFields();
          });
        });
      },
      onCancel: () => {
        form?.resetFields();
      },
    }));
  };

  return {
    showModal,
  };
};

export default useDisqualificationModal;
