import { FC, ReactNode, useEffect, useMemo, useRef, useState } from 'react';
import { useRequest } from 'ahooks';
import { Button, Checkbox, Input, message, Modal, Space, Tag } from 'antd';
import cn from 'classnames';
import dayjs from 'dayjs';
import { cloneDeep, isBoolean, isNil } from 'lodash';
import CheckItemInfo from './check-item-info';
import CheckItemResult from './check-item-result';
import Checklist from './checklist';
import Header from './header';
import OperaItemFilter from './opera-Item-filter';
import ReviewItem from './review-item';
import ReviewItemResult from './review-item-result';
import SumUp from './sum-up';
import SumUpResult from './sum-up-result';
import Summary from './summary';
import { filterReviewNotReviewed } from '../task-review/utils';
import { CheckItemAttribute } from '@/constants/checklist';
import {
  CheckItemActionTypeEnum,
  CheckItemProgressCheckFn,
  CheckItemTypeEnum,
  NotFilledItemHandleType,
  StrategyReviewType,
  StrategyRoutineType,
  StrategyTaskReportStatus,
  StrategyTaskStatus,
  StrategyTaskType,
} from '@/constants/strategy';
import { DiagnosisTypeCN } from '@/constants/task';
import { get } from '@/http';
import { StrategyTaskInfo } from '@/http/apis/report-center';
import { userStore } from '@/mobx';
import SopModal from '@/pages/report/components/patrolReportDetail/OptionsDetail/sopModal';
import UnqualifiedResonModal from '@/pages/report/components/patrolReportDetail/OptionsDetail/unqualifiedResonModal';

export interface StrategyTaskDetailProps {
  data?: {
    info: StrategyTaskInfo;
    worksheets: any;
    summarys: any[];
    statistics: any;
    examDetail?: any;
  };
  hasReview?: boolean;
  readonly?: boolean;
  isPreview?: boolean;
  saveReviewItem?: (dto: any) => Promise<any>;
  submitReview?: (data: any) => Promise<any>;
  detailType: 'PATROL' | 'SELF';
  previewReport?: (data: any) => void;
  reviewSumUp?: string;
  notFilledItemHandleType?: NotFilledItemHandleType;
  /** 巡检类型 */
  taskSubType?: string;
  /** 抄送人列表选项 */
  copyUserOptions?: { label: string; value: number }[];
  submitNextReview?: (data: any) => Promise<any>;
  onWorksheetChangeCallback?: (data: any) => void;
  readOnlyReformLimit?: boolean;
}

interface IDiagnosticInfo {
  id: number;
  cycleStart: string;
  cycleEnd: string;
  shopId: string;
  alarmLevel: string;
  createTime: {
    seconds: number;
    nanos: number;
  };
  updateTime: {
    seconds: number;
    nanos: number;
  };
  score: number;
  weightScore: number;
  taskId: number;
  taskSend: boolean;
  inspector: number;
  reportSubmitUserName: string;
}

/**
 * 诊断头部信息展示
 * @param param0
 * @returns
 */
function DiagnosticHeaderInfo({ info: { shopId, shopName, submitUserName, submitTime, taskId } }: { info: any }) {
  const { data } = useRequest(async () => {
    const res = await (get('/tm-api//common/diagnostic/info', {
      params: { taskId },
    }) as unknown as Promise<IDiagnosticInfo>);

    return res;
  });

  return (
    <>
      <div className="flex justify-between">
        <span className="text-lg">
          {shopId} {shopName}
        </span>
        <span className="text-[#848482]">{data?.alarmLevel ? DiagnosisTypeCN[data.alarmLevel] : '-'}</span>
      </div>
      <div className="bg-[#FAFAF8] mt-2 p-2 flex justify-between rounded-lg">
        <div className="flex flex-col gap-y-3">
          <div>
            <span className="text-[#9A9A98]">门店等级分：</span>
            {data?.weightScore ?? '-'}
          </div>
          <div>
            <span className="text-[#9A9A98]">报告提交时间：</span>
            {submitTime ? dayjs(submitTime).format('YYYY/MM/DD HH:mm') : '-'}
          </div>
        </div>
        <div className="flex flex-col gap-y-3">
          <div>
            <span className="text-[#9A9A98]">执行时段：</span>
            {!!data?.cycleStart && !!data?.cycleEnd
              ? `${dayjs(data.cycleStart).format('YYYY/MM/DD HH:mm')} ~ ${dayjs(data.cycleEnd).format('YYYY/MM/DD HH:mm')}`
              : '-'}
          </div>
          <div>
            <span className="text-[#9A9A98]">报告提交人：</span>
            {submitUserName ?? '-'}
          </div>
        </div>
      </div>
    </>
  );
}

export enum OperateStatusEnum {
  // DO = 'DO', // 做任务
  REVIEW = 'REVIEW', // 点评
  PREVIEW = 'PREVIEW', // 预览
  DETAIL = 'DETAIL', // 看详情
  CONFIRM = 'CONFIRM', // 门店端确认报告
}

const OperateStatusButton: Record<string, (props: any) => ReactNode> = {
  [OperateStatusEnum.REVIEW]: ({ onPreview, isOverdue }) => {
    return isOverdue ? (
      <Button disabled={true} type="primary" className="flex-1 m-3">
        未按时完成，点评已关闭
      </Button>
    ) : (
      <Button onClick={onPreview} className="flex-1 m-3" type="primary">
        预览报告
      </Button>
    );
  },
  [OperateStatusEnum.PREVIEW]: ({ onReview, onSubmitNextReview }) => {
    return (
      <Space>
        <Button onClick={onReview} className="flex-1 m-3" type="primary">
          提交点评
        </Button>
        {onSubmitNextReview && (
          <Button onClick={onSubmitNextReview} className="flex-1 m-3" type="primary">
            提交并继续审核下一条
          </Button>
        )}
      </Space>
    );
  },
};

const OperateStatusCheckItem: Record<string, (props: any) => ReactNode> = {
  // [OperateStatusEnum.DO]: ({ item, shopId, onCheckItemSave, checkItemDisabled }) => {
  //   return <CheckItem data={item} shopId={shopId} onSave={onCheckItemSave} disabled={checkItemDisabled} />;
  // },
  [OperateStatusEnum.REVIEW]: ({
    item,
    onReviewSave,
    taskStatus,
    copyUserOptions,
    hasNeedCommitIssueInstructions,
    taskSubType,
    readOnlyReformLimit,
    // index,
  }) => {
    return (
      <>
        <CheckItemResult data={item} taskSubType={taskSubType} />
        <ReviewItemResult
          data={
            // ai自动识别结果
            item?.reviewResult?.autoReviewResult && item?.reviewResult?.itemJudge
              ? { ...item, result: [item.reviewResult] }
              : item
          }
          taskStatus={taskStatus}
        />
        <ReviewItem
          data={item}
          copyUserOptions={copyUserOptions}
          hasNeedCommitIssueInstructions={hasNeedCommitIssueInstructions}
          onSave={onReviewSave}
          readOnlyReformLimit={readOnlyReformLimit}
        />
      </>
    );
  },
  [OperateStatusEnum.PREVIEW]: ({ item, taskStatus, taskSubType }) => {
    return (
      <>
        <CheckItemResult data={item} taskSubType={taskSubType} />
        <ReviewItemResult data={item} taskStatus={taskStatus} />
      </>
    );
  },
  [OperateStatusEnum.DETAIL]: ({ item, taskStatus, taskSubType }) => {
    return (
      <>
        <CheckItemResult data={item} taskSubType={taskSubType} />
        <ReviewItemResult data={item} taskStatus={taskStatus} />
      </>
    );
  },
  [OperateStatusEnum.CONFIRM]: ({ item, taskSubType }) => {
    return (
      <>
        <CheckItemResult data={item} taskSubType={taskSubType} />
      </>
    );
  },
};

// eslint-disable-next-line max-lines-per-function
const StrategyTaskDetail: FC<StrategyTaskDetailProps> = ({
  data,
  hasReview,
  readonly,
  isPreview,
  saveReviewItem,
  submitReview,
  detailType,
  previewReport,
  reviewSumUp,
  taskSubType,
  notFilledItemHandleType,
  copyUserOptions,
  submitNextReview,
  onWorksheetChangeCallback,
  readOnlyReformLimit,
}) => {
  const [worksheets, setWorksheets] = useState<any>([]);
  const [sumUpValue, setSumUpValue] = useState<string>();
  const [unqualified, setUnqualified] = useState<boolean>(false);
  const [onlyShowNotReviewed, setOnlyShowNotReviewed] = useState<boolean>(false);
  const [activeWorksheet, setActiveWorksheet] = useState<{
    id?: number;
    type?: StrategyTaskType;
    isOverdue?: boolean;
    selfStatus?: string;
  }>({});
  const previewFailedRef: any = useRef();
  const { info } = userStore;

  useEffect(() => {
    setWorksheets(
      data?.worksheets?.map((worksheet, worksheetIndex) => {
        return {
          ...worksheet,
          data: worksheet?.data?.map((category, worksheetCategoryIndex) => {
            return {
              ...category,
              data: category?.data?.map((item, worksheetItemIndex) => {
                const { itemType } = item;
                const worksheetItem = item?.worksheetItemSnap && JSON.parse(item.worksheetItemSnap);
                const actionType = item?.actionType || worksheetItem?.type;
                const typeEnum: number = +CheckItemTypeEnum[itemType as keyof typeof CheckItemTypeEnum];
                const actionEnum: number = +CheckItemActionTypeEnum[actionType];
                const operateType = typeEnum | actionEnum;

                let reviewResult = item?.reviewResult;
                let result: any = [];

                // reviewResult为前端数据，从后端数据中提取，如果已有前端数据则不在提取

                if (!item?.reviewResult) {
                  const reviewTaskIds: number[] = [];

                  result = item?.result?.filter((reviewItem) => {
                    const { taskType, taskId: reviewTaskId } = reviewItem;

                    // 点评预览的时候不过滤数据
                    if (isPreview && hasReview) {
                      return true;
                    }

                    if (taskType === 'REVIEW' && item?.reviewTaskId === reviewTaskId) {
                      reviewResult = { ...reviewItem };

                      return false;
                    }

                    reviewTaskIds.push(reviewTaskId);

                    return true;
                  });

                  if (!reviewTaskIds.includes(item?.reviewTaskId) && !reviewResult) {
                    reviewResult = {};
                  }
                }

                return {
                  ...item,
                  worksheetIndex,
                  worksheetCategoryIndex,
                  worksheetItemIndex,
                  worksheetItem,
                  filled: CheckItemProgressCheckFn?.[operateType]?.(item),
                  reviewResult,
                  result,
                  reviewed: reviewResult?.hasApply === false || (reviewResult && isBoolean(reviewResult?.itemJudge)),
                };
              }),
            };
          }),
        };
      }) || [],
    );
  }, [data?.worksheets]);

  // 综合后端数据类型，转换为前端的类型，对应界面和操作
  const operateStatus = useMemo(() => {
    if (isPreview) {
      return OperateStatusEnum.PREVIEW;
    }

    if (hasReview) {
      return OperateStatusEnum.REVIEW;
    }

    if (
      readonly ||
      [StrategyTaskStatus.COMPLETED, StrategyTaskStatus.EXPIRED].includes(
        data?.info?.taskStatus as StrategyTaskStatus,
      ) ||
      data?.info?.reportStatus === StrategyTaskReportStatus.WAITING_CONFIRM
    ) {
      return OperateStatusEnum.DETAIL;
    }
  }, [data, hasReview, isPreview, readonly]);

  const { NFNC, NFOC, NRNC, NROC } = useMemo<any>(() => {
    // 非必检项数量
    let optionalCount = 0;
    // 已填非必填项数量
    let filledOptionalCount = 0;
    // 未填非必填项数量
    let notFilledOptionalCount = 0;
    // 必检项数量
    let necessaryCount = 0;
    // 已填必填项数量
    let filledNecessaryCount = 0;
    // 未填必填项数量
    let notFilledNecessaryCount = 0;
    // 已点评非必检项数量
    let reviewedOptionalCount = 0;
    // 未点评非必检项数量
    let notReviewedOptionalCount = 0;
    // 已点评必检项数量
    let reviewedNecessaryCount = 0;
    // 未点评必检项数量
    let notReviewedNecessaryCount = 0;

    worksheets.forEach((sheetItem) =>
      sheetItem.data.forEach((item: any) =>
        item?.data.forEach((subItem: any) => {
          const { itemType, worksheetItem, reviewResult, result } = subItem;
          const actionType = worksheetItem?.actionType || worksheetItem?.type;
          const typeEnum: number = +CheckItemTypeEnum[itemType as keyof typeof CheckItemTypeEnum];
          const actionEnum: number = +CheckItemActionTypeEnum[actionType];
          // eslint-disable-next-line no-bitwise
          const operateType = typeEnum | actionEnum;
          const filled = CheckItemProgressCheckFn?.[operateType]?.({
            ...subItem,
            worksheetItem,
          });
          const reviewed = reviewResult?.hasApply === false || (reviewResult && isBoolean(reviewResult?.itemJudge));

          if (subItem?.accentedTermTags?.includes(CheckItemAttribute.NECESSARY)) {
            necessaryCount += 1;

            if (filled) {
              filledNecessaryCount += 1;
            } else {
              notFilledNecessaryCount += 1;
            }

            if (subItem?.reviewItemSnap?.type !== StrategyReviewType.NO_REVIEWS_NEEDED) {
              if (reviewed) {
                reviewedNecessaryCount += 1;
              } else {
                // ai识别结果为合格 result 数组第一条为一点结果（ai识别为一点结果）
                /* if (result?.[0]?.autoReviewed && result?.[0]?.itemJudge) {
                  return;
                } */

                notReviewedNecessaryCount += 1;
              }
            }
          } else {
            optionalCount += 1;

            if (filled) {
              filledOptionalCount += 1;
            } else {
              notFilledOptionalCount += 1;
            }

            if (subItem?.reviewItemSnap?.type !== StrategyReviewType.NO_REVIEWS_NEEDED) {
              if (reviewed) {
                reviewedOptionalCount += 1;
              } else {
                // ai识别结果为合格 result 数组第一条为一点结果（ai识别为一点结果）
                /* if (result?.[0]?.autoReviewed && result?.[0]?.itemJudge) {
                  return;
                } */

                notReviewedOptionalCount += 1;
              }
            }
          }
        }),
      ),
    );

    return {
      OC: optionalCount,
      FOC: filledOptionalCount,
      NFOC: notFilledOptionalCount,
      NC: necessaryCount,
      FNC: filledNecessaryCount,
      NFNC: notFilledNecessaryCount,
      ROC: reviewedOptionalCount,
      NROC: notReviewedOptionalCount,
      RNC: reviewedNecessaryCount,
      NRNC: notReviewedNecessaryCount,
    };
  }, [worksheets]);

  return (
    <div className="flex flex-col relative">
      {isBoolean(data?.info?.passed) && (
        <div
          className={cn(
            'w-full flex justify-center py-[9px] text-xs',
            data?.info?.passed ? 'bg-[#E8FFEA] text-[#23C343]' : 'bg-[#FFECE8] text-[#F76560]',
          )}
        >
          <span role="img" aria-label="celebration">
            {data?.info?.passed
              ? `🎉${{ SELF: '自检', PATROL: '巡检' }[data?.info?.taskType]}报告合格`
              : `${{ SELF: '自检', PATROL: '巡检' }[data?.info?.taskType]}报告不合格`}
          </span>
        </div>
      )}
      <div>
        {data?.info?.taskSubType === 'DIAGNOSTIC' ? (
          <div className="p-4">
            <DiagnosticHeaderInfo info={data?.info} />
          </div>
        ) : (
          <Header data={data?.info} />
        )}
        {[OperateStatusEnum.DETAIL, OperateStatusEnum.PREVIEW].includes(operateStatus as OperateStatusEnum) &&
          data?.statistics?.itemCount > 0 &&
          // 诊断巡检 不展示汇总数据
          !['DIAGNOSTIC', 'FOOD_SAFETY_ARRIVE_SHOP', 'DIFFERENCE_ITEM_ARRIVE_SHOP'].includes(
            data?.info?.taskSubType,
          ) && <Summary data={data?.statistics} />}
        {operateStatus === OperateStatusEnum.REVIEW && (
          <div className="flex justify-end pr-2">
            <Checkbox
              checked={onlyShowNotReviewed}
              onChange={(e) => {
                setOnlyShowNotReviewed(e.target.checked);
              }}
            >
              <div className="text-sm text-[#5E5E5E]">只看未点评项</div>
            </Checkbox>
          </div>
        )}
        <OperaItemFilter
          detailType={detailType}
          unqualified={unqualified}
          setUnqualified={(val) => {
            setUnqualified(val);
          }}
        />
        <Checklist
          data={worksheets}
          unqualified={unqualified}
          onlyShowNotReviewed={onlyShowNotReviewed}
          taskStatus={data?.info?.taskStatus as StrategyTaskStatus}
          operateStatus={operateStatus}
          // progressCheck={(itemData: any) => {
          //   const { worksheetItem, itemType } = itemData;
          //   const actionType = worksheetItem?.actionType || worksheetItem?.type;

          //   const typeEnum: number = +CheckItemTypeEnum[itemType];
          //   const actionEnum: number = +CheckItemActionTypeEnum[actionType];
          //   // eslint-disable-next-line no-bitwise
          //   const operateType = typeEnum | actionEnum;

          //   return CheckItemProgressCheckFn?.[operateType]?.(itemData);
          // }}
          onWorksheetChange={(changeData: any) => {
            setActiveWorksheet(changeData);
          }}
          onWorksheetChangeCallback={(id) => {
            onWorksheetChangeCallback?.(worksheets?.find((item: any) => item?.id === id));
          }}
          filter={(filterData: any, taskStatus: StrategyTaskStatus) => {
            let hiddenResult = false;

            if (unqualified && isBoolean(filterData?.data?.itemJudge)) {
              if (filterData?.data?.itemJudge) {
                // 合格项
                hiddenResult = true;
              } else {
                // 不合格项
                hiddenResult = false;

                if (operateStatus === OperateStatusEnum.REVIEW && onlyShowNotReviewed) {
                  // 只显示未点评项
                  hiddenResult = filterReviewNotReviewed(filterData, taskStatus);
                }
              }

              return hiddenResult;
            }

            if (operateStatus === OperateStatusEnum.REVIEW && onlyShowNotReviewed) {
              // 只显示未点评项
              return filterReviewNotReviewed(filterData, taskStatus);
            }

            return false;
          }}
          renderItem={(itemData: any, index) => {
            return itemData?.hidden ? null : (
              <>
                <CheckItemInfo data={itemData} />
                {OperateStatusCheckItem?.[operateStatus as OperateStatusEnum]?.({
                  item: itemData,
                  taskSubType: data?.info?.taskSubType,
                  taskStatus: data?.info?.taskStatus,
                  hasNeedCommitIssueInstructions: !!data?.info?.hasNeedCommitIssueInstructions,
                  copyUserOptions,
                  index,
                  onReviewSave: async (dto: any) => {
                    const { worksheetIndex, worksheetCategoryIndex, worksheetItemIndex } = itemData;
                    const worksheetsClone = cloneDeep(worksheets);

                    await saveReviewItem?.(dto);

                    worksheetsClone[worksheetIndex].data[worksheetCategoryIndex].data[worksheetItemIndex].reviewResult =
                      {
                        ...dto,
                      };

                    setWorksheets(worksheetsClone);
                  },
                  readOnlyReformLimit,
                })}
              </>
            );
          }}
        />
      </div>
      {/* 预览和详情显示巡检总结结果 */}
      {[
        OperateStatusEnum.PREVIEW,
        OperateStatusEnum.DETAIL,
        OperateStatusEnum.CONFIRM,
        OperateStatusEnum.REVIEW,
      ].includes(operateStatus as OperateStatusEnum) &&
        data?.info?.summary && <SumUpResult value={data?.info?.summary} label="巡检总结" />}
      {data?.summarys &&
        Object.keys(data?.summarys)?.length > 0 &&
        Object.keys(data?.summarys)?.map((key: string) => {
          const content: string = data?.summarys?.[key]
            ?.map(({ username, roleNames, summary }: any) => `${username}(${roleNames?.join('、')})：${summary}`)
            .join('\n');

          return <SumUpResult value={content} label={`点评总结${key}`} />;
        })}

      {operateStatus === OperateStatusEnum.REVIEW && (
        <SumUp
          value={sumUpValue}
          onChange={(e) => {
            setSumUpValue(e.target.value);
          }}
        />
      )}
      {/* 预览时携带的点评总结展示 */}
      {reviewSumUp && operateStatus === OperateStatusEnum.PREVIEW && (
        <SumUpResult
          value={`${info?.nickName}(${info?.roleBaseInfos?.map(({ roleName }: any) => roleName).join('、')})：${reviewSumUp}`}
          label="点评总结"
        />
      )}
      {/* 食安到店考试确认 */}
      {data?.info?.taskSubType === StrategyRoutineType.FOOD_SAFETY_ARRIVE_SHOP && !isNil(data?.examDetail) && (
        <div className="mt-4 px-3">
          <h4 className="text-base">考试情况确认</h4>
          <div className="my-4">
            <span className="text-[#f50]">*</span>
            <span className="mr-4">请确认门店是否都已完成并通过考试</span>
            {data?.examDetail?.confirm ? <Tag color="#87d068">已确认</Tag> : <Tag color="#f50">未确认</Tag>}
          </div>
          <div className="flex">
            <span className="inline-flex w-[80px] leading-8">备注说明：</span>
            <Input.TextArea
              style={{ width: '100%', marginTop: 5 }}
              disabled={true}
              autoComplete="off"
              value={data?.examDetail?.remark}
            />
          </div>
        </div>
      )}
      <div className="flex sticky bottom-0 left-0 right-0 bg-white">
        {OperateStatusButton?.[operateStatus as OperateStatusEnum]?.({
          onPreview: async () => {
            if (activeWorksheet.type !== 'VALIDITY' && NRNC > 0) {
              message.error(`还有${NRNC}项必填项未点评，请确认后再提交点评`);

              return;
            }

            if (NROC > 0) {
              previewFailedRef.current = Modal.info({
                title: '预览报告失败',
                content: '有检查项未填写，请选择处理方案',
                width: 600,
                closable: true,
                footer: (
                  <div className="flex justify-end mt-4">
                    <Button
                      type="primary"
                      className="mr-2"
                      onClick={() => {
                        previewReport?.({
                          reviewSumUp: sumUpValue,
                          isPreview: true,
                          hasReview,
                          taskId: data?.info?.taskId,
                          notFilledItemHandleType: NotFilledItemHandleType.SET_FULL_SCORE,
                          taskSubType: data?.info?.taskSubType,
                        });
                        previewFailedRef?.current?.destroy();
                      }}
                    >
                      设为"合格"(得满分)
                    </Button>
                    <Button
                      className="mr-2"
                      onClick={() => {
                        const worksheetsClone = cloneDeep(worksheets);

                        // 先将所有项设置成不适用
                        worksheetsClone?.forEach((sheetItem) => {
                          sheetItem.data.forEach((item: any) =>
                            item?.data.forEach((subItem: any) => {
                              if (hasReview) {
                                if (!subItem.reviewResult) {
                                  subItem.reviewResult = { hasApply: false };
                                } else {
                                  if (!isBoolean(subItem?.reviewResult?.itemJudge)) {
                                    subItem.reviewResult.hasApply = false;
                                  }
                                }
                              } else {
                                if (!subItem.data) {
                                  subItem.data = { hasApply: false };
                                } else {
                                  if (!isBoolean(subItem?.data?.itemJudge)) {
                                    subItem.data.hasApply = false;
                                  }
                                }
                              }
                            }),
                          );
                        });

                        // 再遍历所有项，只要有一张检查表全部是不适用，就提示“不能所有项都是不适用，请返回修改”,原系统是后端检验，现在是前端校验
                        const allIsNotApply: boolean = worksheetsClone?.some((sheetItem) => {
                          return sheetItem?.data?.every((item: any) => {
                            return item?.data?.every((subItem: any) => {
                              if (hasReview) {
                                return subItem?.reviewResult?.hasApply === false;
                              } else {
                                const { data: taskResult } = subItem;

                                return taskResult?.hasApply === false;
                              }
                            });
                          });
                        });

                        if (allIsNotApply) {
                          message.error('不能所有项都是“不适用”，请返回修改');
                        } else {
                          previewReport?.({
                            reviewSumUp: sumUpValue,
                            isPreview: true,
                            hasReview,
                            taskId: data?.info?.taskId,
                            notFilledItemHandleType: NotFilledItemHandleType.SET_NOT_APPLY,
                            taskSubType: data?.info?.taskSubType,
                          });
                        }

                        previewFailedRef?.current?.destroy();
                      }}
                    >
                      设为"不适用"(不计分)
                    </Button>
                    <Button
                      onClick={() => {
                        previewFailedRef?.current?.destroy();
                      }}
                    >
                      返回继续填写
                    </Button>
                  </div>
                ),
              });

              return;
            }

            previewReport?.({
              reviewSumUp: sumUpValue,
              isPreview: true,
              hasReview,
              taskId: data?.info?.taskId,
              taskSubType: data?.info?.taskSubType,
            });
          },
          onReview: async () => {
            return await submitReview?.({
              summary: reviewSumUp,
              notFilledItemHandleType,
            });
          },
          onSubmitNextReview: submitNextReview
            ? async () => {
                return await submitNextReview?.({
                  summary: reviewSumUp,
                  notFilledItemHandleType,
                });
              }
            : undefined,
          isOverdue: activeWorksheet?.isOverdue,
          readOnlyReformLimit,
        })}
      </div>
      <SopModal />
      <UnqualifiedResonModal />
    </div>
  );
};

export default StrategyTaskDetail;
