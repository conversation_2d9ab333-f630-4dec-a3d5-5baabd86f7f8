import { useMemo } from 'react';

export interface DiagnosticScoreProps {
  data?: any;
}

const DiagnosticScore = ({ data }: DiagnosticScoreProps) => {
  const { integerPart, decimalPart } = useMemo(() => {
    const score = data?.weightScore?.toFixed(2) || '0.00'; // 保留两位小数
    const [integer, decimal] = score.split('.'); // 分割整数和小数

    return {
      integerPart: integer,
      decimalPart: decimal,
    };
  }, [data]);

  return (
    <div className="flex  items-center justify-between">
      <div className="text-xs font-normal text-[#858585]">门店等级分</div>
      <div className="flex items-end">
        <div className="text-[24px] font-medium">{integerPart}</div>
        <div className="text-sm font-medium">.{decimalPart}</div>
      </div>
    </div>
  );
};

export default DiagnosticScore;
