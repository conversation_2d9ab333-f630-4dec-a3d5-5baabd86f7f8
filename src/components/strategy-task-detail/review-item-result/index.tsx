import classNames from 'classnames';
import { isBoolean } from 'lodash';
import { StrategyReviewType, StrategyTaskStatus, StrategyTaskType } from '@/constants/strategy';
import SopModal from '@/pages/report/components/patrolReportDetail/OptionsDetail/sopModal';
import UnqualifiedResonModal from '@/pages/report/components/patrolReportDetail/OptionsDetail/unqualifiedResonModal';

export interface ReviewItemResultProps {
  data?: any;
  taskStatus?: StrategyTaskStatus;
}

// eslint-disable-next-line react-refresh/only-export-components
export const reasonListText = (reasonLis: string[]) => {
  const text = reasonLis?.map((v, index) => {
    return <a key={index}>#{v}&nbsp;</a>;
  });

  return text || '';
};

const ReviewItemResult = ({ data, taskStatus }: ReviewItemResultProps) => {
  const { reviewItemSnap, result } = data;

  // 是否为ai识别
  /* const isAiIdentify = result?.some((s: { autoReviewed: boolean }) => s?.autoReviewed);

  const resultData = useMemo(() => {
    if (isAiIdentify) {
      // 一点为ai识别且为合格 则无需展示后续点评结果
      return result?.[0]?.autoReviewed && result?.[0]?.itemJudge ? [result?.[0] || {}] : [result?.[1] || {}];
    } else {
      return result;
    }
  }, [isAiIdentify, result]); */

  return (
    <>
      {/* 没有点评快照，则无需点评,效期检查表没有点评 */}
      {taskStatus !== StrategyTaskStatus.EXPIRED &&
        data?.itemType !== StrategyTaskType.VALIDITY &&
        reviewItemSnap?.type === StrategyReviewType.NO_REVIEWS_NEEDED && (
          <div className=" h-8 rounded border-[0.5px] border-solid border-[#DCDCDC] bg-[#DCDCDC] px-4 leading-8 text-center text-sm text-white">
            无需点评
          </div>
        )}
      {result?.map(
        ({ itemJudge, itemRemark, itemOtherReason, itemReason, itemScore, hasApply }: any, index: number) => {
          const allReasons = itemOtherReason ? [...(itemReason || []), itemOtherReason] : itemReason;

          if (!isBoolean(hasApply)) {
            return null;
          }

          return (
            <div className="mb-2.5">
              {/* 点评标识+序号 */}
              <span className="mb-2.5 px-2 py-1 text-sm font-medium">点评{index + 1}</span>
              {/* 点评说明+不合格原因 */}
              {(itemRemark || itemOtherReason || itemReason) && (
                <div className="mb-2.5 rounded-lg bg-[#FAFAFA] px-[9px] py-[7px] text-sm leading-6 text-[#858585]">
                  {itemRemark && <span className="text-sm">{itemRemark}</span>}
                  {(itemOtherReason || itemReason) && (
                    <div
                      className="break-all  ellipsis-2 "
                      onClick={() => {
                        UnqualifiedResonModal.showModal({
                          data: {
                            selectReasons: allReasons,
                          },
                        });
                      }}
                    >
                      {reasonListText(allReasons)}
                    </div>
                  )}
                </div>
              )}
              {reviewItemSnap?.standard ? (
                <div
                  className="mb-2.5 text-sm text-primary cursor-pointer"
                  onClick={() => {
                    SopModal.showModal(
                      {
                        data: {
                          standard: reviewItemSnap?.standard,
                        },
                      },
                      { title: '点评标准' },
                    );
                  }}
                >
                  点评标准
                </div>
              ) : null}
              {/* 点评结果 */}
              {/* 判断型 */}
              {reviewItemSnap?.type === StrategyReviewType.MANUAL_JUDGMENT &&
                (!hasApply ? (
                  <div
                    className={classNames(
                      'rounded flex border-[0.5px] border-solid  px-4 py-1 h-8 mb-2.5 justify-center items-center bg-[#0928BB] border-[#0928BB] text-xs leading-4 text-white',
                    )}
                  >
                    不适用
                  </div>
                ) : reviewItemSnap?.displayScore ? (
                  itemJudge ? ( // 显示分数-合格
                    <div
                      className={classNames(
                        'rounded border-[0.5px] border-solid  px-4 h-8 mb-2.5 justify-center  flex items-center',
                        isBoolean(itemJudge) && itemJudge
                          ? 'bg-[#0928BB] border-[#0928BB]'
                          : 'border-[#DCDCDC] bg-white',
                      )}
                    >
                      <span
                        className={classNames('text-xs leading-4', {
                          'text-white': isBoolean(itemJudge) && itemJudge,
                        })}
                      >{`${reviewItemSnap?.normalButtonName} (${itemScore})分`}</span>
                    </div>
                  ) : (
                    // 显示分数-不合格
                    <div
                      className={classNames(
                        'rounded border-[0.5px] border-solid   h-8 mb-2.5  justify-center items-center flex',
                        isBoolean(itemJudge) && !itemJudge
                          ? 'bg-[#F93D39] border-[#F93D39]'
                          : 'border-[#DCDCDC] bg-white',
                      )}
                    >
                      <span
                        className={classNames('text-xs leading-4', {
                          'text-white': isBoolean(itemJudge) && !itemJudge,
                        })}
                      >{`${reviewItemSnap?.abnormalButtonName} (0)分`}</span>
                    </div>
                  )
                ) : itemJudge ? ( // 不显示分数-合格
                  <div
                    className={classNames(
                      'rounded border-[0.5px] border-solid  px-4 flex h-8 mb-2.5  justify-center items-center',
                      isBoolean(itemJudge) && itemJudge ? 'bg-[#0928BB] border-[#0928BB]' : 'border-[#DCDCDC] bg-white',
                    )}
                  >
                    <span
                      className={classNames('text-xs leading-4', {
                        'text-white': isBoolean(itemJudge) && itemJudge,
                      })}
                    >{`${reviewItemSnap?.normalButtonName}`}</span>
                  </div>
                ) : (
                  // 不显示分数-不合格
                  <div
                    className={classNames(
                      'rounded border-[0.5px] border-solid   flex h-8 mb-2.5  justify-center items-center',
                      isBoolean(itemJudge) && !itemJudge
                        ? 'bg-[#F93D39] border-[#F93D39]'
                        : 'border-[#DCDCDC] bg-white',
                    )}
                  >
                    <span
                      className={classNames('text-xs leading-4', {
                        'text-white': isBoolean(itemJudge) && !itemJudge,
                      })}
                    >{`${reviewItemSnap?.abnormalButtonName}`}</span>
                  </div>
                ))}
              {reviewItemSnap?.type === StrategyReviewType.MANUAL_SCORING &&
                (!hasApply ? (
                  <div
                    className={classNames(
                      'rounded border-[0.5px] flex text-xs leading-4 text-white border-solid  text-center  px-4 py-1 h-8 mb-2.5 justify-center items-center bg-[#0928BB] border-[#0928BB]',
                    )}
                  >
                    不适用
                  </div>
                ) : itemJudge ? ( // 显示分数-合格
                  <div
                    className={classNames(
                      'flex  rounded border-[0.5px] border-solid   px-4 h-8 mb-2.5 justify-center items-center',
                      isBoolean(itemJudge) && itemJudge ? 'bg-[#0928BB] border-[#0928BB]' : 'border-[#DCDCDC] bg-white',
                    )}
                  >
                    <span
                      className={classNames('text-xs leading-4', {
                        'text-white': isBoolean(itemJudge) && itemJudge,
                      })}
                    >
                      合格
                    </span>
                  </div>
                ) : (
                  // 显示分数-不合格
                  <div
                    className={classNames(
                      'rounded border-[0.5px] border-solid   h-8 mb-2.5 flex  justify-center items-center',
                      isBoolean(itemJudge) && !itemJudge
                        ? 'bg-[#F93D39] border-[#F93D39]'
                        : 'border-[#DCDCDC] bg-white',
                    )}
                  >
                    <span
                      className={classNames('text-xs leading-4', {
                        'text-white': isBoolean(itemJudge) && !itemJudge,
                      })}
                    >{`${itemScore}分`}</span>
                  </div>
                ))}
              <div />
            </div>
          );
        },
      )}
    </>
  );
};

export default ReviewItemResult;
