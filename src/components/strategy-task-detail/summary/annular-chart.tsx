import { FC, useMemo } from 'react';
import IEcharts from '@/components/IEcharts';

export interface AnnularChartProps {
  color?: string;
  label: string;
  integer: string; // 整数
  decimals?: string; // 小数
  symbol?: string;
  progress: number;
}

const AnnularChart: FC<AnnularChartProps> = ({ color, label, integer, decimals, symbol, progress }) => {
  console.log('🚀 ~ integer:', integer);

  const option = useMemo(() => {
    return {
      color: [color, '#F5F5F5'],
      series: [
        {
          type: 'pie',
          radius: ['84%', '100%'],
          silent: true,
          itemStyle: {
            borderColor: '#fff',
            borderWidth: 2,
          },
          label: {
            show: false,
            position: 'center',
          },
          data: [{ value: progress }, 100 - progress > 0 ? { value: 100 - progress } : undefined],
        },
      ],
    };
  }, [color, progress]);

  return (
    <div className="relative w-[120px] h-[120px] mt-4">
      <IEcharts option={option} style={{ height: 120, width: 120 }} />
      <div className="absolute top-0 left-0 bottom-0 right-0">
        <h6 className="text-[#5E5E5E] text-xs leading-3 text-center mt-[40px]">{label}</h6>
        <div className="mt-1 text-center">
          <span className="text-[24px] font-medium leading-[24px]">{isNaN(integer as any) ? 0 : integer}</span>
          <span className="text-[14px] leading-[14px]">
            {decimals && `.${decimals}`}
            {symbol}
          </span>
        </div>
      </div>
    </div>
  );
};

export default AnnularChart;
