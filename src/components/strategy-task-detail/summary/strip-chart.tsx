import { FC } from 'react';
import { Progress } from 'antd';
import { ProgressProps } from 'antd/lib';

export interface StripChartProps {
  title?: string;
  leftPercent?: ProgressProps['percent'];
  rightPercent?: ProgressProps['percent'];
  leftLabel?: string;
  rightLabel?: string;
}

const StripChart: FC<StripChartProps> = ({ title, leftPercent = 0, rightPercent = 0, leftLabel, rightLabel }) => {
  return (
    <div className="mb-2">
      <div>
        <div className="flex">
          <Progress
            strokeLinecap="butt"
            percent={leftPercent}
            showInfo={false}
            trailColor="#F5F5F5"
            strokeColor="#378BFF"
          />
          <Progress
            strokeLinecap="butt"
            strokeColor="#FFAE4E"
            percent={rightPercent}
            showInfo={false}
            trailColor="#F5F5F5"
            style={{
              transform: 'rotateZ(180deg)',
              marginTop: 2,
            }}
          />
        </div>
        <div className="flex justify-between text-xs leading-3">
          <span className="inline-block w-[50px]">{leftLabel}</span>
          <span className="inline-block w-[130px] overflow-hidden text-ellipsis whitespace-nowrap text-center">
            {title}
          </span>
          <span className="inline-block w-[50px] text-right">{rightLabel}</span>
        </div>
      </div>
    </div>
  );
};

export default StripChart;
