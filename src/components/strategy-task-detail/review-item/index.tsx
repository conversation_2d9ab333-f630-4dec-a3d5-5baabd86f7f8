import { FC, ReactNode } from 'react';
import ReviewJudgeItem from './ReviewJudgeItem';
import ReviewScoreItem from './ReviewScoreItem';
import { StrategyReviewType } from '@/constants/strategy';

export interface ReviewItemProps {
  data?: any;
  /** 抄送人列表选项 */
  copyUserOptions?: { label: string; value: number }[];
  /** 是否需要提交整改说明 */
  hasNeedCommitIssueInstructions?: boolean;
  onSave?: (data: any) => void;
  /** 整改期限是否为只读 */
  readOnlyReformLimit?: boolean;
}

export const ReviewItemTypeNode: Record<string, (props: any) => ReactNode> = {
  [StrategyReviewType.MANUAL_JUDGMENT]: (props: any) => <ReviewJudgeItem {...props} />,
  [StrategyReviewType.MANUAL_SCORING]: (props: any) => <ReviewScoreItem {...props} />,
};

const ReviewItem: FC<ReviewItemProps> = ({
  data,
  copyUserOptions,
  hasNeedCommitIssueInstructions,
  onSave,
  readOnlyReformLimit,
}) => {
  const { reviewItemSnap, reviewTaskId, id, reviewResult } = data;

  const handleSave = (dto: any) => {
    onSave?.({ ...dto, taskId: reviewTaskId, taskItemId: id });
  };

  if (!reviewResult) {
    return null;
  }

  // ai自动识别结果无需展示操作按钮
  if (data?.reviewResult?.autoReviewResult && data?.reviewResult?.itemJudge) {
    return null;
  }

  return (
    <div className="mb-2.5">
      {reviewItemSnap ? (
        ReviewItemTypeNode?.[reviewItemSnap?.type as StrategyReviewType]?.({
          reviewItemSnap,
          reviewResult,
          copyUserOptions,
          hasNeedCommitIssueInstructions,
          onDisqualified: (dto: any) => {
            handleSave?.({ ...dto, hasApply: true, itemJudge: false });
          },
          onQualified: (dto: any) => {
            handleSave?.({ ...dto, hasApply: true, itemJudge: true });
          },
          onInappropriate: () => {
            handleSave?.({ hasApply: false, itemJudge: undefined, itemScore: undefined });
          },
          readOnlyReformLimit,
        })
      ) : (
        <div className=" h-8 rounded border-[0.5px] border-solid border-[#DCDCDC] bg-[#DCDCDC] px-4 py-2 ">
          <div className="text-sm text-white">无需点评</div>
        </div>
      )}
    </div>
  );
};

export default ReviewItem;
