import { useMemo } from 'react';
import { Button } from 'antd';
import classNames from 'classnames';
import { isBoolean } from 'lodash';
import useDisqualificationModal from '../use-disqualification-modal';
import SopModal from '@/pages/report/components/patrolReportDetail/OptionsDetail/sopModal';

export interface ReviewScoreItemProps {
  reviewItemSnap?: any;
  onInappropriate?: () => void;
  onDisqualified?: (values: any) => void;
  onQualified?: (values: any) => void;
  reviewResult?: any;
}

const ReviewScoreItem = ({
  reviewItemSnap,
  onInappropriate,
  onDisqualified,
  onQualified,
  reviewResult,
}: ReviewScoreItemProps) => {
  const { showModal } = useDisqualificationModal();

  const { isDisqualified, isQualified, isInappropriate } = useMemo(() => {
    return {
      isDisqualified: isBoolean(reviewResult?.itemJudge) && !reviewResult?.itemJudge,
      isQualified: isBoolean(reviewResult?.itemJudge) && reviewResult?.itemJudge,
      isInappropriate: isBoolean(reviewResult?.hasApply) && !reviewResult?.hasApply,
    };
  }, [reviewResult]);

  return (
    <>
      <div>
        <Button
          className={classNames('mr-3  rounded border-[0.5px] border-solid border-[#DCDCDC]  px-4 py-2 h-8 text-sm', {
            'bg-[#f53f3f] border-[#f53f3f] text-white': isDisqualified,
          })}
          onClick={() => {
            showModal({
              title: '情况说明',
              showScore: true,
              reviewItem: reviewItemSnap,
              reviewResult,
              onOk: (values) => {
                onDisqualified?.({ ...values });

                return Promise.resolve();
              },
            });
          }}
        >
          {isBoolean(reviewResult?.itemJudge) && !reviewResult?.itemJudge && reviewResult?.itemScore
            ? `${reviewResult?.itemScore} 分`
            : '打分'}
        </Button>
        <Button
          className={classNames('mr-3  rounded border-[0.5px] border-solid border-[#DCDCDC]   px-4 py-2 h-8 text-sm', {
            'bg-[#378bff] border-[#378bff] text-white': isQualified,
          })}
          onClick={() => {
            onQualified?.({ itemScore: reviewItemSnap?.scoreMax });
          }}
        >
          {reviewItemSnap?.displayScore ? `合格 (${reviewItemSnap?.scoreMax}分)` : '合格'}
        </Button>
        <Button
          className={classNames(
            'rounded border-[0.5px] border-solid  px-4 py-2 h-8',
            isInappropriate ? 'bg-[#378bff] border-[#378bff] text-white' : 'border-[#DCDCDC] bg-white ',
          )}
          onClick={onInappropriate}
        >
          不适用
        </Button>
      </div>
      {reviewItemSnap?.standard ? (
        <div
          className="mb-2.5 text-sm text-primary cursor-pointer"
          onClick={() => {
            SopModal.showModal(
              {
                data: {
                  standard: reviewItemSnap?.standard,
                },
              },
              { title: '点评标准' },
            );
          }}
        >
          点评标准
        </div>
      ) : null}
    </>
  );
};

export default ReviewScoreItem;
