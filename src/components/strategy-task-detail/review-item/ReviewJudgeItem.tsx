import { useMemo } from 'react';
import { Button } from 'antd';
import classNames from 'classnames';
import { isBoolean } from 'lodash';
import useDisqualificationModal from '../use-disqualification-modal';
import SopModal from '@/pages/report/components/patrolReportDetail/OptionsDetail/sopModal';

export interface ReviewJudgeItemProps {
  reviewItemSnap?: any;
  onInappropriate?: () => void;
  onDisqualified?: (values: any) => void;
  onQualified?: (values: any) => void;
  reviewResult?: any;
  /** 抄送人列表选项 */
  copyUserOptions?: { label: string; value: number }[];
  /** 当前报告是否需要提交整改说明 */
  hasNeedCommitIssueInstructions?: boolean;
  /** 整改期限是否为只读 */
  readOnlyReformLimit?: boolean;
}

const ReviewJudgeItem = ({
  onInappropriate,
  onDisqualified,
  onQualified,
  reviewItemSnap,
  reviewResult,
  copyUserOptions,
  hasNeedCommitIssueInstructions,
  readOnlyReformLimit,
}: ReviewJudgeItemProps) => {
  const { abnormalButtonName, displayScore, normalButtonName, scoreMax } = reviewItemSnap;

  const { showModal } = useDisqualificationModal();

  const { isDisqualified, isQualified, isInappropriate } = useMemo(() => {
    return {
      isDisqualified: isBoolean(reviewResult?.itemJudge) && !reviewResult?.itemJudge,
      isQualified: isBoolean(reviewResult?.itemJudge) && reviewResult?.itemJudge,
      isInappropriate: isBoolean(reviewResult?.hasApply) && !reviewResult?.hasApply,
    };
  }, [reviewResult]);

  return (
    <>
      <div>
        <Button
          className={classNames(
            'rounded border-[0.5px] border-solid  mr-3 px-4 py-2 h-8',
            isDisqualified ? 'bg-[#f53f3f] border-[#f53f3f] text-white' : 'border-[#DCDCDC] bg-white',
          )}
          onClick={() => {
            showModal({
              title: `${hasNeedCommitIssueInstructions ? '整改' : '情况'}说明`,
              reviewItem: reviewItemSnap,
              reviewResult,
              copyUserOptions,
              hasNeedCommitIssueInstructions,
              onOk: (values) => {
                onDisqualified?.({ itemScore: 0, ...values });

                return Promise.resolve();
              },
              readOnlyReformLimit,
            });
          }}
        >
          {displayScore ? `${abnormalButtonName} (0分)` : abnormalButtonName}
        </Button>
        <Button
          className={classNames(
            'rounded border-[0.5px] border-solid  mr-3 px-4 py-2 h-8',
            isQualified ? 'bg-[#378bff] border-[#378bff] text-white' : 'border-[#DCDCDC] bg-white',
          )}
          onClick={() => {
            onQualified?.({ itemScore: scoreMax });
          }}
        >
          {displayScore ? `${normalButtonName} (${scoreMax}分)` : normalButtonName}
        </Button>
        <Button
          className={classNames(
            'rounded border-[0.5px] border-solid  px-4 py-2 h-8',
            isInappropriate ? 'bg-[#378bff] border-[#378bff] text-white' : 'border-[#DCDCDC] bg-white ',
          )}
          onClick={onInappropriate}
        >
          不适用
        </Button>
      </div>
      {reviewItemSnap?.standard ? (
        <div
          className="mb-2.5 text-sm text-primary cursor-pointer"
          onClick={() => {
            SopModal.showModal(
              {
                data: {
                  standard: reviewItemSnap?.standard,
                },
              },
              { title: '点评标准' },
            );
          }}
        >
          点评标准
        </div>
      ) : null}
    </>
  );
};

export default ReviewJudgeItem;
