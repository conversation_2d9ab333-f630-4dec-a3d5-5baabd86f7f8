import { Tag } from 'antd';

type IProps = {
  data: any;
  value?: string[];
  onChange?: (value: string[]) => void;
  /** 不需要点击效果 */
  noClickEffect?: boolean;
};

const TagSelect = ({ data, value = [], noClickEffect, onChange }: IProps) => {
  const onSelect = (key: string) => {
    if (value?.includes(key)) {
      onChange && onChange(value.filter((v) => v !== key));
    } else {
      onChange && onChange([...(value as string[]), key]);
    }
  };

  return (
    <div>
      {data.map((tag) => (
        <Tag
          className="cursor-pointer"
          color={value?.includes(tag.ruleId) ? 'gold' : ''}
          key={tag.ruleId}
          onClick={() => onSelect(tag.ruleId)}
          {...(noClickEffect ? { className: undefined, onClick: undefined } : {})}
        >
          {tag.ruleName}
        </Tag>
      ))}
    </div>
  );
};

export default TagSelect;
