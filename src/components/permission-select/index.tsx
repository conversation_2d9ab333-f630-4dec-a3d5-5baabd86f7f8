import { FC, useMemo, useState } from 'react';
import { Select, SelectProps } from 'antd';
import RoleCascader from '../role-cascader';

interface PermissionSelectProps extends SelectProps {
  roleOptions: any;
  onChange?: (value: any) => void;
}

const PermissionSelect: FC<PermissionSelectProps> = ({ roleOptions, onChange, value: propValue }) => {
  const [permission, setPermission] = useState<any>();
  const [type, setType] = useState<any>();

  const value: any = useMemo(() => {
    if (propValue?.usePermissionScope) {
      if (propValue?.usePermissionScope !== 'ALL') {
        setPermission('PARTLY');
        setType(propValue?.usePermissionScope);
      } else {
        setPermission('ALL');
        setType(undefined);
      }
    } else {
      setPermission(undefined);
      setType(undefined);
    }

    return propValue;
  }, [propValue]);

  return (
    <div className="flex">
      <Select
        placeholder="请选择"
        className="pro-field-xs"
        value={permission}
        allowClear
        style={{ flexShrink: 0 }}
        options={[
          {
            label: '公开',
            value: 'ALL',
          },
          {
            label: '部分可见',
            value: 'PARTLY',
          },
        ]}
        onChange={(val) => {
          if (val === 'PARTLY') {
            onChange?.({
              permissionId: undefined,
              usePermissionScope: 'BY_ROLE',
            });
          } else {
            onChange?.({
              permissionId: undefined,
              usePermissionScope: val,
            });
          }
        }}
      />

      <Select
        className="pro-field-xs"
        value={type}
        placeholder="请选择"
        disabled={permission !== 'PARTLY'}
        style={{ flexShrink: 0 }}
        options={[
          {
            label: '角色',
            value: 'BY_ROLE',
          },
          // {
          //   label: '组织',
          //   value: 'BY_ORGANIZATION',
          // },
        ]}
        onChange={(val) => {
          onChange?.({
            permissionId: undefined,
            usePermissionScope: val,
          });
        }}
      />
      <RoleCascader
        options={roleOptions}
        showSearch
        allowClear
        style={{ flex: 1 }}
        disabled={permission !== 'PARTLY' || type !== 'BY_ROLE'}
        multiple={false}
        placeholder="请选择角色"
        className="pro-field-sm"
        value={value?.permissionId}
        onChange={(val) => {
          onChange?.({
            usePermissionScope: type,
            permissionId: val,
          });
        }}
      />
    </div>
  );
};

export default PermissionSelect;
