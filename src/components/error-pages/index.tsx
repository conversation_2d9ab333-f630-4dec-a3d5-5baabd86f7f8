import { Navigate, useRouteError } from 'react-router-dom';
import { removeToken } from '@/utils/authorization';
import { UnauthorizedException } from '@/http/errors/unauthorized';

export default function ErrorPage() {
  const error = useRouteError() as any;

  // 如果是未授权异常，重定向到登录页
  if (error instanceof UnauthorizedException) {
    removeToken();

    return <Navigate to="/auth/login" replace />;
  }

  return (
    <div id="error-page">
      <h1>Oops!</h1>
      <p>抱歉，有一个未处理的错误！</p>
      <p>
        <i>{error.statusText || error.message}</i>
      </p>
    </div>
  );
}
