/*
 * @Author: ZengWu <EMAIL>
 * @Date: 2023-02-21
 * @LastEditors: ZengWu <EMAIL>
 * @LastEditTime: 2023-02-22
 * @Description:
 */
/*
 * @Author: ZengWu <EMAIL>
 * @Date: 2023-02-07
 * @LastEditors: ZengWu <EMAIL>
 * @LastEditTime: 2023-02-22
 * @Description:
 */
import React, { useEffect, useState } from 'react';
import { DownloadOutlined } from '@ant-design/icons';
import { Badge } from 'antd';
import styles from './index.module.less';
import { windowOpen } from '@/utils/jump';

export let addDownloadTask = () => {};
export let clearDownloadCount = () => {};

const Download: React.FC = () => {
  const [count, setCount] = useState(0);

  useEffect(() => {
    clearDownloadCount = () => setCount(0);
    addDownloadTask = async () => {
      setCount((c) => c + 1);
    };

    return () => {
      clearDownloadCount = () => {};
      addDownloadTask = () => {};
    };
  }, []);

  return (
    <span
      className={styles.action}
      style={{ padding: 0 }}
      onClick={() => {
        setCount(0);
        windowOpen('/#/download/downloadCenter', '_blank');
      }}
    >
      <Badge count={count}>
        <DownloadOutlined id="download-icon" style={{ padding: '4px 12px' }} />
      </Badge>
    </span>
  );
};

export default Download;
