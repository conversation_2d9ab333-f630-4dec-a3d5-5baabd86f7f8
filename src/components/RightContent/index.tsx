/*
 * @Author: ZengWu <EMAIL>
 * @Date: 2022-11-23
 * @LastEditors: ZengWu <EMAIL>
 * @LastEditTime: 2023-02-22
 * @Description:
 */
import { /** Tag, */ Space } from 'antd';
import React from 'react';
import Avatar from './AvatarDropdown';
import styles from './index.module.less';
import eventEmitter from '@/utils/eventEmitter';
import { showExportAnimation } from '@/utils/yy_utils';
import Download from './Download';
import { userStore } from '@/mobx';

export type SiderTheme = 'light' | 'dark';

// const ENVTagColor = {
//   dev: 'orange',
//   test: 'green',
//   pre: '#87d068',
// };

export const increaseReportNum = (position: any) => {
  showExportAnimation(position);
  setTimeout(() => {
    eventEmitter.emit('change-report-num');
  }, 400);
};

const GlobalHeaderRight: React.FC<{}> = () => {
  const { info: initialState } = userStore;

  if (!initialState || !initialState.settings) {
    return null;
  }

  const { navTheme, layout } = initialState.settings;
  let className = styles.right;

  if ((navTheme === 'dark' && layout === 'top') || layout === 'mix') {
    className = `${styles.right}  ${styles.dark}`;
  }

  return (
    <Space className={className}>
      <Download />
      <Avatar />
      {/* {REACT_APP_ENV && (
        <span>
          <Tag color={ENVTagColor[REACT_APP_ENV]}>{REACT_APP_ENV}</Tag>
        </span>
      )} */}
    </Space>
  );
};
export default GlobalHeaderRight;
