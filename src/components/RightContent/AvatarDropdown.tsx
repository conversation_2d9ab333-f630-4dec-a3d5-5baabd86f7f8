import React, { useCallback } from 'react';
import { LogoutOutlined, EditOutlined } from '@ant-design/icons';
import { Avatar, Menu, Spin } from 'antd';
import { MenuInfo } from 'rc-menu/es/interface';
// import { logout } from '@/services/user';
import tokenUtils from '@/utils/tokenUtils';
import avatarUrl from '@/assets/avatar.png';
import HeaderDropdown from '../HeaderDropdown';
import styles from './index.module.less';
import { post } from '@/http';
import { useNavigate } from 'react-router-dom';
import resourceTree from '@/mobx/resourceTree';
import { userStore } from '@/mobx';

// 登出
export async function logout() {
  return post('user/logout', {
    noAlert: true
  });
}

const AvatarDropdown: React.FC<{}> = () => {
  const { info: initialState } = userStore;
  const { clear } = resourceTree();
  const navigate = useNavigate();

  const onMenuClick = useCallback(async (event: MenuInfo) => {
    const { key } = event;
    if (key === 'logout') {
      await logout().catch(() => {});
      navigate('/user/login', { replace: true });
      tokenUtils.clearToken();
      clear();
      return;
    }
    // @ts-ignore
    history.push(key);
  }, []);

  const loading = (
    <span className={`${styles.action} ${styles.account}`}>
      <Spin
        size="small"
        style={{
          marginLeft: 8,
          marginRight: 8
        }}
      />
    </span>
  );

  if (!initialState) {
    return loading;
  }

  const { userInfo } = initialState;

  if (!userInfo || !userInfo.userName) {
    return loading;
  }

  const menuHeaderDropdown = (
    <Menu className={styles.menu} onClick={onMenuClick}>
      <Menu.Item key="/user/password/change">
        <EditOutlined />
        修改密码
      </Menu.Item>
      <Menu.Item key="logout">
        <LogoutOutlined />
        退出登录
      </Menu.Item>
    </Menu>
  );

  const { /**avatar, */ name, userName } = userInfo;

  return (
    <HeaderDropdown overlay={menuHeaderDropdown}>
      <span className={`${styles.action} ${styles.account}`}>
        {/* <Avatar size="small" className={styles.avatar} src={avatar || avatarUrl} alt="avatar" /> */}
        <Avatar size="small" className={styles.avatar} src={avatarUrl} alt="avatar" />
        {(name || userName) && <span className={`${styles.name} anticon`}>{name || userName}</span>}
      </span>
    </HeaderDropdown>
  );
};

export default AvatarDropdown;
