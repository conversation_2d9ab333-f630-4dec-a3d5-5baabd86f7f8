import { FC } from 'react';
import {
  ModalForm,
  ModalFormProps,
  ProFormSelect,
  ProFormRadio,
} from '@ant-design/pro-components';
import { Form } from 'antd';
import RoleCascader from '../role-cascader';



interface BatchPermissionModalFormProps extends ModalFormProps {
  title: string;
  roles?:any[];
}

const BatchPermissionModalForm: FC<BatchPermissionModalFormProps> = ({ title,open,onOpenChange,onFinish,roles=[]}) => {
  const [form] = Form.useForm();
  const usePermissionScope: string = Form.useWatch('usePermissionScope', form);

  return (
    <ModalForm
      title={title}
      form={form}
      open={open}
      layout="horizontal"
      width={575}
      onOpenChange={(value: boolean) => {
        onOpenChange?.(value);
      }}
      onFinish={onFinish}
      modalProps={{
        destroyOnClose: true,
      }}
    >
      <ProFormRadio.Group
        name="usePermissionScope"
        label="使用权限设置"
        required
        initialValue="ALL"
        options={[
          {
            label: '公开（所有人员可使用、查看）',
            value: 'ALL',
          },
          {
            label: '部分可见（ 指定部分人员可使用、查看）',
            value: 'BY_ROLE',
          },
        ]}
      />
      {usePermissionScope !== 'ALL' && (
        <Form.Item required label={'指定人员'}>
          <div  className="flex">
            <ProFormSelect
              width="xs"
              name="usePermissionScope"
              allowClear={false}
              options={[
                {
                  label: '角色',
                  value: 'BY_ROLE',
                },
              ]}
            />
            <Form.Item
              name="usePermissionIds"
              style={{ marginBottom: 0 }}
              rules={[
                {
                  required: true,
                  message: `请选择`,
                },
              ]}
            >
              <RoleCascader
                style={{ width: 222 }}
                options={roles}
                showSearch
                allowClear
                multiple={true}
                placeholder="请选择"
              />
            </Form.Item>
          </div>
        </Form.Item>
      )}
    </ModalForm>
  );
};

export default BatchPermissionModalForm;
