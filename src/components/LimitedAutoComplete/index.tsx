import { FC } from 'react';
import { AutoComplete, AutoCompleteProps } from 'antd';

export interface LimitedAutoCompleteProps extends AutoCompleteProps {
  maxLength?: number;
}

const LimitedAutoComplete: FC<LimitedAutoCompleteProps> = (props) => {
  const handleChange = (val, obj) => {
    if (props?.maxLength && val?.length > props?.maxLength) {
      // 检查是否超出最大长度限制
      props?.onChange?.(val.slice(0, props?.maxLength), obj); // 截断字符串到最大长度以内
    } else {
      props?.onChange?.(val, obj);
    }
  };

  return (
    <div>
      <AutoComplete {...props} onChange={handleChange} />
      {props?.maxLength && (
        <div className="flex justify-end text-xs text-gray-400">
          {props?.value?.length || 0}/{props?.maxLength}
        </div>
      )}
    </div>
  ); // 将处理后的值传递给AutoComplete组件的其他props。确保其他props如onSearch等也能正常工作。你可能需要额外处理这些事件。例如：onSearch={(searchText) => handleChange(searchText)}。具体取决于你的需求和实现方式。这里只是一个示例，具体实现可能需要根据实际情况调整。通常，你需要确保用户在触发搜索或选择时也能正确处理输入值。例如，你可以在handleSearch方法中再次调用handleChange来同步搜索文本的长度限制。同时，确保用户在选择下拉选项时也能正确更新
};

export default LimitedAutoComplete;
