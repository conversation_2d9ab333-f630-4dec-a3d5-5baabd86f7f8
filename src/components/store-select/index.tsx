import { FC, useMemo } from 'react';
import { Select, SelectProps } from 'antd';
import classnames from 'classnames';
import OrganizationCascader, { OrganizationCascaderOptionType } from '../organization-cascader';

export interface StoreSelectProps {
  organizationOptions?: OrganizationCascaderOptionType[];
  storeOptions?: any[];
  onStoreFocus?: (value: any) => void;
  storeClassName?: SelectProps['className'];
  organizationClassName?: string | undefined;
  onChange?: (value: any) => void;
  value?: any;
  storeLoading?: boolean;
}

const StoreSelect: FC<StoreSelectProps> = ({
  organizationOptions,
  storeOptions,
  onStoreFocus,
  storeClassName,
  organizationClassName,
  onChange,
  value: propValue,
  storeLoading,
}) => {
  // const [organization, setOrganization] = useState();

  const value = useMemo(() => {
    // setOrganization(propValue?.organization);
    return propValue;
  }, [propValue]);

  return (
    <div className={classnames('tstd-composite-component', 'tstd-store-select')}>
      <OrganizationCascader
        changeOnSelect
        className={organizationClassName}
        options={organizationOptions}
        value={value?.groupId}
        style={{ width: '50%' }}
        placeholder="请选择组织"
        allowClear
        onChange={(val: any) => {
          onChange?.({
            groupId: val,
            shopIds: undefined,
          });
        }}
      />
      <Select
        options={storeOptions}
        className={storeClassName}
        loading={storeLoading}
        onFocus={() => onStoreFocus?.(value?.groupId)}
        value={value?.shopIds}
        placeholder="请选择门店"
        style={{ width: '50%' }}
        mode="multiple"
        allowClear
        optionFilterProp="label"
        maxTagCount="responsive"
        onChange={(val: any) => {
          onChange?.({
            ...value,
            shopIds: val,
          });
        }}
      />
    </div>
  );
};

export default StoreSelect;
