import { isEmpty, isNil } from 'lodash';
import { StrategyReviewType, StrategyTaskStatus, StrategyTaskType } from '@/constants/strategy';

export const filterReviewNotReviewed = (item: any, taskStatus: StrategyTaskStatus) => {
  // 过滤无需点评的项
  const isNoReviewNeeded =
    taskStatus !== StrategyTaskStatus.EXPIRED &&
    item?.itemType !== StrategyTaskType.VALIDITY &&
    item?.reviewItemSnap?.type === StrategyReviewType.NO_REVIEWS_NEEDED;

  if (isNoReviewNeeded) {
    return true;
  }

  const { reviewResult } = item;

  // ai识别合格项 无需点评
  if (reviewResult?.autoReviewed && reviewResult?.itemJudge) {
    return true;
  }

  // 兼容ai识别失败情况
  if (reviewResult?.autoReviewResult === false) {
    return false;
  }

  // 未点评项
  if (isEmpty(reviewResult)) {
    return false;
  }

  // 已点评，但是选了不适用
  if (!reviewResult.hasApply) {
    return true;
  }

  // 已提交申请但未评判,返回false
  if (isNil(reviewResult.itemJudge)) {
    return false;
  }

  return true;
};
