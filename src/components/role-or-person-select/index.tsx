import { Select } from 'antd';
import { FC } from 'react';
import RoleCascader from '../role-cascader';

enum Type {
  Role = 'Role',
  Name = 'Name'
}

const RoleOrPersonSelect: FC<any> = ({ value, onChange, personOptions, roleOptions }) => {
  return (
    <div className="flex">
      <Select
        className="max-w-[80px]"
        options={[
          { value: Type.Role, label: '角色' },
          { value: Type.Name, label: '姓名' }
        ]}
        
        value={value?.type || Type.Role}
        onChange={(val) => {
          onChange?.({ ...value, type: val });
        }}
      />
      {value?.type === Type.Name ? (
        <Select
          options={personOptions}
          placeholder='请选择'
          showSearch
          allowClear
          optionFilterProp='label'
          value={value?.personId}
          onChange={(val) => {
            onChange?.({
              ...value,
              personId: val,
              roleId: undefined
            });
          }}
        />
      ) : (
        <RoleCascader
          options={roleOptions}
          value={value?.roleId}
          style={{ width: 0 }}
          showSearch
          allowClear
          onChange={(val) => {
            onChange?.({
              ...value,
              personId: undefined,
              roleId: val
            });
          }}
        />
      )}
    </div>
  );
};

export default RoleOrPersonSelect;
