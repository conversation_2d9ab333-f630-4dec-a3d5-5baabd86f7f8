import { useState } from 'react';
import { Image } from 'antd';
import styles from './index.module.scss';

type TFile = {
  type: string;
  url: string;
};

type IProps = {
  file: TFile | TFile[];
  open: boolean;
  current?: number;
  onCancel: () => void;
};

const ViewMediaModal = ({ file, open, current: defaultCurrent, onCancel }: IProps) => {
  const [current, setCurrent] = useState<number>(defaultCurrent);

  const FileDom = ({ file }: { file?: TFile }) => {
    return !file ? null : file.type?.toLocaleLowerCase().includes('video') ? (
      <div className={styles.maskWrap} onClick={onCancel}>
        <div className={styles.sliderItem}>
          <video
            className={styles.video}
            controlsList="nodownload"
            disablePictureInPicture
            controls
            autoPlay
            loop
            src={file.url}
            onClick={(e) => e.stopPropagation()}
          />
        </div>
      </div>
    ) : (
      <Image
        width={200}
        style={{ display: 'none' }}
        src={file.url}
        preview={{
          visible: open,
          src: file.url,
          onVisibleChange: onCancel,
        }}
      />
    );
  };

  return open ? (
    <>
      {Array.isArray(file) ? (
        <>
          <Image.PreviewGroup
            preview={{
              visible: open,
              onVisibleChange: onCancel,
              current,
              onChange: (current: number) => setCurrent(current),
            }}
          >
            {(file || []).map(
              (e) =>
                (e.type?.toLocaleLowerCase().includes('image') || e.type?.toLocaleLowerCase().includes('img')) && (
                  <FileDom file={e} key={e.url} />
                ),
            )}
          </Image.PreviewGroup>
        </>
      ) : (
        <FileDom file={file} />
      )}
    </>
  ) : null;
};

export default ViewMediaModal;
