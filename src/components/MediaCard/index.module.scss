.card {
  height: 100%;
  margin: 0;
  position: relative;
  // padding: 8px;
  // border: 1px solid #d9d9d9;
  border-radius: 4px;
  .media {
    height: 100%;
  }
  .mask {
    height: 100%;
    position: relative;
    top: -100%;
    transition: all 0.3s;
    cursor: pointer;
    .actions {
      color: rgba(255, 255, 255, 0.85);
      font-size: 16px;
      text-align: center;
      opacity: 0;
      position: relative;
      top: 50%;
      transform: translate(0, -50%);
      transition: all 0.3s;
      .icon {
        margin: 0 4px;
        // cursor: pointer;
        &:hover {
          color: white;
        }
      }
    }
    &:hover {
      .actions {
        opacity: 1;
      }
    }
  }
  &:hover {
    .mask {
      background-color: #00000080;
    }
  }
}

.maskWrap {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: rgba(40, 40, 40, 0.6);
  z-index: 1000;

  .sliderItem {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    height: 80%;
    width: auto;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1081;
    .video {
      max-width: 100%;
      max-height: 100%;
    }
  }
}
