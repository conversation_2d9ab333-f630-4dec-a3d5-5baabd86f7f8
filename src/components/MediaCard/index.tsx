import { useState } from 'react';
import { DeleteOutlined, DownloadOutlined, EyeOutlined } from '@ant-design/icons';
import styles from './index.module.scss';
import ViewMediaModal from './ViewMediaModal';
import useMediaView from '@/hooks/useMediaView';
import { windowOpen } from '@/utils/jump';

type TFile = {
  contentType: string;
  name: string;
  uid: string;
  url: string;
  id?: string;
  snapshotUrl?: string;
};

type IProps = {
  isSampleImg?: boolean;
  /** 是否为比对图片 */
  isCompareImg?: boolean;
  file: TFile;
  fileList?: TFile[];
  disabled?: boolean;
  newWindow?: boolean;
  width?: number | string;
  height?: number | string;
  onRemove?: () => void;
  onDownload?: () => void;
  drawerPlacement?: string;
};

const MediaCard = ({
  isSampleImg,
  isCompareImg,
  file,
  fileList,
  onRemove,
  onDownload,
  disabled,
  newWindow,
  drawerPlacement,
  width = '100%',
  height = '100%',
}: IProps) => {
  const [preview, setPreview] = useState(false);
  const { setInfo } = useMediaView();

  const params = {
    width: '100%',
    height: '100%',
  };

  const files =
    file?.contentType?.toLocaleLowerCase().includes('video') || !fileList
      ? { ...file, type: file.contentType }
      : fileList?.map((e) => ({ ...e, type: e.contentType })) || [];

  const current = fileList
    ?.filter(
      (e) => e.contentType?.toLocaleLowerCase().includes('image') || e.contentType?.toLocaleLowerCase().includes('img'),
    )
    ?.findIndex((e) => {
      const eId = e?.uid || e?.id;
      const fId = file?.uid || file?.id;

      return eId === fId;
    });

  return (
    <div
      className={styles.card}
      style={{ width, height }}
      onClick={(e) => {
        e.preventDefault();
        e.stopPropagation();
      }}
    >
      <div className={styles.media}>
        {file.contentType?.toLocaleLowerCase().includes('video') ? (
          <video
            {...params}
            src={file?.url}
            className="rounded border-[0.5px] border-solid border-gray-300 bg-[#D9D9D9] "
          />
        ) : (
          <div className="h-full relative">
            <img
              {...params}
              alt="pic"
              className="rounded border-[0.5px] border-solid border-gray-300 bg-[#D9D9D9] "
              style={{ objectFit: 'scale-down' }}
              src={file?.snapshotUrl || file?.url}
            />
            {(isSampleImg || isCompareImg) && (
              <span className="absolute bottom-0 left-0 text-xs rounded-lg bg-[#378BFF] text-white">
                {isCompareImg ? '比对' : '示例'}图片
              </span>
            )}
          </div>
        )}
      </div>
      <div
        className={styles.mask}
        onClick={() => {
          if (newWindow) {
            setInfo({
              current,
              files,
            });
            windowOpen(`/mediaView`);
          } else {
            setPreview(true);
          }
        }}
      >
        <div className={styles.actions}>
          {onDownload ? <DownloadOutlined className={styles.icon} onClick={onDownload} /> : null}
          <EyeOutlined
            className={styles.icon}
            /* onClick={() => {
              if (newWindow) {
                setInfo({
                  current,
                  files,
                });
                windowOpen(`/mediaView`);
              } else {
                setPreview(true);
              }
            }} */
          />
          {disabled || !onRemove ? null : (
            <DeleteOutlined
              className={styles.icon}
              onClick={(e) => {
                e.stopPropagation();
                onRemove();
              }}
            />
          )}
        </div>
      </div>
      {preview && <ViewMediaModal file={files} current={current} open={preview} onCancel={() => setPreview(false)} />}
    </div>
  );
};

export default MediaCard;
