import { memo, ReactNode, useCallback, useEffect, useState } from 'react';
import { message, UploadProps } from 'antd';
import { UploadFile } from 'antd/es/upload/interface';
import Dragger from 'antd/lib/upload/Dragger';
import styles from './index.module.scss';
import MediaCard from '../MediaCard';
import { useOSSClient } from '@/hooks/useOSSClient';
import tokenUtils from '@/utils/tokenUtils';

interface IUploadProps extends Pick<UploadProps, 'accept' | 'beforeUpload'> {
  maxCount?: number;
  children?: ReactNode;
  value?: UploadFile<any>[];
  onChange?(fileList: UploadFile<any>[]): void;
  onError?(e?: any): void;
  listType?: 'text' | 'picture' | 'picture-card';
  disabled?: boolean;
  data?: any;
  max?: number;
  extra?: any;
}

const IDragUpload = memo(
  ({ accept = '.xlsx,.csv', onChange, value, onError, children, max = 50, extra, ...restProps }: IUploadProps) => {
    const [fileList, setFileList] = useState<UploadFile<any>[]>([]);
    const { uploadFile } = useCallback(useOSSClient, [])('PATROL');

    useEffect(() => {
      setFileList(value!);
    }, [value]);

    const beforeUpload = (file: File) => {
      const { name } = file;
      const nameArr = name.split('.');
      const type = `.${nameArr[nameArr.length - 1].toLowerCase()}`;
      const acceptArr = accept.toLowerCase().split(',');

      if (!acceptArr?.includes(type)) {
        message.error(`${file.name}不是${accept}其中的类型`);

        return Promise.reject('文件上传失败');
      }

      if (file.size / 1024 / 1024 > max) {
        message.error(`${file.name}文件大小超过${max}MB`);

        return Promise.reject('文件上传失败');
      }
      // return Promise.resolve('上传成功!');
    };

    const customRequest = async (e: any) => {
      const { name } = e.file;
      const nameArr = name.split('.');
      const type = `.${nameArr[nameArr.length - 1].toLowerCase()}`;

      try {
        const response = await uploadFile(e.file, !['.mp4', '.pdf'].includes(type), null, extra);

        e.onSuccess(response);
      } catch (error) {
        e.onError(error);
      }
    };

    return (
      <div className={styles.drag}>
        <Dragger
          style={{ marginBottom: '8px' }}
          // action={actionUrl}
          accept={accept}
          headers={{ 'user-token': tokenUtils.getToken() }}
          {...restProps}
          beforeUpload={beforeUpload}
          customRequest={customRequest}
          isImageUrl={(file) => {
            if (file.fileName?.endsWith('mp4')) {
              return false;
            }

            return true;
          }}
          itemRender={(originNode, file: any, fileList: any, actions) =>
            file && file.contentType ? (
              <MediaCard file={file} fileList={fileList} onRemove={() => actions.remove()} newWindow={true} />
            ) : (
              originNode
            )
          }
          showUploadList={{
            showPreviewIcon: false,
          }}
          onChange={({ fileList, file }) => {
            let nextFileList = fileList || [];

            // if (file.status === 'error') {
            //   nextFileList = fileList.filter((f) => f !== file);
            //   onError && onError(file.response);
            // } else
            if (file.status === 'done') {
              const response = file.response as IResponse['result'];

              if (response.url) {
                const { id, url, name, contentType } = response;

                nextFileList = fileList.map((f) => (f === file ? { uid: id, name, url, contentType } : f));
              } else {
                nextFileList = fileList.filter((f) => f !== file);
                onError && onError(file.response);
              }
            }

            setFileList(nextFileList);

            if (file.status !== 'uploading') {
              onChange && onChange(nextFileList);
            }
          }}
          fileList={fileList}
        >
          {children}
        </Dragger>
      </div>
    );
  },
);

export default IDragUpload;
