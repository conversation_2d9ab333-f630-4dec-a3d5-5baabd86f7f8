import classnames from 'classnames';
import { FC } from 'react';
import ChecklistSelect, { ChecklistSelectProps } from '../checklist-select';
import { Select } from 'antd';

export interface ChecklistAndItemSelectProps extends ChecklistSelectProps {
  value?: any;
  onChange?: (value: any) => void;
  onChecklistChange?: (value: any) => void;
  checkItemOptions?: any[];
  checklistMultiple?: ChecklistSelectProps['multiple'];
}
const ChecklistAndItemSelect: FC<ChecklistAndItemSelectProps> = ({
  tagOptions,
  checklistOptions,
  checkItemOptions,
  onChange,
  onChecklistChange,
  onTagChange,
  value,
  checklistMultiple
}) => {
  return (
    <div className={classnames('flex')}>
      <ChecklistSelect
        tagOptions={tagOptions}
        checklistOptions={checklistOptions}
        onChecklistChange={onChecklistChange}
        value={value}
        onTagChange={onTagChange}
        multiple={checklistMultiple}
        onChange={(val: any) => {
          onChange?.({
            ...val,
            checkItem: undefined
          });
        }}
      />
      <Select
        style={{ maxWidth: 250 }}
        placeholder="请选择检查项分类"
        allowClear
        options={value?.checklist ? checkItemOptions : []}
        value={value?.checkItem}
        onChange={(val: any) => {
          onChange?.({
            ...value,
            checkItem: val
          });
        }}
      />
    </div>
  );
};

export default ChecklistAndItemSelect;
