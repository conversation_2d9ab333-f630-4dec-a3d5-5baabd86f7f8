import { DownloadOutlined } from '@ant-design/icons';
import { useRequest } from 'ahooks';
import { Button, ButtonProps, message, Popconfirm } from 'antd';
import { layoutStore } from '@/mobx';

interface DownloadButtonProps extends Omit<ButtonProps, 'onClick'> {
  downloadReq: () => Promise<any>;
}

export const DownloadButton = ({ children = '下载明细', downloadReq, ...props }: DownloadButtonProps) => {
  const { runAsync } = useRequest(() => downloadReq(), {
    manual: true,
    onSuccess: () => {
      message.success('导出成功');
      layoutStore.increaseExport();
    },
  });

  return (
    <Popconfirm
      title="是否导出下载明细？"
      description="导出明细后，可到“报表下载-我的导出”中查看进度"
      onConfirm={runAsync}
    >
      <Button type="primary" icon={<DownloadOutlined />} {...props}>
        {children}
      </Button>
    </Popconfirm>
  );
};
