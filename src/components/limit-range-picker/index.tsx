import React, { useState } from 'react';
import { DatePicker } from 'antd';
import { Dayjs } from 'dayjs';
import { DatePickerProps, RangePickerProps } from 'antd/es/date-picker';

const { RangePicker } = DatePicker;

type RangeValue = [Dayjs | null, Dayjs | null] | null;
type LimitRangePickerProps = RangePickerProps & {
  limit: number;
  showTime?: any;
};

const LimitRangePicker: React.FC<LimitRangePickerProps> = ({
  limit,
  style,
  onChange,
  allowClear,
  showTime,
  format,
  value
}) => {
  const [dates, setDates] = useState<RangeValue>(null);

  const disabledDate: DatePickerProps['disabledDate'] = (current: Dayjs, { from }) => {
    if (from) {
      return Math.abs(current.diff(from, 'days')) >= limit;
    }

    return false;
  };

  const onOpenChange = (open: boolean) => {
    if (open) {
      setDates([null, null]);
    } else {
      setDates(null);
    }
  };

  return (
    <RangePicker
      value={dates || value}
      style={style}
      allowClear={allowClear}
      disabledDate={disabledDate}
      onCalendarChange={(val) => {
        console.log(val);
        // setDates(val);
      }}
      onChange={(val, _) => {
        onChange?.(val, _);
      }}
      onOpenChange={onOpenChange}
      showTime={showTime}
      format={format}
    />
  );
};

export default LimitRangePicker;
