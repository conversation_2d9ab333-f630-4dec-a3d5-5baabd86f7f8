import React, { memo, useState, useEffect, useRef } from 'react';
import { Modal } from 'antd';
import { ModalProps } from 'antd/es/modal';
import { useUpdate } from 'ahooks';

export type InnerModalProps = Omit<ModalProps, 'visible' | 'onOk' | 'onCancel' | 'confirmLoading'>;

export interface PayloadType<D> {
  data?: D;
  onSuccess?: (...args: any[]) => void;
  onCancel?: (...args: any[]) => void;
}

type HookReturnProps = Pick<ModalProps, 'onOk' | 'okButtonProps' | 'onCancel' | 'confirmLoading' | 'destroyOnClose' | 'title'>;

export type InnerComponentType<P extends object = Record<string, never>, D = undefined> = React.FC<
  Omit<P, 'visible' | 'setVisible' | 'payload'> & {
    visible: boolean;
    setVisible: React.Dispatch<React.SetStateAction<boolean>>;
    payload: PayloadType<D>;
    useParentModalProps(props: HookReturnProps, update?: boolean): void;
  }
>;

function createModal<P extends object = Record<string, never>, D = undefined>(InnerComponent: InnerComponentType<P, D>, defaultModalProps?: InnerModalProps) {
  type ShowModal = (payload: PayloadType<D>, modalProps?: InnerModalProps) => void;

  let showModal: ShowModal = () => {};

  const CurrentModal = memo<P>((props) => {
    const [visible, setVisible] = useState(false);
    const forceUpdate = useUpdate();
    const payloadRef = useRef<PayloadType<D>>({});
    const propsDeliveredRef = useRef<InnerModalProps>();
    const hookReturnProps = useRef<HookReturnProps>({});

    useEffect(() => {
      const lastShowModal = showModal;

      showModal = (payload, modalProps) => {
        payloadRef.current = payload;
        propsDeliveredRef.current = modalProps;
        setVisible(true);
      };

      return () => {
        showModal = lastShowModal;
      };
    }, []);

    const useParentModalProps = (props: HookReturnProps, update?: boolean) => {
      if (update || hookReturnProps.current.confirmLoading !== props.confirmLoading) {
        setTimeout(forceUpdate);
      }
      hookReturnProps.current = props;
    };

    const { onCancel } = payloadRef.current;

    const modalProps: ModalProps = {
      open: visible,
      onOk: (e) => {
        const { onOk } = hookReturnProps.current;
        onOk && onOk(e);
      },
      onCancel: (e) => {
        const { onCancel: childCancel } = hookReturnProps.current;
        if (childCancel) return childCancel(e);
        setVisible(false);
        return onCancel && onCancel();
      },
      ...defaultModalProps,
      ...propsDeliveredRef.current,
      ...hookReturnProps.current,
    };

    return (
      <Modal {...modalProps}>
        <InnerComponent {...props} visible={visible} setVisible={setVisible} payload={payloadRef.current} useParentModalProps={useParentModalProps} />
      </Modal>
    );
  });

  Object.defineProperty(CurrentModal, 'show', {
    get() {
      return showModal;
    },
  });

  return CurrentModal as typeof CurrentModal & {
    show: ShowModal;
  };
}

export default createModal;
