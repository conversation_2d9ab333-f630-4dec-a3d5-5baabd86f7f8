import { FC, useMemo } from 'react';
import { Cascader, CascaderProps } from 'antd';
import { DefaultOptionType } from 'antd/es/cascader';
import useOrganizationCascader from './useOrganizationCascader';

export interface OrganizationCascaderOptionType extends DefaultOptionType {
  type?: string | null;
}

export type OrganizationCascaderProps = CascaderProps & {
  onChange?: (value: any) => void;
  invest?: boolean;
};

const OrganizationCascader: FC<OrganizationCascaderProps> = ({
  options,
  className,
  changeOnSelect,
  multiple,
  onChange,
  value: propValue,
  allowClear,
  placeholder,
  style,
  maxTagCount,
  disabled,
  expandTrigger,
  invest,
  getPopupContainer,
}) => {
  const { formatSelectedToValue, getValueFromMap, getChildrenValues } = useOrganizationCascader({
    options,
    multiple,
  });

  const value = useMemo(() => {
    const realValue: any = getValueFromMap(propValue);

    return realValue;
  }, [propValue, options]);

  return (
    <Cascader
      disabled={disabled}
      options={options}
      allowClear={allowClear}
      value={value}
      style={style}
      multiple={multiple}
      showSearch={true}
      maxTagCount={maxTagCount}
      className={className}
      getPopupContainer={getPopupContainer}
      expandTrigger={expandTrigger || 'hover'}
      placeholder={placeholder}
      changeOnSelect={changeOnSelect}
      onChange={(_: any, selectedOptions: any) => {
        const selectValue = formatSelectedToValue(selectedOptions);

        onChange?.(invest ? getChildrenValues(selectValue, options) : selectValue);
      }}
    />
  );
};

export default OrganizationCascader;
