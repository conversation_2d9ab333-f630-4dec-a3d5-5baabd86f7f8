import { useMemo } from 'react';
import { OrganizationCascaderOptionType } from '.';

const useOrganizationCascader = ({
  options = [],
  multiple,
}: {
  options?: OrganizationCascaderOptionType[];
  multiple?: boolean;
}) => {
  // 生成每一级与所有父节的映射
  const genParentsMap = (options: OrganizationCascaderOptionType[]) => {
    const map: any = {};
    const loop = (nodes: OrganizationCascaderOptionType[] = [], parentValues?: any[]) => {
      nodes.forEach(({ value, children }: OrganizationCascaderOptionType) => {
        const parentValuesProxy = parentValues || [];

        if (children && children.length > 0) {
          loop(children, parentValuesProxy.concat([value]));
        }

        map[value!] = parentValuesProxy?.concat([value]);
      });
    };

    loop(options);

    return map;
  };

  const parentIdsMap = useMemo(() => genParentsMap(options), [options, multiple]);

  const getValueFromMap = (value: any) => {
    if (multiple) {
      // 多选-二维数组
      if (Array.isArray(value)) {
        return value
          ?.map((num: number) => {
            return parentIdsMap[num];
          })
          .filter((item) => !!item);
      } else {
        return [];
      }
    } else {
      return parentIdsMap[value];
    }
  };

  const formatSelectedToValue = (selectedOptions: any) => {
    if (multiple) {
      // 多选-二维数组
      const result: any[] = [];
      const loop = (nodes: OrganizationCascaderOptionType[] = []) => {
        nodes.forEach(({ value }: OrganizationCascaderOptionType) => {
          result.push(value);
        });
      };

      if (Array.isArray(selectedOptions)) {
        selectedOptions.forEach((subOptions: OrganizationCascaderOptionType[]) => {
          loop([subOptions[subOptions.length - 1]]);
        });
      }

      return result;
    } else {
      // 单选-一维数组
      if (selectedOptions?.length > 0) {
        return selectedOptions[selectedOptions?.length - 1].value;
      }

      return;
    }
  };

  /** 查找所有子节点,包括选中的节点本身 */
  const getChildrenValues = (value: any, options: OrganizationCascaderOptionType[]): any[] => {
    const findChildren = (nodes: OrganizationCascaderOptionType[], targetValue: any): any[] => {
      let result: any[] = [];

      for (const node of nodes) {
        if (node.value === targetValue) {
          result.push(node.value);

          if (node.children && node.children.length > 0) {
            result = result.concat(findAllChildren(node.children));
          }

          break;
        }

        if (node.children && node.children.length > 0) {
          const subResult = findChildren(node.children, targetValue);

          if (subResult.length > 0) {
            result = result.concat(subResult);

            break;
          }
        }
      }

      return result;
    };

    const findAllChildren = (nodes: OrganizationCascaderOptionType[]): any[] => {
      let result: any[] = [];

      for (const node of nodes) {
        result.push(node.value);

        if (node.children && node.children.length > 0) {
          result = result.concat(findAllChildren(node.children));
        }
      }

      return result;
    };

    return findChildren(options, value);
  };

  return { getValueFromMap, formatSelectedToValue, getChildrenValues };
};

export default useOrganizationCascader;
