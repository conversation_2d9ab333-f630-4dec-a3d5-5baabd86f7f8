import { memo, useState, ReactNode } from 'react';
import { ButtonProps } from 'antd/es/button/button';
import { Button } from 'antd';
import { sleep } from '@/utils/yy_utils';
import { useUpdateEffect } from 'ahooks';

export interface DelayButtonProps extends ButtonProps {
  delay?: number; // 单位 s, 一定要大于 1
  delayRender(lastTime: number): ReactNode;
}

const DelayButton = memo<DelayButtonProps>(({ children, loading, delayRender, disabled, delay = 3, ...props }) => {
  const [disabledTime, setDisabledTime] = useState(0);

  useUpdateEffect(() => {
    if (loading || delay < 1) return;

    (async () => {
      for (let i = delay + 1; i--; ) {
        setDisabledTime(i);
        await sleep(1000);
      }
    })();
  }, [loading]);

  return (
    <Button {...props} loading={loading} disabled={disabled || !!disabledTime}>
      {disabledTime ? delayRender(disabledTime) : children}
    </Button>
  );
});

export default DelayButton;
