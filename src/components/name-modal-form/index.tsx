import {
  ModalForm,
  ModalFormProps,
  ProFormText,
} from "@ant-design/pro-components";
import Form, { RuleObject } from "antd/es/form";
import { StoreValue } from "antd/es/form/interface";
import { FC, useEffect } from "react";

interface NameModalFormProps extends ModalFormProps {
  open: boolean;
  onFinish: (values: any) => Promise<any>;
  initialValues?: any;
  validator?: (
    rule: RuleObject,
    value: StoreValue,
    callback: (error?: string) => void
  ) => Promise<void | any> | void;
  name: string;
  label?: string;
  placeholder?: string;
  maxLength?: number;
}

const NameModalForm: FC<NameModalFormProps> = ({
  initialValues = {},
  title,
  open,
  onOpenChange,
  onFinish,
  validator,
  name,
  label,
  placeholder = "请输入",
  maxLength,
}) => {
  const [form] = Form.useForm<{ name: string }>();

  useEffect(() => {
    form.setFieldsValue(initialValues);
  }, [initialValues]);

  return (
    <ModalForm
      title={title}
      form={form}
      width={570}
      modalProps={{
        destroyOnClose: true,
      }}
      layout="inline"
      open={open}
      onOpenChange={onOpenChange}
      onFinish={async (values: any) => {
        return onFinish?.(values).then(() => {
          return true;
        });
      }}
    >
      <ProFormText
        width={400}
        name={name}
        label={label}
        rules={[
          {
            required: true,
            message: placeholder,
          },
          {
            validator,
          },
        ]}
        placeholder={placeholder}
        fieldProps={{
          maxLength,
          showCount:maxLength&&maxLength>0? true:false,
        }}
      />
    </ModalForm>
  );
};

export default NameModalForm;
