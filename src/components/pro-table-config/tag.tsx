import { ProColumns, ProFormSelectProps } from '@ant-design/pro-components';
import { merge } from 'lodash';

export const getTagColumn = (
  props?: { options?: ProFormSelectProps['options']; mode?: ProFormSelectProps['mode'] },
  column?: ProColumns,
) => {
  return merge(
    {
      title: '标签',
      dataIndex: 'tag',
      hideInTable: true,
      colSize: 1,
      valueType: 'select',
      fieldProps: {
        options: props?.options,
        showSearch: true,
        placeholder: '请选择标签',
        mode: props?.mode,
        maxTagCount: 'responsive',
      },
    },
    column,
  );
};
