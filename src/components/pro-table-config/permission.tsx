import { merge, ProColumns, ProFormSelectProps } from '@ant-design/pro-components';
import PermissionSelect from '@/components/permission-select';

const getPermissionColumn = (props?: { options?: ProFormSelectProps['options'] }, column?: ProColumns) => {
  return merge(
    {
      title: '权限',
      dataIndex: 'permission',
      hideInTable: true,
      colSize: 2,
      renderFormItem: () => {
        return <PermissionSelect roleOptions={props.options} />;
      },
    },
    column,
  );
};

export default getPermissionColumn;
