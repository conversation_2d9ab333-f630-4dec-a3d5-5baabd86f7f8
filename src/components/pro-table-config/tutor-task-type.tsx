import type { ProColumns, ProFormSelectProps } from '@ant-design/pro-components';
import { merge } from 'lodash';
import { TutorTaskTypeCN } from '@/constants/task';

export const getTutorTaskTypeColumn = (props?: { options?: ProFormSelectProps['options'] }, column?: ProColumns) => {
  return merge(
    {
      title: '任务类型',
      dataIndex: 'taskType',
      hideInTable: true,
      colSize: 1,
      valueType: 'select',
      fieldProps: {
        options:
          props?.options || Object.keys(TutorTaskTypeCN).map((key) => ({ value: key, label: TutorTaskTypeCN[key] })),
      },
    },
    column,
  );
};
