import { merge, ProColumns, ProFormSelectProps } from '@ant-design/pro-components';

const getCyclicRectificationColumn = (props?: { valueEnum: ProFormSelectProps['valueEnum'] }, column?: ProColumns) => {
  return merge(
    {
      title: '是否循环整改',
      dataIndex: 'loopTaskFlag',
      hideInTable: true,
      colSize: 1,
      valueType: 'select',
      valueEnum: props?.valueEnum ?? { true: '是', false: '否' },
    },
    column,
  );
};

export default getCyclicRectificationColumn;
