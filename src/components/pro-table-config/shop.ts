import { ProColumns } from '@ant-design/pro-components';
import { merge } from 'lodash';

// 门店类型查询条件
const getShopColumn = (props?: any, column?: ProColumns) => {
  return merge(
    {
      title: '门店',
      dataIndex: 'shopIds',
      hideInTable: true,
      colSize: 1,
      valueType: 'select',
      fieldProps: {
        options: props?.options,
        showSearch: true,
        mode: props?.mode || 'multiple',
        maxTagCount: 'responsive',
      },
    },
    column,
  );
};

export default getShopColumn;
