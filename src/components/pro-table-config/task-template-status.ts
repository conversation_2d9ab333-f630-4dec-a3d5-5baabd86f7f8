import type { ProColumns, ProFormSelectProps } from '@ant-design/pro-components';
import { merge } from 'lodash';
import { TemplateStatusCN } from '@/constants/template';

export const getTaskTemplateStatusColumn = (
  props?: { options?: ProFormSelectProps['options'] },
  column?: ProColumns,
) => {
  return merge(
    {
      title: '状态',
      dataIndex: 'templateStatus',
      hideInTable: true,
      colSize: 1,
      valueType: 'select',
      fieldProps: {
        options:
          props?.options || Object.keys(TemplateStatusCN).map((key) => ({ value: key, label: TemplateStatusCN[key] })),
      },
    },
    column,
  );
};
