import type { ProColumns, ProFormSelectProps } from '@ant-design/pro-components';
import { merge } from 'lodash';

export const getTaskTemplateColumn = (props?: { options: ProFormSelectProps['options'] }, column?: ProColumns) => {
  return merge(
    {
      title: '基础任务模板',
      dataIndex: 'templateId',
      hideInTable: true,
      valueType: 'select',
      colSize: 1,

      fieldProps: {
        placeholder: '请选择任务模板',
        showSearch: true,
        options: props?.options,
      },
    },
    column,
  );
};
