import { merge, ProColumns, ProFormSelectProps } from '@ant-design/pro-components';

const getTypeRectificationColumn = (props?: { valueEnum: ProFormSelectProps['valueEnum'] }, column?: ProColumns) => {
  return merge(
    {
      title: '整改类型',
      dataIndex: 'issueType',
      hideInTable: true,
      colSize: 1,
      valueType: 'select',
      valueEnum: props?.valueEnum ?? { SHOP_ISSUE: '门店整改', AT_ONCE_ISSUE: '当场整改' },
    },
    column,
  );
};

export default getTypeRectificationColumn;
