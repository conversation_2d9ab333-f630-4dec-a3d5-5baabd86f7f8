import { ProColumns, ProFormSelectProps } from '@ant-design/pro-components';
import { merge } from 'lodash';

export const getStragetySubTypeColumn = (
  props?: {
    options: ProFormSelectProps['options'];
  },
  column?: ProColumns,
) => {
  return merge(
    {
      title: '任务类型',
      dataIndex: 'strategySubType',
      hideInTable: true,
      colSize: 1,
      valueType: 'select',
      valueEnum: {
        NORMAL: '到店巡检',
        FOOD_SAFETY_NORMAL: '食安线下稽核',
        VIDEO: '视频云巡检',
        FOOD_SAFETY_VIDEO: '食安线上稽核',
        DIAGNOSTIC: '诊断巡检',
        FOOD_SAFETY_ARRIVE_SHOP: '食安稽核到店辅导',
        DIFFERENCE_ITEM_ARRIVE_SHOP: '差异项到店任务',
      },
      fieldProps: {
        options: props?.options,
      },
    },
    column,
  );
};
