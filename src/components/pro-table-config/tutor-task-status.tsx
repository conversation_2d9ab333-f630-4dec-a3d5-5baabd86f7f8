import type { ProColumns, ProFormSelectProps } from '@ant-design/pro-components';
import { merge } from 'lodash';
import { TutorTaskStatusCN } from '@/constants/task';

export const getTutorTaskStatusColumn = (props?: { options?: ProFormSelectProps['options'] }, column?: ProColumns) => {
  return merge(
    {
      title: '任务状态',
      dataIndex: 'taskStatus',
      hideInTable: true,
      colSize: 1,
      valueType: 'select',
      fieldProps: {
        options:
          props?.options ||
          Object.keys(TutorTaskStatusCN).map((key) => ({ value: key, label: TutorTaskStatusCN[key] })),
      },
    },
    column,
  );
};
