/* eslint-disable react-refresh/only-export-components */
import React from 'react';
import { QuestionCircleOutlined } from '@ant-design/icons';
import { ProColumns, ProFormInstance, ProFormSelectProps } from '@ant-design/pro-components';
import { InputNumber, SelectProps, Tooltip } from 'antd';
import { merge } from 'lodash';
import { ISelectPersonWithFS } from './ISelectPersonWithFS';
import ChecklistAndItemSelect, { ChecklistAndItemSelectProps } from '../checklist-and-item-select';
import ChecklistSelect, { ChecklistSelectProps } from '../checklist-select';
import RoleCascader, { RoleCascaderProps } from '../role-cascader';
import StoreSelect, { StoreSelectProps } from '../store-select';
import { CheckItemAttributeCN } from '@/constants/checklist';
import { StoreTypeCN } from '@/constants/common';
import { DisinfectionStatusCN, DisinfectionTypeCN, PositiveTypeCN } from '@/constants/disinfection';
import {
  DiagnosisTypeCN,
  InspectionReviewStatusCN,
  InspectionType,
  InspectionTypeCN,
  RectificationStatusCN,
  ReportStatusCN,
  RoutineTypeCN,
  StudyStatusCN,
  TaskTypeCN,
} from '@/constants/task';

export * from './study-project';
export * from './study-situation';
export * from './study-status';

export const onReset = (form: ProFormInstance, initialValue?: any) => {
  const values = form.getFieldsValue();

  console.log('🚀 ~ onReset ~ values:', values);

  const resetMap = {};

  Object.keys(values).forEach((key) => {
    resetMap[key] = undefined;
  });
  form.setFieldsValue(Object.assign(resetMap, initialValue));
  form.submit();

  return resetMap;
};

export const getCommonConfig = (config?: any) => {
  return merge(
    {
      search: {
        labelWidth: 'auto', // 标签宽度自适应
        span: {
          xs: 24,
          sm: 24,
          md: 12,
          lg: 8,
          xl: 6,
          xxl: 4,
        },
        collapseRender: false,
        collapsed: false,
        optionRender: (_s, _f, dom) => {
          return dom.reverse();
        },
      },
      manualRequest: false, // 第一次request，手动触发
      options: false,
    },
    config,
  );
};

// 严格区分，查询列和表单列
export const getColumns = ({
  searchColumns,
  tableColumns,
}: {
  searchColumns?: ProColumns[];
  tableColumns?: ProColumns[];
}) => {
  let columns: ProColumns[] = [];

  if (searchColumns?.length > 0) {
    columns = columns.concat(
      searchColumns.filter(Boolean).map((item) => {
        return {
          ...item,
          hideInTable: true,
        };
      }),
    );
  }

  if (tableColumns?.length > 0) {
    columns = columns.concat(
      tableColumns.map((item) => {
        return {
          ...item,
          hideInSearch: true,
        };
      }),
    );
  }

  return columns;
};

// 门店查询条件
export const getStoreColumn = (
  {
    organizationOptions,
    storeOptions,
    onStoreFocus,
    storeLoading,
  }: Pick<StoreSelectProps, 'onStoreFocus' | 'organizationOptions' | 'storeOptions' | 'storeLoading'>,
  column?: ProColumns,
): ProColumns => {
  return merge(
    {
      title: '门店',
      dataIndex: 'store',
      hideInTable: true,
      colSize: 2,
      renderFormItem: () => {
        return (
          <StoreSelect
            organizationOptions={organizationOptions || []}
            storeOptions={storeOptions || []}
            onStoreFocus={onStoreFocus}
            storeLoading={storeLoading}
          />
        );
      },
    },
    column,
  );
};

// 门店类型查询条件
export const getStoreTypeColumn = (_?: any, column?: ProColumns) => {
  return merge(
    {
      title: '门店类型',
      dataIndex: 'storeType',
      hideInTable: true,
      colSize: 1,
      valueType: 'select',
      valueEnum: StoreTypeCN,
    },
    column,
  );
};

// 各种人员查询条件
export const getPersonColumn = (
  props?: {
    mode?: ProFormSelectProps['mode'];
    options: ProFormSelectProps['options'];
    onFocus?: SelectProps['onFocus'];
    loading?: SelectProps['loading'];
  },
  column?: ProColumns,
) => {
  return merge(
    {
      title: '创建人',
      dataIndex: 'person',
      hideInTable: true,
      colSize: 1,
      valueType: 'select',
      fieldProps: {
        mode: props?.mode,
        options: props?.options,
        showSearch: true,
        optionFilterProp: 'label',
        maxTagCount: 'responsive',
        onFocus: props?.onFocus,
        loading: props?.loading,
      },
    },
    column,
  );
};

export const getChecklistColumn = (
  {
    onTagChange,
    tagOptions,
    multiple,
    checklistOptions,
  }: Pick<ChecklistSelectProps, 'onTagChange' | 'multiple' | 'tagOptions' | 'checklistOptions'>,
  column?: ProColumns,
) => {
  return merge(
    {
      title: '检查表',
      dataIndex: 'sheet',
      hideInTable: true,
      colSize: 2,
      renderFormItem: () => {
        return (
          <ChecklistSelect
            onTagChange={onTagChange}
            tagOptions={tagOptions || []}
            checklistOptions={checklistOptions}
            multiple={multiple}
          />
        );
      },
    },
    column,
  );
};

export const getDateRangeColumn = (
  _?: any,
  column?: ProColumns,
  /** 是否仅显示日期 不显示 时间 */
  isOnlyDate?: boolean,
) => {
  return merge(
    {
      title: '报告提交时间',
      dataIndex: 'dateRange',
      hideInTable: true,
      colSize: 2,
      valueType: 'dateRange',
      ...(isOnlyDate
        ? { fieldProps: { allowClear: false } }
        : {
            fieldProps: {
              allowClear: false,
              showTime: { format: 'HH:mm' },
              format: 'YYYY-MM-DD HH:mm',
            },
          }),
    },
    column,
  );
};

// 检查项属性
export const getCheckItemAttributeColumn = (_?: any, column?: ProColumns) => {
  return merge(
    {
      title: '检查项属性',
      dataIndex: 'checkItemAttribute',
      hideInTable: true,
      colSize: 1,
      valueType: 'select',
      valueEnum: CheckItemAttributeCN,
    },
    column,
  );
};

// 整改状态
export const getRectifyStatusColumn = (props?: { valueEnum?: ProColumns['valueEnum'] }, column?: ProColumns) => {
  return merge(
    {
      title: '整改状态',
      dataIndex: 'rectifyStatus',
      colSize: 1,
      valueType: 'select',
      valueEnum: props?.valueEnum || RectificationStatusCN,
    },
    column,
  );
};

// 巡检类型
export const getRoutineTypeColumn = (props?: { valueEnum?: ProColumns['valueEnum'] }, column?: ProColumns) => {
  return merge(
    {
      title: '巡检类型',
      dataIndex: 'routineType',
      colSize: 1,
      valueType: 'select',
      valueEnum: props?.valueEnum || RoutineTypeCN,
    },
    column,
  );
};
// 任务
export const getTaskColumn = (
  {
    options,
    placeholder,
    loading,
    searchValue,
    onSearch,
    onFocus,
    onClear,
  }: Pick<
    ProFormSelectProps['fieldProps'],
    'loading' | 'placeholder' | 'options' | 'searchValue' | 'onSearch' | 'onFocus' | 'onClear'
  >,
  column?: ProColumns,
) => {
  return merge(
    {
      title: '任务',
      dataIndex: 'task',
      hideInTable: true,
      colSize: 1,
      fieldProps: {
        placeholder: placeholder || '请输入任务名称',
        options,
        loading,
        showSearch: true,
        searchValue,
        onSearch,
        onFocus,
        onClear,
        defaultActiveFirstOption: false,
        filterOption: false,
      },
      valueType: 'select',
    },
    column,
  );
};

// 角色
export const getRoleColumn = (props?: Pick<RoleCascaderProps, 'options' | 'multiple'>, column?: ProColumns) => {
  return merge(
    {
      title: '角色',
      dataIndex: 'role',
      hideInTable: true,
      colSize: 1,
      renderFormItem: () => {
        return <RoleCascader options={props?.options} multiple={props?.multiple} />;
      },
    },
    column,
  );
};

// 任务类型
export const getTaskTypeColumn = ({ valueEnum }: { valueEnum?: ProColumns['valueEnum'] }, column?: ProColumns) => {
  return merge(
    {
      title: '任务类型',
      dataIndex: 'taskType',
      hideInTable: true,
      colSize: 1,
      valueType: 'select',
      valueEnum: valueEnum || TaskTypeCN,
    },
    column,
  );
};

// 巡检类型
export const getInspectionTypeColumn = (
  props?: { options?: ProFormSelectProps['options']; mode?: ProFormSelectProps['mode'] },
  column?: ProColumns,
) => {
  return merge(
    {
      title: '巡检类型',
      dataIndex: 'inspectionType',
      hideInTable: true,
      colSize: 1,
      valueType: 'select',
      fieldProps: {
        options: props?.options || [
          {
            label: InspectionTypeCN[InspectionType.NORMAL],
            value: InspectionType.NORMAL,
          },
          {
            label: InspectionTypeCN[InspectionType.VIDEO],
            value: InspectionType.VIDEO,
          },
          {
            label: (
              <div className="flex items-center">
                {InspectionTypeCN[InspectionType.CROSS]}
                <Tooltip title="仅显示当前账号有权限的门店中由无门店权限的督导提交的巡检报告" placement="bottom">
                  <QuestionCircleOutlined className="ml-1" />
                </Tooltip>
              </div>
            ),
            value: InspectionType.CROSS,
          },
          {
            label: InspectionTypeCN[InspectionType.FOOD_SAFETY_NORMAL],
            value: InspectionType.FOOD_SAFETY_NORMAL,
          },
          {
            label: InspectionTypeCN[InspectionType.FOOD_SAFETY_VIDEO],
            value: InspectionType.FOOD_SAFETY_VIDEO,
          },
          {
            label: InspectionTypeCN[InspectionType.FOOD_SAFETY_ARRIVE_SHOP],
            value: InspectionType.FOOD_SAFETY_ARRIVE_SHOP,
          },
        ],
        mode: props?.mode,
        maxTagCount: 'responsive',
      },
    },
    column,
  );
};

// 检查表及分类
export const getChecklistAndCategoryColumn = (
  {
    tagOptions,
    onTagChange,
    checklistMultiple,
    checklistOptions,
    onChecklistChange,
    checkItemOptions,
  }: ChecklistAndItemSelectProps,
  column: ProColumns,
) => {
  return merge(
    {
      title: '检查表',
      dataIndex: 'checklistAndItem',
      hideInTable: true,
      colSize: 2.5,
      renderFormItem: () => {
        return (
          <ChecklistAndItemSelect
            tagOptions={tagOptions}
            onTagChange={onTagChange}
            checklistMultiple={checklistMultiple || false}
            checklistOptions={checklistOptions}
            onChecklistChange={onChecklistChange}
            checkItemOptions={checkItemOptions}
          />
        );
      },
    },
    column,
  );
};

export const getStudyStatusColumn = (props: { options?: ProFormSelectProps['options'] }, column?: ProColumns) => {
  return merge(
    {
      title: '学习情况',
      dataIndex: 'studyStatus',
      hideInTable: true,
      colSize: 1,
      valueType: 'select',
      fieldProps: {
        options: props?.options || Object.keys(StudyStatusCN).map((key) => ({ value: key, label: StudyStatusCN[key] })),
      },
    },
    column,
  );
};

// 消杀类型
export const getDisinfectionTypeColumn = (valueEnum?: ProColumns['valueEnum'], column?: ProColumns) => {
  return merge(
    {
      title: '消杀类型',
      dataIndex: 'disinfectionType',
      hideInTable: true,
      colSize: 1,
      valueEnum: valueEnum || DisinfectionTypeCN,
    },
    column,
  );
};

// 阳性类型
export const getPositiveTypeColumn = (valueEnum?: ProColumns['valueEnum'], column?: ProColumns) => {
  return merge(
    {
      title: '阳性类型',
      dataIndex: 'reportPositiveTags',
      hideInTable: true,
      colSize: 1.5,
      fieldProps: {
        mode: 'multiple',
      },
      valueEnum: valueEnum || PositiveTypeCN,
    },
    column,
  );
};

// 消杀任务状态
export const getDisinfectionStatusColumn = (props?: { valueEnum?: ProColumns['valueEnum'] }, column?: ProColumns) => {
  return merge(
    {
      title: '任务状态',
      dataIndex: 'taskStatus',
      hideInTable: true,
      colSize: 1,
      valueEnum: props?.valueEnum || DisinfectionStatusCN,
    },
    column,
  );
};

// 报告是否通过
export const getReportPassStatusColumn = (props?: { options?: ProFormSelectProps['options'] }, column?: ProColumns) => {
  return merge(
    {
      title: '报告是否通过',
      hideInTable: true,
      dataIndex: 'passed',
      valueType: 'select',
      colSize: 1,
      fieldProps: {
        options: props?.options || [
          {
            label: '通过',
            value: true,
          },
          {
            label: '未通过',
            value: false,
          },
        ],
      },
    },
    column,
  );
};

// 消杀公司
export const getDisinfectionCompanyColumn = (
  props?: { options?: ProFormSelectProps['options'] },
  column?: ProColumns,
) => {
  return merge(
    {
      title: '消杀公司',
      hideInTable: true,
      dataIndex: 'disinfectionCompanyId',
      valueType: 'select',
      colSize: 1,
      fieldProps: {
        options: props?.options,
        optinoFilterProp: 'label',
        showSearch: true,
      },
    },
    column,
  );
};

// 报告状态
export const getReportStatusColumn = (
  props?: {
    valueEnum?: ProColumns['valueEnum'];
  },
  column?: ProColumns,
) => {
  return merge(
    {
      title: '报告状态',
      dataIndex: 'reportStatus',
      hideInTable: true,
      colSize: 1,
      valueType: 'select',
      valueEnum: props?.valueEnum || ReportStatusCN,
    },
    column,
  );
};
// 诊断类型
export const getDiagnosisTypeColumn = (
  props?: {
    valueEnum?: ProColumns['valueEnum'];
  },
  column?: ProColumns,
) => {
  return merge(
    {
      title: '诊断类型',
      dataIndex: 'alarmLevel',
      hideInTable: true,
      colSize: 1,
      valueType: 'select',
      valueEnum: props?.valueEnum || DiagnosisTypeCN,
    },
    column,
  );
}; // 点评状态
export const getReviewStatusColumn = (
  props?: {
    valueEnum?: ProColumns['valueEnum'];
  },
  column?: ProColumns,
) => {
  return merge(
    {
      title: '点评状态',
      dataIndex: 'reviewStatus',
      hideInTable: true,
      colSize: 1,
      valueType: 'select',
      valueEnum: props?.valueEnum || InspectionReviewStatusCN,
    },
    column,
  );
};

// 报告是否通过
export const getReportPassedColumn = (_?: unknown, column?: ProColumns) => {
  return merge(
    {
      title: '报告是否通过',
      dataIndex: 'passed',
      hideInTable: true,
      colSize: 1,
      valueType: 'select',
      valueEnum: {
        true: '通过',
        false: '未通过',
      },
    },
    column,
  );
};
// 点评是否通过
export const getReviewPassedColumn = (_?: unknown, column?: ProColumns) => {
  return merge(
    {
      title: '点评是否通过',
      dataIndex: 'reportReviewPassed',
      hideInTable: true,
      colSize: 1,
      valueType: 'select',
      fieldProps: {
        options: [
          {
            label: '通过',
            value: true,
          },
          {
            label: '未通过',
            value: false,
          },
        ],
      },
    },
    column,
  );
};

// 数量范围
export const getRequireNumRangeColumn = (
  { title = '数量范围', min = 0, max = 10000, dataIndex = 'numRange' }: any,
  column?: ProColumns,
) => {
  return merge(
    {
      title,
      dataIndex,
      colSize: 1.5,
      renderFormItem: (_, __, form) => {
        const NumRangeInput = () => {
          const [minValue, setMinValue] = React.useState(0);
          const [maxValue, setMaxValue] = React.useState(undefined);

          React.useEffect(() => {
            const rangeValue = form.getFieldValue(dataIndex) || [];

            setMinValue(rangeValue[0] || 0);
            setMaxValue(rangeValue[1]);
          }, []);

          const handleMinChange = (value) => {
            setMinValue(+value);
          };

          const handleMaxChange = (value) => {
            setMaxValue(+value);
          };

          const handleBlur = () => {
            // 在失焦时更新表单字段，避免输入时失焦
            form.setFieldsValue({
              [dataIndex]: [minValue, maxValue],
            });

            const min = minValue;
            const max = maxValue;

            if ((min !== undefined && max !== undefined && +min > +max) || (min === undefined && max !== undefined)) {
              setMinValue(max);
              setMaxValue(min);
              // 交换后更新表单字段
              form.setFieldsValue({
                [dataIndex]: [max, min],
              });
            }
          };

          return (
            <div style={{ display: 'flex', alignItems: 'center' }}>
              <InputNumber
                placeholder="最小值"
                min={min}
                max={max}
                value={minValue}
                onChange={handleMinChange}
                onBlur={handleBlur}
              />
              <span className="px-2">-</span>
              <InputNumber
                placeholder="最大值"
                min={min}
                max={max}
                value={maxValue}
                onChange={handleMaxChange}
                onBlur={handleBlur}
              />
            </div>
          );
        };

        return <NumRangeInput />;
      },
    },
    column,
  );
};

// 人员查询带工号
export const getPersonWithStaffCodeColumn = (
  props?: {
    options?: ProFormSelectProps['options'];
  },
  column?: ProColumns,
) => {
  return merge(
    {
      title: '被鉴定人',
      dataIndex: 'hikUserId',
      hideInTable: true,
      colSize: 1.5,
      renderFormItem: () => {
        return <ISelectPersonWithFS />;
      },
    },
    column,
  );
};
