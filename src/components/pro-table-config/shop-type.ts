import { ProColumns } from '@ant-design/pro-components';
import { merge } from 'lodash';
import { StoreTypeCN } from '@/constants/common';

// 门店类型查询条件
const getShopTypeColumn = (props?: any, column?: ProColumns) => {
  return merge(
    {
      title: '门店类型',
      dataIndex: 'shopType',
      hideInTable: true,
      colSize: 1,
      valueType: 'select',
      valueEnum: props?.valueEnum || StoreTypeCN,
    },
    column,
  );
};

export default getShopTypeColumn;
