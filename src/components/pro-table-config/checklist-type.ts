import { ProColumns, ProFormSelectProps } from '@ant-design/pro-components';
import { merge } from 'lodash';

const getChecklistTypeColumn = (props?: { options?: ProFormSelectProps['options'] }, column?: ProColumns) => {
  return merge(
    {
      title: '检查表类型',
      dataIndex: 'checklistType',
      hideInTable: true,
      colSize: 1,
      valueType: 'select',
      fieldProps: {
        options: props?.options,
        placeholder: '请选择检查表类型',
      },
    },
    column,
  );
};

export default getChecklistTypeColumn;
