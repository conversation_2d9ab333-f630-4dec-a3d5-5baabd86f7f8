import type { ProColumns, ProFormSelectProps } from '@ant-design/pro-components';
import { merge } from 'lodash';
import { StudySituationCN } from '@/constants/task';

export const getStudySituationColumn = (props?: { options?: ProFormSelectProps['options'] }, column?: ProColumns) => {
  return merge(
    {
      title: '学习情况',
      dataIndex: 'studyStatus',
      hideInTable: true,
      colSize: 1,
      valueType: 'select',
      fieldProps: {
        options:
          props?.options || Object.keys(StudySituationCN).map((key) => ({ value: key, label: StudySituationCN[key] })),
      },
    },
    column,
  );
};
