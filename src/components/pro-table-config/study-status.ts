import type { ProColumns, ProFormSelectProps } from '@ant-design/pro-components';
import { merge } from 'lodash';
import { StudyStatusCN } from '@/constants/task';

export const getStudyStatusColumn = (props?: { options?: ProFormSelectProps['options'] }, column?: ProColumns) => {
  return merge(
    {
      title: '学习状态',
      dataIndex: 'studyStatus',
      hideInTable: true,
      colSize: 1,
      valueType: 'select',
      fieldProps: {
        options: props?.options || Object.keys(StudyStatusCN).map((key) => ({ value: key, label: StudyStatusCN[key] }))
      }
    },
    column
  );
};
