import { ProColumns } from '@ant-design/pro-components';
import { merge } from 'lodash';

export const getOrganizationTreeColumn = (props?: { options?: any }, column?: ProColumns) => {
  return merge(
    {
      title: '所在组织(战区)',
      dataIndex: 'groupId',
      hideInTable: true,
      colSize: 1.5,
      valueType: 'treeSelect',
      fieldProps: {
        options: props?.options,
      },
    },
    column,
  );
};
