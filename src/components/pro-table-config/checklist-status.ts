import { ProColumns, ProFormSelectProps } from '@ant-design/pro-components';
import { merge } from 'lodash';

const getChecklistStatusColumn = (props?: { options?: ProFormSelectProps['options'] }, column?: ProColumns) => {
  return merge(
    {
      title: '检查表状态',
      dataIndex: 'checklistStatus',
      hideInTable: true,
      colSize: 1,
      valueType: 'select',
      fieldProps: {
        options: props?.options,
        placeholder: '请选择检查表状态',
      },
    },
    column,
  );
};

export default getChecklistStatusColumn;
