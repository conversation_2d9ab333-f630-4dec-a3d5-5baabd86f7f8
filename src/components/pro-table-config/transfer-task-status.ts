import type { ProColumns, ProFormSelectProps } from '@ant-design/pro-components';
import { merge } from 'lodash';
import { TransferTaskStatusCN } from '@/constants/task';

export const getTransferTaskStatusColumn = (
  props?: { options?: ProFormSelectProps['options'] },
  column?: ProColumns,
) => {
  return merge(
    {
      title: '状态',
      dataIndex: 'taskStatus',
      hideInTable: true,
      colSize: 1,
      valueType: 'select',
      fieldProps: {
        options:
          props?.options ||
          Object.keys(TransferTaskStatusCN).map((key) => ({ value: key, label: TransferTaskStatusCN[key] })),
      },
    },
    column,
  );
};
