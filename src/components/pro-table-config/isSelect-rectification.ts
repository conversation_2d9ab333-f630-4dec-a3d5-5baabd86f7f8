import { merge, ProColumns, ProFormSelectProps } from '@ant-design/pro-components';

const getIsSelectRectificationColumn = (
  props?: { valueEnum: ProFormSelectProps['valueEnum'] },
  column?: ProColumns,
) => {
  return merge(
    {
      title: '是否巡检人人选择整改方式',
      dataIndex: 'selectIssueTypeFlag',
      hideInTable: true,
      colSize: 1,
      valueType: 'select',
      valueEnum: props?.valueEnum ?? { true: '是', false: '否' },
    },
    column,
  );
};

export default getIsSelectRectificationColumn;
