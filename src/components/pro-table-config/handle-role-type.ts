import { ProColumns, ProFormSelectProps } from '@ant-design/pro-components';
import { merge } from 'lodash';

const getHandleRoleTypeColumn = (props?: { options?: ProFormSelectProps['options'] }, column?: ProColumns) => {
  return merge(
    {
      title: '处理人角色类型',
      dataIndex: 'responsiblePersonType',
      hideInTable: true,
      colSize: 1,
      valueType: 'select',
      fieldProps: {
        options: props?.options || [
          {
            label: '公司内部',
            value: 1,
          },
          {
            label: '供应商',
            value: 2,
          },
        ],
        placeholder: '请选择处理人角色类型',
      },
    },
    column,
  );
};

export default getHandleRoleTypeColumn;
