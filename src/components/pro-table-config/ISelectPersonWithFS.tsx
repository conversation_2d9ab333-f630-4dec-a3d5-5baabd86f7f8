import { useMemo, useRef, useState } from 'react';
import { Select, Spin } from 'antd';
import debounce from 'lodash/debounce';
import { getUserStaffCodeInfo } from '@/http/apis/center-control';

/** 切记不能通过url回显(后端不支持) */
export const ISelectPersonWithFS = (props) => {
  const [fetching, setFetching] = useState(false);
  const [options, setOptions] = useState<any[]>([]);
  const fetchRef = useRef(0);
  const [type, setType] = useState('userName');

  const fetchOption = async (payload: Record<string, any>) => {
    const res = (await getUserStaffCodeInfo(payload)) as any;

    return res?.map(({ name, staffCode, id }: any) => ({
      label: `${name}(${staffCode})`,
      value: id,
    }));
  };

  const debounceFetcher = useMemo(() => {
    const loadOptions = (value: string) => {
      if (!value) {
        return;
      }

      fetchRef.current += 1;

      const fetchId = fetchRef.current;

      setOptions([]);
      setFetching(true);

      fetchOption({ [type]: value }).then((newOptions: any) => {
        if (fetchId !== fetchRef.current) {
          return;
        }

        setOptions(newOptions);
        setFetching(false);
      });
    };

    return debounce(loadOptions, 800);
  }, [type]);

  return (
    <div className=" flex">
      <Select
        options={[
          {
            label: '姓名',
            value: 'userName',
          },
          {
            label: '工号',
            value: 'userCode',
          },
        ]}
        defaultValue={'userName'}
        style={{
          width: 120,
        }}
        value={type}
        onChange={setType}
      />
      <Select
        filterOption={false}
        showSearch
        onSearch={debounceFetcher}
        notFoundContent={fetching ? <Spin size="small" /> : '未查询到结果'}
        {...props}
        options={options}
        placeholder="请输入姓名进行搜索"
        allowClear
      />
    </div>
  );
};
