import { ProColumns, ProFormSelectProps } from '@ant-design/pro-components';
import { merge } from 'lodash';

// 视频云巡检  风险类型
const getCloudRiskTypeColumn = (props?: { options?: ProFormSelectProps['options'] }, column?: ProColumns) => {
  return merge(
    {
      title: '风险类型',
      dataIndex: 'riskLevel',
      hideInTable: true,
      colSize: 1,
      valueType: 'select',
      fieldProps: {
        mode: 'multiple',
        options: props?.options,
        placeholder: '请选择风险类型',
      },
    },
    column,
  );
};

export default getCloudRiskTypeColumn;
