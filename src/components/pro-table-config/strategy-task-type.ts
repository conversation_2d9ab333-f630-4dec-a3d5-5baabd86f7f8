import { ProColumns, ProFormSelectProps } from '@ant-design/pro-components';
import { merge } from 'lodash';
import { TaskSubType } from '@/constants/strategy';

export const getStragetyTaskTypeColumn = (
  props?: {
    options: ProFormSelectProps['options'];
  },
  column?: ProColumns,
) => {
  return merge(
    {
      title: '基础任务类型',
      dataIndex: 'strategyTaskType',
      hideInTable: true,
      colSize: 1,
      valueType: 'select',
      valueEnum: {
        [TaskSubType.SELF]: '自检任务',
        [TaskSubType.PATROL]: '巡检任务',
      },
      fieldProps: {
        options: props?.options,
      },
    },
    column,
  );
};
