import type { ProColumns, ProFormSelectProps } from '@ant-design/pro-components';
import { merge } from 'lodash';

export const getStudyProjectColumn = (props?: { options?: ProFormSelectProps['options'] }, column?: ProColumns) => {
  return merge(
    {
      title: '学习项目',
      dataIndex: 'studyProjectId',
      hideInTable: true,
      colSize: 1,
      valueType: 'select',
      fieldProps: {
        options: props?.options,
        showSearch: true,
      },
    },
    column,
  );
};
