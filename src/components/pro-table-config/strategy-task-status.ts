import { ProColumns, ProFormSelectProps } from '@ant-design/pro-components';
import { merge } from 'lodash';
import { StrategyTaskStatusCN } from '@/constants/strategy';

// 视频云巡检  风险类型
const getStrategyTaskStatusColumn = (props?: { options?: ProFormSelectProps['options'] }, column?: ProColumns) => {
  return merge(
    {
      title: '任务状态',
      dataIndex: 'taskStatus',
      hideInTable: true,
      colSize: 1,
      valueType: 'select',
      fieldProps: {
        options:
          props?.options ||
          Object.keys(StrategyTaskStatusCN).map((key) => ({
            value: key,
            label: StrategyTaskStatusCN[key],
          })),
        placeholder: '请选择任务状态',
      },
    },
    column,
  );
};

export default getStrategyTaskStatusColumn;
