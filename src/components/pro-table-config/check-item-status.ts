import type { ProColumns, ProFormSelectProps } from '@ant-design/pro-components';
import { merge } from 'lodash';
import { ChecklistStatusCN } from '@/constants/checklist';

export const getCheckItemStatusColumn = (props?: { options?: ProFormSelectProps['options'] }, column?: ProColumns) => {
  return merge(
    {
      title: '检查项状态',
      dataIndex: 'checkItemStatus',
      hideInTable: true,
      colSize: 1,
      valueType: 'select',
      fieldProps: {
        options:
          props?.options ||
          Object.keys(ChecklistStatusCN).map((key) => ({ value: key, label: ChecklistStatusCN[key] })),
      },
    },
    column,
  );
};
