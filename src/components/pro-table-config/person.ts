import { ProColumns, ProFormSelectProps } from '@ant-design/pro-components';
import { merge } from 'lodash';

export const getPersonColumn = (
  props?: {
    mode?: ProFormSelectProps['mode'];
    options: ProFormSelectProps['options'];
  },
  column?: ProColumns,
) => {
  return merge(
    {
      title: '创建人',
      dataIndex: 'person',
      hideInTable: true,
      colSize: 1,
      valueType: 'select',
      fieldProps: {
        mode: props?.mode,
        options: props?.options,
        showSearch: true,
        optionFilterProp: 'label',
        maxTagCount: 'responsive',
      },
    },
    column,
  );
};
