import React, { ReactNode, useEffect, useState } from 'react';
import { Moment } from 'moment';
import { DatePicker, message, Space } from 'antd';
import { PickerProps } from 'antd/es/date-picker/generatePicker';

const { RangePicker } = DatePicker;

export type ValueEnums = 'DAY_1' | 'DAY_7' | 'DAY_30' | 'CUSTOM' | 'WEEK' | 'MONTH' | 'DATE';

export type DateRenderProps = {
  rangeLimit?: number;
  disabledDate?: PickerProps<Moment>['disabledDate'];
  defaultValue?: Moment[];
  endMoment: Moment;
  onChange: (dateInfo: Moment[]) => void;
};

type DATE_SELECT_OPTION = {
  value: ValueEnums;
  label: ReactNode | ((useYesterday: boolean) => ReactNode);
  DateRender: React.FC<DateRenderProps>;
};

export const DATE_SELECT_OPTIONS: DATE_SELECT_OPTION[] = [
  {
    value: 'DAY_1' as const,
    label: (useYesterday: boolean) => (useYesterday ? '昨天' : '前天'),
    DateRender: ({ endMoment, onChange }) => {
      const dateMoment = endMoment.subtract(1, 'days');
      useEffect(() => {
        onChange([dateMoment, dateMoment]);
      }, []);
      return <DatePicker disabled value={endMoment} />;
    }
  },
  {
    value: 'DAY_7' as const,
    label: '近 7 天',
    DateRender: ({ endMoment, onChange }) => {
      const rangeData = [endMoment.clone().subtract(6, 'days'), endMoment];
      useEffect(() => {
        onChange(rangeData);
      }, []);
      return <RangePicker disabled value={rangeData as any} />;
    }
  },
  {
    value: 'DAY_30' as const,
    label: '近 30 天',
    DateRender: ({ endMoment, onChange }) => {
      const rangeData = [endMoment.clone().subtract(29, 'days'), endMoment];
      useEffect(() => {
        onChange(rangeData);
      }, []);
      return <RangePicker disabled value={rangeData as any} />;
    }
  },
  {
    value: 'WEEK' as const,
    label: '自然周',
    DateRender: ({ defaultValue, disabledDate, endMoment, onChange }) => {
      const realEndMoment = endMoment.subtract(endMoment.get('day'), 'days');

      const [time, setTime] = useState(() => {
        if (defaultValue && defaultValue[0].get('day') === 1 && defaultValue[1].diff(defaultValue[0], 'day') === 6) {
          return defaultValue[0];
        }
        return realEndMoment.clone().startOf('week');
      });

      useEffect(() => {
        onChange([time.clone(), time.clone().endOf('week')]);
      }, [time]);

      return (
        <Space>
          <DatePicker
            disabledDate={disabledDate || ((m) => m.isAfter(realEndMoment))}
            value={time}
            onChange={(m) => setTime((m || time)!.startOf('week'))}
          />
          &nbsp;&nbsp;~&nbsp;&nbsp;
          <DatePicker disabled value={time.clone().endOf('week')} />
        </Space>
      );
    }
  },
  {
    value: 'MONTH' as const,
    label: '自然月',
    DateRender: ({ defaultValue, disabledDate, endMoment, onChange }) => {
      const realEndMoment = endMoment.clone().add(1, 'days').subtract(1, 'month').endOf('month');
      const [month, setMonth] = useState(() => {
        return defaultValue && defaultValue[0].isBefore(realEndMoment) ? defaultValue[0] : realEndMoment;
      });

      useEffect(() => {
        onChange([month.clone().startOf('month'), month.clone().endOf('month')]);
      }, [month]);
      console.log(month);

      return (
        <DatePicker
          picker="month"
          allowClear={false}
          // disabledDate={disabledDate || ((m) => m.isAfter(realEndMoment))}
          value={month}
          onChange={setMonth as any}
        />
      );
    }
  },
  {
    value: 'DATE' as const,
    label: '自然日',
    DateRender: ({ defaultValue, disabledDate, endMoment, onChange }) => {
      const [date, setDate] = useState(() => (defaultValue ? defaultValue[0] : endMoment));

      useEffect(() => {
        onChange([date.clone(), date.clone()]);
      }, [date]);

      return (
        <DatePicker
          allowClear={false}
          value={date}
          disabledDate={disabledDate || ((m) => m.isAfter(endMoment))}
          onChange={setDate as any}
        />
      );
    }
  },
  {
    value: 'CUSTOM' as const,
    label: '自定义',
    DateRender: ({ defaultValue, disabledDate, rangeLimit, endMoment, onChange }) => {
      return (
        <>
          <RangePicker
            allowClear={false}
            disabledDate={disabledDate || ((m) => m.isAfter(endMoment))}
            value={(defaultValue || [endMoment, endMoment]) as any}
            onChange={(values) => {
              if (values![1]!.diff(values![0]!, 'days') > rangeLimit!) {
                message.warning(`所选日期区间不能超过 ${rangeLimit! + 1} 天。`);
                return;
              }
              onChange(values as any);
            }}
          />
          {/* <InnerRangePicker
            value={(defaultValue || [endMoment, endMoment]) as any}
            onChange={(values) => {
              if (values![1]!.diff(values![0]!, 'days') > rangeLimit!) {
                message.warning(`所选日期区间不能超过 ${rangeLimit! + 1} 天。`);
                return;
              }
              onChange(values as any);
            }}
          /> */}
        </>
      );
    }
  }
];
