import React, { useImperativeHandle, useMemo, useRef, useState } from 'react';
import moment, { Dayjs } from 'dayjs';
import { Row, Select } from 'antd';
import { DateRenderProps, DATE_SELECT_OPTIONS, ValueEnums } from './enum';

type Value = [Dayjs[], string[]];

export type DateSelectFilterProps = Pick<DateRenderProps, 'rangeLimit' | 'disabledDate' | 'defaultValue'> & {
  picks?: ValueEnums[];
  useYesterday?: boolean | (() => boolean);
  defaultDateType?: ValueEnums;
  onChange(...args: Value): void;
};

export type DateSelectFilterRef = Omit<DateSelectFilterProps, 'onChange'> & {
  useYesterday: boolean;
};

const DateSelectFilter = React.forwardRef<DateSelectFilterRef, DateSelectFilterProps>(
  ({ useYesterday, picks, defaultDateType, onChange, defaultValue, ...restProps }, ref) => {
    const showOptions = useMemo(
      () => picks!.map((pick) => DATE_SELECT_OPTIONS.find((item) => item.value === pick)!),
      []
    );

    const showYesterday = typeof useYesterday === 'function' ? useYesterday() : !!useYesterday;

    const endMoment = moment().endOf('day');
    // .subtract(+!showYesterday + 1, 'days');

    const [dateType, setDateType] = useState(defaultDateType || showOptions[0].value);
    const defaultValueRef = useRef(defaultValue);

    const DateRender = showOptions.find((item) => item.value === dateType)!.DateRender;

    useImperativeHandle(ref, () => ({
      useYesterday: showYesterday,
      defaultDateType: dateType,
      defaultValue: defaultValueRef.current,
      ...restProps
    }));

    return (
      <Row align="middle">
        <Select defaultValue={dateType} style={{ width: 120, marginRight: 16 }} onChange={setDateType}>
          {showOptions.map(({ label, value }) => (
            <Select.Option key={value} value={value}>
              {typeof label === 'function' ? label(showYesterday) : label}
            </Select.Option>
          ))}
        </Select>
        <DateRender
          {...restProps}
          defaultValue={defaultValueRef.current}
          endMoment={endMoment}
          onChange={(values) => {
            if (
              defaultValueRef.current &&
              defaultValueRef.current.every((m, i) => m.format('YYYYMMDD') === values[i].format('YYYYMMDD'))
            )
              return;
            defaultValueRef.current = values;
            onChange(
              values,
              values.map((m) => m.format('YYYY-MM-DD'))
            );
          }}
        />
      </Row>
    );
  }
);

DateSelectFilter.defaultProps = {
  useYesterday: () => new Date().getHours() > 0,
  rangeLimit: 30,
  picks: ['DAY_1', 'DAY_7', 'DAY_30', 'CUSTOM']
};

export default DateSelectFilter;
