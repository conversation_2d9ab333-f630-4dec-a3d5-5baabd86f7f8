import ProForm from '@ant-design/pro-form';
import OrganizationOrStoreSelectModal, { ModalType } from '../organization-or-store-select-modal';
import { Button, Checkbox, Col, Row, Tag } from 'antd';
import { FC, useMemo, useState } from 'react';
import List from 'rc-virtual-list';
import { CloseOutlined } from '@ant-design/icons';

const OrganizationOrStoreField: FC<any> = ({
  orgName,
  shopName,
  options,
  form,
  disabled,
  showAutoAddShop = false,
  autoAddName
}) => {
  const [modalProps, setModalProps] = useState<{
    open: boolean;
    type: ModalType;
    value?: any;
  }>({
    open: false,
    type: ModalType.ORGANIZATION,
    value: undefined
  });

  const orgs = ProForm.useWatch(orgName, form);
  const stores = ProForm.useWatch(shopName, form);
  const autoAdd = ProForm.useWatch(autoAddName, form);

  const disabledOrgs = useMemo(() => {
    return orgs?.filter(({ deleted }) => deleted) || [];
  }, [orgs]);

  const disabledStores = useMemo(() => {
    return stores?.filter(({ deleted }) => deleted) || [];
  }, [stores]);

  const deleteItem = (target, key) => {
    const values = form.getFieldValue(target);
    form.setFieldValue(
      target,
      values?.filter(({ value }) => value !== key)
    );
  };

  return (
    <>
      <Row>
        <Col span={12}>
          <ProForm.Item name={orgName} noStyle>
            <div>
              <Button
                disabled={disabled}
                onClick={() => {
                  setModalProps({
                    open: true,
                    type: ModalType.ORGANIZATION,
                    value: orgs
                  });
                }}
              >
                已选择<span className="mx-1">{orgs?.length || 0}</span>个组织
              </Button>
            </div>
            <div className="mt-4 rounded border border-[#d9d9d9] border-solid h-[100px] mr-2 p-2 overflow-y-auto">
              {orgs?.length > 3000 ? (
                <List data={orgs} height={80} itemHeight={24} itemKey="value">
                  {({ label, value }) => {
                    return (
                      <div className="leading-6 flex ">
                        <span>{label}</span>
                        {!disabled && (
                          <CloseOutlined
                            className="ml-3 text-xs"
                            onClick={() => {
                              deleteItem(orgName, value);
                            }}
                          />
                        )}
                      </div>
                    );
                  }}
                </List>
              ) : (
                orgs?.map(({ label, value, deleted }) => {
                  return (
                    <Tag
                      className="mr-1 mb-1"
                      closeIcon={disabled ? false : true}
                      color={deleted ? 'error' : 'default'}
                      style={{
                        display: 'inline-block'
                      }}
                      onClose={() => {
                        deleteItem(orgName, value);
                      }}
                    >
                      {label}
                    </Tag>
                  );
                })
              )}
            </div>
          </ProForm.Item>
          {disabledOrgs?.length > 0 && (
            <p className="text-[#ff4d4f] mt-1">{disabledOrgs?.map(({ label }) => label).join('、')}已失效</p>
          )}
        </Col>
        <Col span={12}>
          <ProForm.Item name={shopName} noStyle>
            <div className="flex">
              <Button
                disabled={disabled}
                onClick={() => {
                  setModalProps({
                    open: true,
                    type: ModalType.STORE,
                    value: stores
                  });
                }}
              >
                已选择<span className="mx-1">{stores?.length || 0}</span>个门店
              </Button>
              {showAutoAddShop && (
                <ProForm.Item noStyle name={autoAddName}>
                  <Checkbox
                    className="ml-3 leading-8"
                    checked={autoAdd}
                    onChange={(e) => {
                      form?.setFieldValue(autoAddName, e.target.checked);
                    }}
                  >
                    新营业门店自动纳入该任务
                  </Checkbox>
                </ProForm.Item>
              )}
            </div>
            <div className="mt-4 rounded border border-[#d9d9d9] border-solid h-[100px] mr-2 p-2 overflow-y-auto">
              {stores?.length > 3000 ? (
                <List data={stores} height={80} itemHeight={24} itemKey="value">
                  {({ label, value }) => {
                    return (
                      <div className="leading-6 flex ">
                        <span>{label}</span>
                        {!disabled && (
                          <CloseOutlined
                            className="ml-3 text-xs"
                            onClick={() => {
                              deleteItem(shopName, value);
                            }}
                          />
                        )}
                      </div>
                    );
                  }}
                </List>
              ) : (
                stores?.map(({ label, value, deleted }) => {
                  return (
                    <Tag
                      className="mr-1 mb-1"
                      closeIcon={disabled ? false : true}
                      color={deleted ? 'error' : 'default'}
                      style={{
                        display: 'inline-block'
                      }}
                      onClose={() => {
                        deleteItem(shopName, value);
                      }}
                    >
                      {label}
                    </Tag>
                  );
                })
              )}
            </div>
          </ProForm.Item>
          {disabledStores?.length > 0 && (
            <p className="text-[#ff4d4f] mt-1">{disabledStores?.map(({ label }) => label).join('、')}已失效</p>
          )}
        </Col>
      </Row>
      <OrganizationOrStoreSelectModal
        open={modalProps.open}
        type={modalProps.type}
        value={modalProps.value}
        options={options}
        onOk={(checkedList) => {
          form?.setFieldValue(modalProps.type === ModalType.ORGANIZATION ? orgName : shopName, checkedList);
          setModalProps({
            ...modalProps,
            open: false
          });
        }}
        onCancel={() => {
          setModalProps({
            type: ModalType.ORGANIZATION,
            open: false
          });
        }}
      />
    </>
  );
};

export default OrganizationOrStoreField;
