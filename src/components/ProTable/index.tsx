import {
  ActionType as AntdActionType,
  ParamsType,
  ProTable as AntdProTable,
  ProTableProps,
} from '@ant-design/pro-components';
import { useImperativeHandle, useRef } from 'react';

export type ActionType = AntdActionType & {
  /** 删除一行成功后执行，自动判断是否返回上一页 */
  delOneAndReload: () => void;
};

const ProTable = <
  DataType extends Record<string, any>,
  Para<PERSON> extends ParamsType = ParamsType,
  ValueType = 'text',
>({
  actionRef: propsActionRef,
  onDataSourceChange,
  ...props
}: ProTableProps<DataType, Params, ValueType>) => {
  const actionRef = useRef<ActionType>();
  const dataSourceRef = useRef<DataType[]>();

  const delOneAndReload = () => {
    const current = actionRef.current?.pageInfo?.current || 1;
    // 当前页数据只有一条，且不是第一页
    if (dataSourceRef.current?.length === 1 && current > 1) {
      actionRef.current?.setPageInfo?.({ current: current - 1 });
    } else {
      actionRef.current?.reload();
    }
  };

  useImperativeHandle(propsActionRef, () => ({
    ...(actionRef.current as AntdActionType),
    delOneAndReload,
  }));

  return (
    <AntdProTable
      {...props}
      actionRef={actionRef}
      onDataSourceChange={(dataSource) => {
        dataSourceRef.current = dataSource;
        onDataSourceChange?.(dataSource);
      }}
    />
  );
};

export default ProTable;
