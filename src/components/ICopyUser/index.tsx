import { getInspectorDetail } from '@/http/apis/center-control';
import { useRequest } from 'ahooks';
import { Select, Tooltip } from 'antd';
import { useState } from 'react';

type IProps = {
  value?: string[];
  onChange?: (value: number[]) => void;
};

const ICopyUser = ({ onChange, value }: IProps) => {
  const [data, setData] = useState([]);

  const { loading } = useRequest(
    async () => {
      let res = await getInspectorDetail({
        bizType: 14,
        roleCategory: 1
        // nickname: searchText,
      });
      return res;
    },

    {
      refreshDeps: [],
      onSuccess: (data) => {
        setData(data);
      }
    }
  );

  return (
    <Select
      style={{ overflowY: 'auto' }}
      optionFilterProp="label"
      onChange={(val) => {
        onChange?.(val);
      }}
      showSearch
      maxLength={50}
      value={value}
      mode="multiple"
      placeholder="请选择抄送人"
    >
      {data?.map((v: any) => {
        return (
          <Select.Option title={v?.nickname} key={v?.userId} value={v?.userId} label={`${v?.nickname}`}>
            {v?.nickname}
          </Select.Option>
        );
      })}
    </Select>
  );
};

export default ICopyUser;
