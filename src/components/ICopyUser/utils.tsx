import { TreeData } from '@/pages/task/cloud/camera/Operator/Task/createTask/utils';
import { TResourceTree } from '@/services/organization';
import { ClockCircleOutlined } from '@ant-design/icons';
import { sortBy } from 'lodash';

// 拼接组织跟门店数据
export const treeOrgShopV1 = (data?: TResourceTree[]) => {
  let orgShopData: TreeData[] = [];
  const taskUserList: any = [];
  if (!data) {
    return orgShopData;
  }
  const dig = (data?: any[], parentGroup: { groupName?: string; groupId?: number } = {}): TreeData[] => {
    if (!data || !data.length) {
      return [];
    }
    const value: any = [];
    data.forEach((v) => {
      if (!v) {
        return;
      }
      const userList: any = [];
      (v.children || [])?.forEach((e: any) => {
        if (!e) {
          return;
        }
        if (e.type === 'USER' && e.permission) {
          userList.push({
            ...e,
            userId: e.id
          });
        }
      });
      if (v.type === 'USER' && v.permission) {
        const index = taskUserList.findIndex((e) => e.userId === v.id);
        const item: any = {
          roleList: v.roleList,
          userName: v.name,
          userId: v.id,
          groupId: parentGroup.groupId
        };
        if (parentGroup?.groupName) {
          if (index >= 0) {
            item.groupName = [...taskUserList[index].groupName, parentGroup.groupName];
            taskUserList[index] = item;
          } else {
            item.groupName = [parentGroup.groupName];
            taskUserList.push(item);
          }
        }
      }
      if (v.type === 'SHOP') {
        const group = { groupName: v.name, groupId: v.id };
        value.push({
          id: `SHOP-${v?.id}`,
          name: (
            <i>
              {' '}
              {v?.name} {v?.businessStatus === 'PREPARING' && <ClockCircleOutlined twoToneColor="gray" />}
            </i>
          ),
          userList: userList,
          children: v.children && dig(v.children || [], group)
        });
      }
      if (v.type === 'ORGANIZATION') {
        const group = { groupName: v.name, groupId: v.id };
        const children = v.children && dig(v.children || [], group);
        value.push({
          id: v?.id,
          name: v?.name,
          sortNum: v?.sortNum,
          userList: userList,
          children: sortBy(children, 'sortNum')
        });
      }
    });
    return value;
  };
  orgShopData = dig(data);

  return { orgShopData, taskUserList };
};
