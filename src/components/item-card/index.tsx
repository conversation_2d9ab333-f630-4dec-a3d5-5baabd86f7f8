import { FC, ReactElement, ReactNode } from 'react';

interface ItemCardProps {
  title: ReactNode;
  children?: ReactElement | ReactElement[] | never[];
}

const ItemCard: FC<ItemCardProps> = ({ title, children }) => {
  return (
    <div>
      <div className="text-base leading-none border-l-[#D23127] border-l-[3px] border-solid border-y-0 border-r-0 pl-2">
        {title}
      </div>
      {children && <div className="py-4 px-5">{children}</div>}
    </div>
  );
};

export default ItemCard;
