import { DatePicker } from "antd";
import { FC } from "react";
import dayjs from "dayjs";
import classnames from "classnames";

interface NatureWeekProps {
  value?: any;
  style?: any;
  onChange?: (value: any) => void;
}

const NatureWeek: FC<NatureWeekProps> = ({ style, value, onChange }) => {
 
  return (
    <div  className={classnames("tstd-composite-component")}>
      <DatePicker
        value={value?.[0]}
        style={style}
        // allowClear={false}
        onChange={(val) => {
          let newValue: any = [];
          if (val) {
            newValue = [dayjs(val).startOf("week"), dayjs(val).endOf("week")];
          }

          onChange?.(newValue);
        }}
      />
      ~ <DatePicker value={value?.[1]} disabled />
    </div>
  );
};

export default NatureWeek;
