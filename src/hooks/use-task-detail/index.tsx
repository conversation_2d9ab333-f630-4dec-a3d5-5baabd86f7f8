import { useEffect } from 'react';
import useTaskDetailReducer from './reducer';
import { NotFilledItemHandleType, StrategyRoutineType, StrategyTaskStatus } from '@/constants/strategy';
import {
  getStrategyDiagnosticInfo,
  getStrategyTaskInfo,
  getStrategyTaskReviewSummary,
  getStrategyTaskStatistics,
  getStrategyTaskWorksheet,
  getStrategyTutorTaskExamDetail,
} from '@/http/apis/report-center';

interface useTaskDetailProps {
  taskId?: string | number;
  hasReview?: boolean;
  notFilledItemHandleType?: NotFilledItemHandleType;
  needStatistics?: boolean; // 是否请求汇总数据
  needReviewSummary?: boolean; // 是否请求点评总结数据
  needExam?: boolean; // 是否请求考试详情
}

const useTaskDetail = ({
  taskId,
  hasReview = false,
  notFilledItemHandleType,
  needStatistics,
  needReviewSummary,
  needExam,
}: useTaskDetailProps) => {
  const [detail, dispatch] = useTaskDetailReducer();

  const requestTaskInfo = async (taskId) => {
    const info = await getStrategyTaskInfo({ id: taskId });

    return info;
  };

  const requestTaskWorksheets = async ({ taskId, hasReview, notFilledItemHandleType }) => {
    const worksheets = await getStrategyTaskWorksheet({ id: taskId, hasReview, notFilledItemHandleType });

    return worksheets?.data?.map((worksheet: any) => {
      return {
        ...worksheet,
        data: worksheet?.data?.map((category: any) => {
          return {
            ...category,
            data: category?.data?.map((item: any) => {
              let reviewResult = item?.reviewResult;

              let result: any = [];

              if (!item?.reviewResult) {
                const reviewTaskIds: number[] = [];

                result = item?.result?.filter((reviewItem) => {
                  const { taskType, taskId: reviewTaskId } = reviewItem;

                  if (
                    taskType === 'REVIEW' &&
                    item?.reviewTaskId === reviewTaskId &&
                    // ai自动识别结果
                    !reviewItem?.autoReviewResult
                  ) {
                    reviewResult = { ...reviewItem };

                    return false;
                  }

                  reviewTaskIds.push(reviewTaskId);

                  return true;
                });

                if (!reviewTaskIds.includes(item?.reviewTaskId) && !item?.reviewResult) {
                  reviewResult = {};
                }
              }

              return { ...item, result, reviewResult };
            }),
          };
        }),
      };
    });
  };

  const requestTaskStatistics = async ({ taskId, notFilledItemHandleType }) => {
    const statistics = await getStrategyTaskStatistics({ id: taskId, notFilledItemHandleType });

    return statistics;
  };

  const requestTaskReviewSummary = async (taskId) => {
    const reviewSummarys = await getStrategyTaskReviewSummary(taskId);

    return reviewSummarys;
  };

  useEffect(() => {
    if (taskId) {
      dispatch({ ...detail, loading: true });

      const requestPromises = [
        requestTaskInfo(taskId),
        requestTaskWorksheets({ taskId, hasReview, notFilledItemHandleType }),
      ];

      Promise.all(requestPromises)
        .then(async ([info, worksheets]: any) => {
          let statistics: any;
          let reviewSummarys: any;
          let examDetail: any;
          let diagnosticInfo: any;

          if (needStatistics) {
            statistics = await requestTaskStatistics({ taskId, notFilledItemHandleType });
          }

          if (needReviewSummary) {
            reviewSummarys = await requestTaskReviewSummary(taskId);
          }

          if (
            needExam &&
            info?.taskStatus !== StrategyTaskStatus.CANCELED &&
            info?.taskSubType === StrategyRoutineType.FOOD_SAFETY_ARRIVE_SHOP
          ) {
            examDetail = await getStrategyTutorTaskExamDetail(+taskId);
          }

          if (info?.taskSubType === StrategyRoutineType.DIAGNOSTIC) {
            diagnosticInfo = await getStrategyDiagnosticInfo({ taskId: +taskId });
          }

          dispatch({
            info,
            worksheets,
            statistics,
            reviewSummarys,
            examDetail,
            diagnosticInfo,
            loading: false,
            pageError: false,
          });
        })
        .catch((error) => {
          console.log('error :>> ', error);
          // 添加页面异常标识
          dispatch({ ...detail, pageError: true });
        });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [taskId, needStatistics, hasReview, notFilledItemHandleType]);

  return { detail };
};

export default useTaskDetail;
