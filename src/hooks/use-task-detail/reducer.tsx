import { useReducer } from 'react';

const reducer = (state: any, action: any) => {
  return { ...state, ...action };
};
const useTaskDetailReducer = () => {
  const [detail, dispatch] = useReducer(reducer, {
    info: {},
    worksheets: [],
    statistics: {},
    reviewSummarys: [],
    examDetail: {},
    loading: false,
  });

  return [detail, dispatch];
};

export default useTaskDetailReducer;
