import OSS from 'ali-oss';
import { message } from 'antd';
import dayjs from 'dayjs';
import { nanoid } from 'nanoid';
import { getOSSToken, TWatermark, uploadImage, uploadVideo, uploadXlxs } from '@/http/apis/oss';

const messageStr: any = {
  ConnectionTimeoutError: '上传文件超时，请稍后再试',
  RequestError: '上传文件失败，请稍后再试',
};

export const filterErrorImg = (e: { status: string; contentType: any; url: any }[]) => {
  if (!e) return;

  const images: any[] = [];

  e.forEach((e: { status: string; contentType: any; url: any }) => {
    if (e?.status && e.status === 'error') return;

    if (e.contentType && e.url) {
      images.push(e);
    }
  });

  return images;
};

export const useOSSClient = (fileBusinessType) => {
  const getToken = async () => {
    let token;

    const tokenStr = localStorage.getItem('ALI_OSS_TOKEN');

    const tokenObj = tokenStr ? JSON.parse(tokenStr || '') : null;

    if (tokenObj && tokenObj.expiration >= dayjs().unix()) {
      token = tokenObj;
    } else {
      token = await getOSSToken({ fileBusinessType });
      localStorage.setItem('ALI_OSS_TOKEN', JSON.stringify(token));
    }

    return token;
  };

  const uploadFile = async (
    file: File,
    watermark: boolean = false,
    imageSource?: string,
    extra?: any,
  ): Promise<any> => {
    if (!file) {
      message.error('上传文件错误');

      return Promise.reject('上传文件错误');
    }

    if (file?.name?.length > 255) {
      message.error('文件名过长,不超过255字符');

      return Promise.reject('文件名过长,不超过255字符');
    }

    if (file.size >= 1024 * 1024 * 20) {
      message.error('上传文件不可大于 20m');

      return Promise.reject('上传文件不可大于 20m');
    }

    const token = await getToken();
    const client = token
      ? new OSS({
          // yourRegion填写Bucket所在地域。以华东1（杭州）为例，yourRegion填写为oss-cn-hangzhou。
          region: 'oss-cn-shanghai',
          accessKeyId: token.accessKeyId,
          accessKeySecret: token.accessKeySecret,
          stsToken: token.securityToken,
          // 填写Bucket名称。
          bucket: token.bucket,
          // 刷新临时访问凭证。
          secure: true,
          refreshSTSToken: async () => {
            const newToken: any = await getOSSToken({ fileBusinessType });

            return {
              accessKeyId: newToken?.accessKeyId,
              accessKeySecret: newToken?.accessKeySecret,
              stsToken: newToken?.securityToken,
            };
          },
        })
      : null;

    const uid = nanoid();
    const name = `${token?.prefix}${uid}`;
    const fileName = encodeURIComponent(file.name || '');

    console.log(fileName?.length, '=222');

    return new Promise((resolve, reject) => {
      return client
        ?.put(name, file, {
          headers: {
            'Content-Disposition': `attachment;filename=${fileName}`,
          },
        })
        .then(async (response) => {
          const fileExtension = fileName?.split('.').pop()?.toLowerCase();
          const params = {
            bucket: token?.bucket!,
            key: name,
            originName: file.name,
            ...extra,
          };
          const watermarkType = watermark
            ? imageSource === 'COMPANY_WATER_MK'
              ? TWatermark.company
              : extra
                ? TWatermark.extra
                : TWatermark.default
            : TWatermark.none;

          file.type.startsWith('image');

          if (fileExtension === 'mp4') {
            const res = uploadVideo(params);

            resolve(res as any);
          }

          if (file.type.startsWith('image')) {
            const res = await uploadImage(params, watermarkType);

            resolve({ ...res, ...params } as any);
          }

          if (fileExtension === 'pdf') {
            const res = uploadImage(params, watermarkType);

            resolve(res as any);
          }

          if (fileExtension === 'xlsx') {
            const payload = {
              bucket: token?.bucket!,
              key: name,
            };
            const res = await uploadXlxs(payload);

            resolve({ ...payload, originName: decodeURIComponent(fileName), url: res } as any);
          }

          resolve({
            ...params,
            name: decodeURIComponent(fileName),
            url: response.url,
            contentType: file.type,
            fileType: fileExtension,
          });
        })
        .catch((e: any) => {
          message.error(messageStr[e.code || 'RequestError']);

          const error = { ...e, message: messageStr[e.code || 'RequestError'] };

          reject(error);
        });
    });
  };

  return { uploadFile };
};
