import { useRef } from 'react';
import { ProColumns, ProTable, ProTableProps } from '@ant-design/pro-components';
import { Modal, ModalProps } from 'antd';

const useProTableModal = () => {
  const modalRef: any = useRef();

  const showModal = ({
    title,
    columns,
    request,
    width,
  }: {
    title: string;
    width?: ModalProps['width'];
    columns: ProColumns[];
    request?: ProTableProps<any, any>['request'];
  }) => {
    return (modalRef.current = Modal.info({
      title,
      closable: true,
      icon: null,
      footer: null,
      width,
      content: (
        <ProTable
          options={false}
          search={false}
          columns={columns}
          pagination={{
            defaultPageSize: 10,
          }}
          tableRender={(_p, _d, { table }) => {
            return <div className="mt-2">{table}</div>;
          }}
          request={request}
        />
      ),
    }));
  };

  return {
    showModal,
  };
};

export default useProTableModal;
