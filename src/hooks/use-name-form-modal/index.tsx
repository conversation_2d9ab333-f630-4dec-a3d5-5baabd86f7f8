import { useRef } from 'react';
import { ProForm, ProFormItemProps, ProFormProps, ProFormText } from '@ant-design/pro-components';
import { InputProps, Modal, ModalProps } from 'antd';

const useNameFormModal = () => {
  const modalRef: any = useRef();
  const [form] = ProForm.useForm();

  const showModal = ({
    title,
    label,
    name,
    rules,
    maxLength,
    onOk,
    initialValues,
    placeholder,
  }: {
    title: ModalProps['title'];
    maxLength?: InputProps['maxLength'];
    onOk?: (value: any) => Promise<any>;
    initialValues?: ProFormProps['initialValues'];
    placeholder?: string;
  } & Pick<ProFormItemProps, 'label' | 'name' | 'rules'>) => {
    if (initialValues) {
      form?.setFieldsValue(initialValues);
    }

    return (modalRef.current = Modal.confirm({
      title,
      closable: true,
      okText: '保存',
      cancelText: '取消',
      icon: null,
      content: (
        <ProForm submitter={false} form={form} layout="horizontal" className="mt-4">
          <ProFormText
            label={label}
            name={name}
            rules={rules ?? [{ required: true, message: placeholder ?? '请输入名称' }]}
            fieldProps={{
              maxLength,
              showCount: maxLength > 0 ? true : false,
              placeholder,
            }}
          />
        </ProForm>
      ),
      onOk: async () => {
        return await form?.validateFields().then(() => {
          return onOk?.(form?.getFieldsValue())?.then(() => {
            form?.resetFields();
          });
        });
      },
      onCancel: () => {
        form?.resetFields();
      },
    }));
  };

  return {
    showModal,
  };
};

export default useNameFormModal;
