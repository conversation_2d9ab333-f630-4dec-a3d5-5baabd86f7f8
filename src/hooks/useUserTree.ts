import { SupervisionNode } from '@/services/types/supervisorOper';
import { useMemo } from 'react';
import { sortBy } from 'lodash';
import resourceTree from '@/mobx/resourceTree';

export type ArchitectureNode = TreeWith<
  Omit<SupervisionNode, 'userList'> & {
    key: string;
    title: string;
    type: string;
    value: number | string;
    disabled?: boolean;
    // shopList?: (ShopInfo & { title: string; value: string })[];
  }
>;

// const processUserList = (users: UserInfo[]) =>
//   users
//     ? users.map((user) => {
//         return {
//           // 为了解决userId重复问题
//           value: user.userId + '-' + Math.random(),
//           title: user.userName,
//           type: 'user',
//         };
//       })
//     : [];

// const dig: (
//   tags?: (SupervisionNode & { disabled?: boolean })[],
//   parent?: string,
//   isBrand?: boolean
// ) => ArchitectureNode[] = (tags, parent = '', isBrand = true) => {
//   if (!tags || !tags.length) return [];

//   return tags.map((node) => {
//     const { childList, id, name, userList } = node;

//     const org = isBrand ? '' : `${parent ? parent + ' - ' : ''}${name}`;

//     // hasChild, 如果有子集children有门店!v.disabled，或者当前下有门店tempList，就说明此节点有门店。
//     const children = dig(childList, org, false);
//     const tempList = processUserList(userList);
//     const hasChild = tempList.length || children.some((v) => !v.disabled);

//     return {
//       // ...node,
//       value: `g_${id}`,
//       key: `g_${id}`,
//       disabled: !hasChild,
//       // title: id === 0 ? '全部' : name,
//       title: name,
//       type: 'group',
//       children: [...children, ...tempList],
//       // shopList: tempList,
//     } as ArchitectureNode;
//   });
// };

const digV1: (tags?: any, parent?: string, isBrand?: boolean) => ArchitectureNode[] = (
  tags,
  parent = '',
  isBrand = true
) => {
  if (!tags || !tags.length) return [];
  const value: any = [];
  tags.forEach((node: any) => {
    const { children: child, id, name, type, disabled, sortNum } = node;

    if (['ORGANIZATION', 'SHOP'].includes(type)) {
      const org = isBrand ? '' : `${parent ? parent + ' - ' : ''}${name}`;
      const children = digV1(child, org, false);
      if (children?.length > 0) {
        value.push({
          value: `g_${id}_${Math.random()}`,
          key: `g_${id}_${Math.random()}`,
          disabled: !!disabled,
          title: name,
          type: 'group',
          children: sortBy(children, 'sortNum'),
          sortNum: sortNum
          // shopList: tempList,
        });
      }
    }
    if (type === 'USER') {
      value.push({
        // 为了解决userId重复问题
        value: id + '-' + Math.random(),
        title: name,
        type: 'user'
      });
    }
  });

  return value;
};

const getCopyUserIds = (
  copyUsers: {
    id: number;
    name: string;
  }[],
  data: ArchitectureNode[] | undefined
): string[] => {
  if (!data) return [];
  const copyUserIds: string[] = [];
  const processId = (n: string): string => n?.split('-')?.[0];
  const dig = (data: ArchitectureNode[]) => {
    data.forEach((item) => {
      const number = processId(item.value as string);

      if (copyUsers.findIndex((v) => v.id === +number) !== -1) {
        copyUserIds.push(item.value as string);
      }
      if (item.children) {
        dig(item.children);
      }
    });
  };
  dig(data);
  return copyUserIds;
};

const getCopyUsers = (copyUserIds: string[], data: ArchitectureNode[] | undefined): { id: number; name: string }[] => {
  if (!data || !copyUserIds?.length) return [];
  const copyUsers: { id: number; name: string }[] = [];
  const processId = (n: string): string => n?.split('-')?.[0];
  const dig = (data: ArchitectureNode[]) => {
    data.forEach((item) => {
      const number = processId(item.value as string);

      if (copyUserIds.findIndex((v) => +processId(v) === +number) !== -1) {
        copyUsers.push({ id: +number, name: item.title });
      }
      if (item.children) {
        dig(item.children);
      }
    });
  };
  dig(data);
  return copyUsers;
};

export default function useUserTree() {
  const { supervisionLoading: loading, getTreeData } = resourceTree();

  const supervisionTreeData = getTreeData({ withShop: true }, 'supervisionTreeData');

  const data = useMemo(() => {
    return digV1(supervisionTreeData ? supervisionTreeData : []);
  }, [supervisionTreeData]);

  return {
    loading,
    data: data,
    getCopyUserIds,
    getCopyUsers
  };
}
