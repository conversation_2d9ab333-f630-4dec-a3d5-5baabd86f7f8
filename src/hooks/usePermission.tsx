import { userStore } from '@/mobx';

export const usePermission = () => {
  const { permissions } = userStore;

  const hasPermission = (permission: string | string[]) => {
    // 开发分支都有权限
    // if (import.meta.env.DEV) {
    //   return true;
    // }
    if (Array.isArray(permission)) {
      return permissions.some((p) => permission.includes(p));
    }

    return permissions.includes(permission);
  };

  return hasPermission;
};
