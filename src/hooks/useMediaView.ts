const KEY = 'tastien-media-view-info';

const useMediaView = () => {
  const setInfo = (info?: { current?: number; files?: any; }) => {
    localStorage.setItem(KEY, JSON.stringify(info));
  };

  const getInfo = () => {
    return localStorage.getItem(KEY);
  };

  const clearInfo = () => {
    localStorage.removeItem(KEY);
  };

  return { info: getInfo(), setInfo, clearInfo };
};

export default useMediaView;
