import { useRef } from 'react';
import { ProForm, ProFormItem } from '@ant-design/pro-components';
import { Modal, ModalProps } from 'antd';
import { getCloseShopInfo } from '@/http/apis/close-shop';

const useOutOfBusinessModal = () => {
  const modalRef: any = useRef();

  const showModal = async ({ title, shopId }: { title: ModalProps['title']; shopId?: string | number }) => {
    const res: any = await getCloseShopInfo(shopId);

    return (modalRef.current = Modal.info({
      title,
      closable: true,

      icon: null,
      content: (
        <ProForm submitter={false} layout="inline">
          <ProFormItem label="歇业时段">
            {res?.closeStarTime && res.openTime ? `${res?.closeStarTime} ~ ${res.openTime}` : ''}
          </ProFormItem>
        </ProForm>
      ),
    }));
  };

  return {
    showModal,
  };
};

export default useOutOfBusinessModal;
