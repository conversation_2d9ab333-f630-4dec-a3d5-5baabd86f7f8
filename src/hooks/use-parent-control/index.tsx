import { useEffect } from 'react';
import { isInIcestark } from '@ice/stark-app';
import { event, store } from '@ice/stark-data';
import { layoutStore } from '@/mobx';
import useGlobalNavigate from '@/router/useGlobalNavigate';
import { windowOpen } from '@/utils/jump';

const useParentControl = () => {
  const navigate = useGlobalNavigate();

  useEffect(() => {
    if (isInIcestark()) {
      store.on('actionType', (data: string) => {
        switch (data) {
          case 'download':
            event.emit('download-count', 0);
            windowOpen(`/download/export`);
            layoutStore.clearExport();

            break;
        }
      });

      return () => {
        store.off('actionType');
      };
    }
  }, []);
};

export default useParentControl;
