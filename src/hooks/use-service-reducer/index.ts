import { editRole } from './../../services/roles';
import { useReducer } from 'react';

type ReducerAction = {
  key: string;
  value: any;
};

const reducer = (state: any, action: any) => {
  return { ...state, ...action };
};

type ServiceReducerAction = {
  [key: string | number]: {
    beforeRequest?: (dispatch: any) => void;
    request: (...arg) => Promise<any>;
    afterRequest?: (data: any, dispatch: any) => void;
    errorHandler?: (e: any, dispatch: any) => void;
  };
};

const useServiceReducer = (initialState: any, action?: ServiceReducerAction) => {
  const [service, dispatch] = useReducer(reducer, initialState);

  const executeRequest = (name: string, ...arg) => {
    return Promise.resolve().then(() => {
      action?.[name]?.beforeRequest?.(dispatch);
      return action?.[name]
        ?.request?.(...arg)
        .then((data: any) => {
          action?.[name]?.afterRequest?.(data, dispatch);
          return data;
        })
        .catch((e) => {
          action?.[name]?.errorHandler?.(e, dispatch);
          throw e;
        });
    });
  };
  return [service, executeRequest, dispatch];
};

export default useServiceReducer;
