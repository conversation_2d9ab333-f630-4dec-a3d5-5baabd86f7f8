import mockjs from 'mockjs';
import { MockMethod } from 'vite-plugin-mock';

const generateRoles = (count) => {
  const roles: unknown[] = [];

  for (let i = 1; i <= count; i++) {
    roles.push({
      id: i,
      name: `Role ${i}`,
      gender: mockjs.Random.pick(['Male', 'Female']),
      age: mockjs.Random.integer(20, 50),
      nickname: mockjs.Random.word()
    });
  }

  return roles;
};

export default [
  {
    url: '/api/role/all',
    timeout: 1000,
    method: 'get',
    response: () => {
      return {
        success: true,
        data: [
          {
            roleId: 1,
            name: '测试',
            roleType: 1,
            isSystem: true
          }
        ]
      };
    }
  },

  {
    url: '/api/role/list',
    timeout: 1000,
    method: 'get',
    response: (req) => {
      const { page = 1, pageSize = 10 } = req.query;
      const total = 30;
      const startIndex = (page - 1) * pageSize;
      const endIndex = Math.min(startIndex + pageSize, total);
      const roles = generateRoles(endIndex - startIndex + 1).slice(startIndex, endIndex);

      return {
        success: true,
        data: {
          list: roles,
          total
        }
      };
    }
  },

  {
    url: '/api/role/add',
    timeout: 1000,
    method: 'post',
    response: () => {
      return {
        success: true,
        data: null
      };
    }
  },

  {
    url: '/api/role/edit',
    timeout: 1000,
    method: 'post',
    response: () => {
      return {
        success: true,
        data: null
      };
    }
  },

  {
    url: '/api/role/del',
    timeout: 1000,
    method: 'delete',
    response: () => {
      return {
        success: true,
        data: null
      };
    }
  },
  {
    url: '/api/role/batchDel',
    timeout: 1000,
    method: 'post',
    response: () => {
      return {
        success: true,
        data: null
      };
    }
  }
] as MockMethod[];
