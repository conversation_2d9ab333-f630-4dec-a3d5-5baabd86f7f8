// test.ts

import { MockConfig, MockMethod } from 'vite-plugin-mock';

export default [
  {
    url: '/api/user/login',
    method: 'get',
    response: ({ query }) => {
      return {
        success: true,
        data: 'asdsa3dfjh128y7127huqfhif'
      };
    }
  },
  {
    url: '/api/text',
    method: 'post',
    rawResponse: async (req, res) => {
      let reqbody = '';

      await new Promise((resolve) => {
        req.on('data', (chunk) => {
          reqbody += chunk;
        });
        req.on('end', () => resolve(undefined));
      });
      res.setHeader('Content-Type', 'text/plain');
      res.statusCode = 200;
      res.end(`hello, ${reqbody}`);
    }
  }
] as MockMethod[];

// export default function (config: MockConfig) {
//   console.log("config",config);

//   return [
//     {
//       url: '/api/adminLogin',
//       method: 'GET',
//       rawResponse: async (req, res) => {
//         let reqbody = ''
//         await new Promise((resolve) => {
//           req.on('data', (chunk) => {
//             reqbody += chunk
//           })
//           req.on('end', () => resolve(undefined))
//         })
//         res.setHeader('Content-Type', 'text/plain')
//         res.statusCode = 200
//         res.end(`hello, ${reqbody}`)
//       },
//     },
//   ]
// }
