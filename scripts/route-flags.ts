import { createFlag } from './flags';

const ROUTE_FLAGS = {
  serviceParameter: createFlag('serviceParameter', '业务参数设置', {
    sop: createFlag('serviceParameter:sop', 'SOP（标准作业流程）管理', {
      add: createFlag('serviceParameter:sop:add', 'SOP新增'),
      modify: createFlag('serviceParameter:sop:modify', 'SOP修改'),
      del: createFlag('serviceParameter:sop:del', 'SOP删除'),
      see: createFlag('serviceParameter:sop:see', 'SOP查看'),
      push: createFlag('serviceParameter:sop:push', 'SOP发布'),
      manage: createFlag('serviceParameter:sop:manage', 'SOP分类管理'),
      tagManage: createFlag('serviceParameter:sop:tagManage', 'SOP标签管理'),
      batchSettings: createFlag('serviceParameter:sop:batchSettings', '批量设置'),
    }),
    basicSettings: createFlag('serviceParameter:basicSettings', '基础设置'),
    checklist: createFlag('serviceParameter:checklist', '检查表设置', {
      edit: createFlag('serviceParameter:checklist:edit', '编辑检查表'),
      batchEdit: createFlag('serviceParameter:checklist:batchEdit', '批量编辑检查表'),
      tag: createFlag('serviceParameter:checklist:tag', '检查表标签'),
      add: createFlag('serviceParameter:checklist:add', '新增检查表'),
    }),
    taskType: createFlag('serviceParameter:taskType', '常规任务类型管理'),
  }),
  task: createFlag('task', '任务', {
    selfTaskManage: createFlag('task:selfTaskManage', '自检任务管理', {
      task: createFlag('task:selfTaskManage:task', '自检任务', {
        create: createFlag('task:selfTaskManage:task:loop', '创建自检任务'),
      }),
    }),
    routineTaskManage: createFlag('task:routineTaskManage', '常规巡检任务管理', {
      fast: createFlag('task:routineTaskManage:fast', '快速创建巡检计划'),
      batchCreate: createFlag('task:routineTaskManage:batchCreate', '批量创建巡检计划'),
    }),
    // TODO:
    videoTaskManage: createFlag('task:videoTaskManage', '视频云巡检任务', {
      create: createFlag('task:videoTaskManage:create', '创建任务'),
      batch: createFlag('task:videoTaskManage:batch', '批量创建任务'),
      begin: createFlag('task:videoTaskManage:begin', '批量开始巡检'),
      modifyChannels: createFlag('task:videoTaskManage:modifyChannels', '修改通道名称'),
    }),
    aiTaskManage: createFlag('task:aiTaskManage', 'AI巡检任务', {
      analysis: createFlag('task:aiTaskManage:analysis', 'AI图片分析任务', {
        auditing: createFlag('task:aiTaskManage:analysis:auditing', '审核'),
        modify: createFlag('task:aiTaskManage:analysis:modify', '编辑'),
      }),
    }),
    standardManage: createFlag('task:standardTaskManage', '常规任务管理', {
      export: createFlag('task:standardTaskManage:export', '常规任务管理导出'),
    }),
  }),
  report: createFlag('report', '报告', {
    self: createFlag('report:self', '自检报告', {
      following: createFlag('report:self:following', '问题跟踪'),
    }),
    routineInspection: createFlag('report:routineInspection', '常规巡检报告', {
      // TODO:不要巡检报告
      report: createFlag('report:routineInspection:report', '巡检报告', {
        following: createFlag('report:routineInspection:report:following', '问题跟踪'),
        comment: createFlag('report:routineInspection:report:comment', '巡检点评'), // TODO:没做
      }),
    }),
  }),
  rectificationTracking: createFlag('rectificationTracking', '整改跟踪', {
    self: createFlag('rectificationTracking:self', '自检', {
      export: createFlag('rectificationTracking:self:export', '导出'), // TODO:没做
    }),
    routine: createFlag('rectificationTracking:routine', '常规巡检', {
      export: createFlag('rectificationTracking:routine:export', '导出'), // TODO:没做
    }),
    ai: createFlag('rectificationTracking:ai', 'ai巡检', {
      export: createFlag('rectificationTracking:ai:export', '导出'), // TODO:没做
    }),
  }),
  data: createFlag('data', '数据', {
    complete: createFlag('data:complete', '门店完成情况', {
      self: createFlag('data:complete:self', '自检'),
      inspection: createFlag('data:complete:inspection', '巡检'),
    }),
    unqualified: createFlag('data:unqualified', '不合格情况', {
      self: createFlag('data:unqualified:self', '自检不合格情况', {
        export: createFlag('data:unqualified:self:export', '导出'), // TODO:没做
      }),
      inspection: createFlag('data:unqualified:inspection', '巡检不合格情况', {
        export: createFlag('data:unqualified:inspection:export', '导出'),
      }),
    }),
    shopSelfSituation: createFlag('data:shopSelfSituation', '门店自检情况', {}),
    storeClosed: createFlag('data:storeClosed', '临时歇业门店名单'),
    inspectorComplete: createFlag('data:inspectorComplete', '巡检人完成情况'),
    board: createFlag('data:board', '常规巡检看板'),
  }),
  businessManagement: createFlag('businessManagement', '企业管理', {
    shop: createFlag('businessManagement:shop', '门店管理', {
      adjust: createFlag('businessManagement:shop:adjust', '查看定位不准确门店'),
    }),
    organizational: createFlag('businessManagement:organizational', '组织架构'),
    record: createFlag('businessManagement:record', '视频监控查看记录'),
  }),
  dataSource: createFlag('dataSource', '数据源管理', {
    marketAnalysis: createFlag('dataSource:marketAnalysis', '市场分析数据源管理', {
      import: createFlag('dataSource:marketAnalysis:import', '批量导入数据'),
      brand: createFlag('dataSource:marketAnalysis:brand', '品牌管理', {
        import: createFlag('dataSource:marketAnalysis:brand:import', '批量导入数据'),
      }),
    }),
    userSatisfaction: createFlag('dataSource:userSatisfaction', '用户满意度数据源管理', {
      import: createFlag('dataSource:userSatisfaction:import', '批量导入数据'),
      complaint: createFlag('dataSource:userSatisfaction:complaint', '全渠道投诉数据'),
      complaintRes: createFlag('dataSource:userSatisfaction:complaintRes', '差评回复率数据'),
      averageTime: createFlag('dataSource:userSatisfaction:averageTime', '平均出餐时长数据'),
      star: createFlag('dataSource:userSatisfaction:star', '顾客体验星级数据'),
      overtime: createFlag('dataSource:userSatisfaction:overtime', '出餐时长超时数据'),
    }),
  }),
  reportFormsDownload: createFlag('reportFormsDownload', '报表下载', {
    export: createFlag('reportFormsDownload:export', '我的导出'),
  }),
  messageTemplate: createFlag('messageTemplate', '消息管理', {
    template: createFlag('messageTemplate:', '消息模板管理'),
    announcement: createFlag('messageTemplate:announcement', '公告管理'),
    feedback: createFlag('messageTemplate:feedback', '问题反馈'),
  }),
};

export { ROUTE_FLAGS };
