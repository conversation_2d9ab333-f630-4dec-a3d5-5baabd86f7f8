/**
 * 路由标识
 * 标识用于后端权限管理，所以标识必须唯一
 * children中的标识也必须唯一，推荐使用父级标识+子级标识的方式
 * 例如：系统管理的标识为systems，用户管理的标识为systems:users
 */
interface RouteFlag {
  flag: string;
  name: string;
  children?: Record<string, RouteFlag>;
  /**
   * 通过flag查找children中的标识，会递归的查找，如果找不到抛出异常，如果找到返回标识
   * 这样在开发阶段可以通过这个方法来检查标识是否正确设置了
   * @param flag
   */
  find(flag: string): RouteFlag;
}

class RouteFlagNotFoundException extends Error {
  constructor(flag: string) {
    super(`未找到标识为${flag}的路由，请检查路由标识是否正确设置`);
    this.name = 'RouteFlagNotFoundException';
  }
}

/**
 * 创建标识对象的工厂方法
 * @param flag 标识
 * @param name 名称
 * @param children 子级标识
 * @returns
 */
function createFlag(flag: string, name: string, children?: RouteFlag['children']): RouteFlag {
  const find = (flag: string) => {
    if (children) {
      const stack = Object.values(children);
      while (stack.length > 0) {
        const currentFlag = stack.pop()!;
        if (currentFlag.flag === flag) {
          return currentFlag;
        }
        if (currentFlag.children) {
          for (const childFlag of Object.values(currentFlag.children)) {
            stack.push(childFlag);
          }
        }
      }
    }
    throw new RouteFlagNotFoundException(flag);
  };

  return {
    flag,
    name,
    children,
    find,
  };
}

export type { RouteFlag };
export { createFlag, RouteFlagNotFoundException };
