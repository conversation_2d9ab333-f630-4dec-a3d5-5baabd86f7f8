<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0" />
    <title>隐私条款</title>
    <style>
      html,
      body {
        width: 100%;
        white-space: wrap;
      }
      html body,
      p,
      div,
      h2,
      h3,
      h4 {
        margin: 0;
        padding: 0;
      }
      .title {
        text-align: center;
      }
      .text {
        text-indent: 2em;
      }
      .fs-b {
        font-weight: bold;
      }
      .main {
        padding: 0 5%;
        line-height: 1.6;
        word-wrap: break-word;
      }
      .table {
        width: 100%;
        max-width: 100%;
        border-collapse: collapse;
        border: 1px solid #000;
      }
      h2,
      h3,
      h4 {
        margin: 16px 0;
      }
      .table td,
      th {
        border: 1px solid #000;
        padding: 5px;
      }
      .table td {
        word-break: break-word;
      }
    </style>
  </head>
  <body>
    <div class="main">
      <h2 class="title">隐私条款</h2>
      <div>
        <p>发布日期：2024年4月8日</p>
        <p>生效日期：2024年4月8日</p>
        <p>更新日期：2024年10月18日</p>
        <p>前言：</p>
        <p class="text">
          首先感谢您对福州塔斯汀餐饮管理有限公司开发提供的塔塔运营通APP的认可和喜爱，福州塔斯汀餐饮管理有限公司
          (下称塔斯汀，注册地址为福建省福州高新区科技东路12、16、18号华建大厦2号楼23层2301室)
          非常重视您个人隐私的保护和使用，为此在本隐私政策中我们
          (下文的“我们”均指代福州塔斯汀餐饮管理有限公司和塔斯汀品牌门店)将述明在征得您的授权和同意的前提下，如何收集、处理您的个人信息以及如何对其进行使用和保护。
        </p>
        <p class="text">您使用或继续使用我们的服务，即意味着同意我们按照本隐私政策、使用、存储和分享您的相关信息。</p>
        <p class="text">
          如您有关于网络信息安全的投诉和举报，或您对本隐私政策、您的信息的相关事宜有任何问题、意见或建议，请与我们联系(联系邮箱:
          <EMAIL>)，我们会在收到您的问题意见或者建议的30天内进行回复。
        </p>
        <p class="text">
          本隐私政策将帮助您了解以下内容：
          <br />
          一、我们如何收集和使用您以及您最终用户的个人信息
          <br />
          二、我们如何共享、转让、公开披露您以及您最终用户的个人信息
          <br />
          三、我们如何保护您以及您最终用户的个人信息
          <br />
          四、我们如何使用您以及您最终用户的个人信息
          <br />
          五、信息的分享及对外提供
          <br />
          六、我们如何处理人脸数据信息
          <br />
          七、我们如何处理未成年人的信息
          <br />
          八、本隐私政策如何更新
        </p>
      </div>
      <div>
        <h3>一、我们如何收集和使用您以及您最终用户的个人信息</h3>
        <p class="text">
          我们收集信息是为了更好、更优、更准确的完成您所选择的服务。我们仅会出于本隐私政策所述的业务功能需要以及目的，并通常会在征得您同意的情况下收集和使用您以及您最终用户的信息。我们收集信息的方式如下：
        </p>
        <h4>1、在您/最终客户使用服务过程中收集的信息</h4>
        <div>
          <p>
            1.1 当你首次登录，你需要使用企业用户管理员提供的帐号密码登录，会收集你的手机号、脸部图像或视频、面容ID信息
            <br />
            1.2
            当企业用户管理员开启了设备管理，根据你在安装及使用服务过程中的设备型号及授予的权限，企业用户会收集设备相关信息，包括设备型号、操作系统、唯一设备标识符等。
            <br />
            1.3
            为了向您提供服务，我们会向您发送信息、通知或与您进行业务沟通，包括但不限于为保证服务完成所必须的验证码、使用服务时所必要的推送通知。为了保障您可以正常接收推送内容，避免消息遗漏，我们使用了App自启动和关联启动其他App功能。
          </p>
          <div class="fs-b">
            <p>个人信息字段</p>
            <table class="table">
              <thead>
                <tr>
                  <th>SDK服务内容</th>
                  <th>个人信息字段</th>
                  <th>适用场景</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>ReactNative</td>
                  <td>获取设备传感器 监听传感器 SDCard数据读写</td>
                  <td>软件基础架构</td>
                </tr>
                <tr>
                  <td>SENTRY平台（sentry-android,io.sentry.react）</td>
                  <td>获取设备传感器 SDCard数据读写 获取应用安装列表</td>
                  <td>崩溃日志收集</td>
                </tr>
                <tr>
                  <td>拍摄</td>
                  <td>允许应用拍摄照片或录制视频</td>
                  <td>用于反馈问题与收集资料</td>
                </tr>
                <tr>
                  <td>HTTPDNS</td>
                  <td>设备信息，运营商信息，网络信息</td>
                  <td>
                    用于实现 HTTPDNS 域名解析 服务的基本功能,在网络切换时 缓存域名解析结果,用于统计分
                    析客户解析服务使用情况
                  </td>
                </tr>
                <tr>
                  <td>移动推送</td>
                  <td>设备信息,包括操作系统、设备型号,IMEI、 IMSI、OAID、MAC、IP,运营商信息</td>
                  <td>用于识别推送设备,实现向单个 设备推送功能</td>
                </tr>
                <tr>
                  <td>远程日志</td>
                  <td>网络信息,设备信息包括操作系统、 Android_ID、设备型号,IP,运营商信息</td>
                  <td>用于拉取指定设备上的日志并 实现日志上报及缓存功能</td>
                </tr>
                <tr>
                  <td>崩溃分析</td>
                  <td>网络信息,设备信息包括操作系统、传感 器、设备型号,运营商信息</td>
                  <td>用于崩溃日志的上报及分析</td>
                </tr>
                <tr>
                  <td>性能分析</td>
                  <td>网络信息,设备信息包括操作系统、设备型 号,运营商信息</td>
                  <td>用于性能日志上报及分析</td>
                </tr>
                <tr>
                  <td>移动数据分析</td>
                  <td>设备信息,设备信息包括 IMEI、IMS1、 AndroidlD、操作系统、设备序列号,运营 商信息</td>
                  <td>作为统计分析时使用的维度</td>
                </tr>
              </tbody>
            </table>
          </div>
          <p class="text">
            我们将对接入的相关第三方SDK在以下目录中列明。您可以通过相关链接查看第三方的数据使用和保护规则。请注意，第三方SDK可能由于版本升级、策略调整等原因导致其个人信息处理类型发生变化，请以其公示的官方说明为准：
          </p>
          <p class="text">
            阿里金融级实人认证
            <br />
            使用目的：用户在打卡签到/签离人脸拍照/录像认证时使用
            <br />
            数据类型: 设备标识信息(如OAID、IM
            EI号、MAC地址、AndroidID、IDFA、IP)、身份证信息(姓名、身份证号)人脸信息、应用进程信息、传感器信息(如:加速传感器)、WLAN接入点(如SSID、BSSID)、应用进程信息
            <br />
            隐私政策:https://terms.aliyun.com/legal-agreement/terms/suit_bu1_ali_cloud/suit_bu1_ali_cloud202107281509_18386.html?spm=a2c4g.11186623.0.0.168350f3ZcZhv6
            <br />
            处理方式:加密
          </p>
          <p class="text">
            com.aliyun(阿里移动网关、阿里推送)
            <br />
            使用目的:识别推送设备，实现向单个设备推送功能
            <br />
            数据类型: 设备标识信息、获取 IMEL、OAID、IMSI、MAC、应用列表
            <br />
            隐私链接：https://terms.aliyun.com/legal-agreement/terms/suit_bu1_ali_cloud/suit_bu1_ali_cloud202112071542_40792.html?spm=a1zaa.8161610.0.0
          </p>
          <p class="text">
            com.heytap.msp
            <br />
            使用目的:消息推送
            <br />
            数据类型: 匿名用户 ID(OAID)
            <br />
            隐私政策: https://www.oppo.com/cn/privacy
          </p>
          <p class="text">
            com.xiaomi.push
            <br />
            使用目的:识别推送设备，实现向单个设备推送功能
            <br />
            数据类型: 设备标识符((如 GUID))、网络状态、网络制式
            <br />
            隐私政策: https://dev.mi.com/console/doc/detail?pld=1822
          </p>
          <p class="text">
            com.learnium
            <br />
            使用目的:获取设备信息，进行问题排查
            <br />
            数据类型:获取 Android ID、传感器
          </p>
          <p class="text"></p>
          <p class="text">
            Sentry-Android
            <br />
            使用目的:用于程序崩溃信息统计分析，进行问题排查
            <br />
            数据类型:环境:Crash 信息及线程堆栈，ROM/RAM/SD 卡容量、网络/语言等状态
            <br />
            官网链接: https://sentry.io/
          </p>
          <p class="text">
            权限收集
            为向您提供更便捷、更优质的产品及/或服务，努力提升您的体验，我们的移动应用会向设备申请一些必要的系统权限。如果拒绝/撤回授权下表内容，您将无法使用对应功能，但不影响您使用其他服务。
          </p>
          <table class="table">
            <thead>
              <tr>
                <th>权限名称</th>
                <th>使用场景</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td>android.permission.CALLPHONE</td>
                <td>应用需要拨打电话权限拨打电话</td>
              </tr>
              <tr>
                <td>android.permission.BODY_SENSORS</td>
                <td>应用需要获取传感器信息,以轨迹信息</td>
              </tr>
              <tr>
                <td>android.permission.SYSTEM_ALERT_ WINDOW</td>
                <td>应用需要权限弹窗显示</td>
              </tr>
              <tr>
                <td>android.permission.READ_EXTERNAL_STORAGE</td>
                <td>应用需要内存权限读取相册、文件</td>
              </tr>
              <tr>
                <td>android.permission.WRITE_EXTERNAL_STORAGE</td>
                <td>应用需要内存权限写入件以拍照后存储照片</td>
              </tr>
              <tr>
                <td>android.permission.ACCESS_FINE_LOCATION</td>
                <td>应用需要获取精准的(GPS)位置以定位打卡</td>
              </tr>
              <tr>
                <td>android.permission.ACCESS_COARSE_LOCATION</td>
                <td>应用需要获取(基于网络的)大概位置以定位打卡</td>
              </tr>
              <tr>
                <td>android.permission.CAMERA</td>
                <td>应用需要调用相机,以拍照</td>
              </tr>
              <tr>
                <td>com.learnium.RNDevicelnfo</td>
                <td>应用需要 Android ID 的权限,信息收集后进 行问题排查</td>
              </tr>
            </tbody>
          </table>
          <p class="text">
            请你理解，我们向你提供的功能和服务是不断更新和发展的，如果某一功能或服务未在前述说明中且需要收集你的信息，我们会通过页面提示交互流程、网站公告等方式另行向你说明信息收集的内容、范围和目的，以征得你的同意。
          </p>
          <p class="text">
            在使用我们的服务时，我们可能会要求您向我们提供可用于联系或识别您的某些个人身份信息（“个人数据”），个人身份信息可能包括但不限于：
          </p>
          <table class="table">
            <caption>收集个人信息、权限信息</caption>
            <thead>
              <tr>
                <th name="sdk" scope="col">使用SDK名称</th>
                <th name="name" scope="col">第三方名称</th>
                <th name="purpose" scope="col">使用目的</th>
                <th name="permission" scope="col">收集个人信息、权限信息</th>
                <th name="privacy" scope="col">第三方隐私政策</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td>阿里移动推送</td>
                <td>阿里云计算有限公司</td>
                <td>
                  移动推送（Mobile
                  Push）是提供给移动开发者的移动端消息推送服务，通过在App中集成推送功能，进行高效、精准、实时的消息推送，从而使业务及时触达用户，提高用户粘性。
                </td>
                <td>设备标识符和通知接收状态</td>
                <td>
                  https://terms.aliyun.com/legal-agreement/terms/suit_bu1_ali_cloud/suit_bu1_ali_cloud202112071754_83380.html?spm=a2c4g.11186623.0.0.598730e5wsglWa
                </td>
              </tr>
              <tr>
                <td>荣耀推送</td>
                <td>荣耀终端有限公司</td>
                <td>
                  荣耀推送服务（HONOR
                  Push）是荣耀公司向开发者提供的消息推送服务，通过服务端与客户端建立一条稳定、可靠的长连接通道，向荣耀手机系统上的APP应用客户端实时推送消息的服务。无论应用进程是否存在，均可正常收到消息。
                </td>
                <td>设备标识符和通知接收状态</td>
                <td>
                  https://developer.hihonor.com/cn/kitdoc?category=%E5%9F%BA%E7%A1%80%E6%9C%8D%E5%8A%A1&kitId=11002&navigation=guides&docId=sdk-data-security.md&token=
                </td>
              </tr>
              <tr>
                <td>vivo推送</td>
                <td>维沃移动通信有限公司</td>
                <td>
                  vivo推送（即Vpush）是vivo公司向开发者提供的消息推送服务，通过在云端与客户端之间建立一条稳定、可靠的长连接，为开发者提供向客户端应用实时推送消息的服务，支持百亿级的通知/消息推送，秒级触达移动用户。
                </td>
                <td>设备标识符和通知接收状态</td>
                <td>https://dev.vivo.com.cn/documentCenter/doc/652</td>
              </tr>
              <tr>
                <td>魅族推送</td>
                <td>珠海市魅族通讯设备有限公司</td>
                <td>
                  集成推送服务通过对各厂商的推送接口进行统一封装，加上自有的长连接服务，实现一次集成，全安卓推送消息。
                </td>
                <td>权限信息获取：读取外置存储卡、读取手机状态和身份(仅SDK)、写入外部存储卡。 个人信息获取：IMEI</td>
                <td>https://open.flyme.cn/docs?id=202</td>
              </tr>
              <tr>
                <td>华为推送</td>
                <td>华为软件技术有限公司</td>
                <td>
                  推送服务（Push
                  Kit）是华为提供的消息推送平台，建立了从云端到终端的消息推送通道。您通过集成推送服务，可以向客户端应用实时推送消息，构筑良好的用户关系，提升用户的感知度和活跃度。
                </td>
                <td>设备标识符和通知接收状态 权限信息获取：检索正在运行的应用</td>
                <td>
                  https://developer.huawei.com/consumer/cn/doc/development/HMSCore-Guides/sdk-data-security-0000001050042177
                </td>
              </tr>
              <tr>
                <td>小米推送</td>
                <td>北京小米移动软件有限公司</td>
                <td>
                  小米推送（MiPush）是小米公司向开发者提供的消息推送服务，通过在云端与客户端之间建立一条稳定、可靠的长连接，为开发者提供向客户端应用实时推送消息的服务，有效地帮助开发者触达用户，提升APP活跃度。
                </td>
                <td>设备标识符和通知接收状态 权限信息获取：查看WLAN连接</td>
                <td>https://dev.mi.com/console/doc/detail?pId=1822</td>
              </tr>
              <tr>
                <td>OPPO推送</td>
                <td>OPPO广东移动通信有限公司</td>
                <td>提供给到外部业务的进行集成的SDK，业务通过该SDK实现离线消息的推送能力。</td>
                <td>设备标识符和通知接收状态</td>
                <td>https://open.oppomobile.com/new/developmentDoc/info?id=11228</td>
              </tr>
              <tr>
                <td>阿里百川组件</td>
                <td>阿里云计算有限公司</td>
                <td>接口可用性统计</td>
                <td>接口可用性统计</td>
                <td>http://developer.alibaba.com/</td>
              </tr>
              <tr>
                <td>华为分析SDK</td>
                <td>华为软件技术有限公司</td>
                <td>
                  华为分析服务（Analytics
                  Kit）是针对移动应用、Web、快应用、快游戏、小程序等产品的一站式用户行为分析平台，贴合业务场景，提供数据采集、数据管理、数据分析、数据应用的一体化解决方案，驱动企业实现精准拉新、产品优化、精益运营、业务增长。
                </td>
                <td>用户行为分析 权限信息获取：读取外置存储卡、写入外部存储卡</td>
                <td>
                  https://developer.huawei.com/consumer/cn/doc/HMSCore-Guides/android-sdk-data-security-0000001050745153
                </td>
              </tr>
              <tr>
                <td>高德定位</td>
                <td>高德软件有限公司</td>
                <td>
                  定位 SDK
                  是一套简单的LBS服务定位接口，您可以使用这套定位API获取定位结果、逆地理编码（地址文字描述）、以及地理围栏功能。
                </td>
                <td>
                  位置信息（经纬度、精确位置、粗略位置）【通过IP
                  地址、GNSS信息、WiFi状态、WiFi参数、WiFi列表、基站信息、信号强度的信息、蓝牙信息、传感器信息（矢量、加速度、压力）、设备信号强度信息获取】
                </td>
                <td>https://lbs.amap.com/pages/privacy/</td>
              </tr>
              <tr>
                <td>高德地图</td>
                <td>高德软件有限公司</td>
                <td>
                  高德地图 SDK
                  是一套地图开发调用接口，开发者可以轻松地在自己的Android应用中加入地图相关的功能，包括：地图显示（含室内、室外地图）、与地图交互、在地图上绘制、兴趣点搜索、地理编码、离线地图等功能。
                </td>
                <td>
                  权限信息获取：访问定位额外命令、查看WLAN连接、获取粗略位置、获取精确位置、读取外置存储卡、读取手机状态和身份、写入外部存储卡
                  个人信息获取：精确位置信息、粗略位置信息、WiFi扫描结果
                </td>
                <td>https://lbs.amap.com/pages/privacy/</td>
              </tr>
              <tr>
                <td>设备标识生成库</td>
                <td>支付宝（中国）网络技术有限公司</td>
                <td>utdid为设备生成唯一deviceid的一个基础类库。</td>
                <td>检索正在运行的应用</td>
              </tr>
              <tr>
                <td>华为HMS Core SDK</td>
                <td>华为软件技术有限公司</td>
                <td>
                  HMS
                  Core提供端、云开放能力，帮助开发者实现应用高效开发、快速增长、商业变现，使能开发者创新，为全球用户提供精品内容、服务及体验。
                  静态代码分析
                </td>
                <td>
                  权限信息获取：查看WLAN连接、摄像头（仅SDK）、读取外置存储卡、写入外部存储卡、 检索正在运行的应用
                  个人信息获取：摄像头信息、DHCP信息
                </td>
              </tr>
              <tr>
                <td>React-native-device-info</td>
                <td></td>
                <td>获取iOS和Android的设备信息</td>
                <td>本机号码</td>
                <td>https://github.com/rebeccahughes/react-native-device-info</td>
              </tr>
              <tr>
                <td>阿里金融级实人认证</td>
                <td></td>
                <td>
                  刷脸认证客户端SDK，帮助您在业务应用（App）中实现刷脸认证功能。
                  仅在品牌员工工作范围内，进行打卡签到/签离人脸认证场景时使用，品牌员工离职后将清除其信息；
                </td>
                <td>脸部图像或视频，人脸面容ID信息</td>
                <td>
                  https://help.aliyun.com/zh/id-verification/financial-grade-id-verification/integration-process-5?spm=a2c4g.11186623.0.0.172c1525kAF803
                </td>
              </tr>
              <tr>
                <td>华为统一扫码SDK</td>
                <td>华为软件技术有限公司</td>
                <td>
                  统一扫码服务（Scan Kit）提供便捷的条形码和二维码扫描、解析、生成能力，帮助您快速构建应用内的扫码功能。
                </td>
                <td>权限信息获取：读取外置存储卡、写入外部存储卡</td>
                <td>
                  https://developer.huawei.com/consumer/cn/doc/development/HMSCore-Guides/sdk-data-security-0000001050043971
                </td>
              </tr>
              <tr>
                <td>华为设备标识服务</td>
                <td>华为软件技术有限公司</td>
                <td>获取设备标识</td>
                <td>权限信息获取：检索正在运行的应用</td>
                <td>
                  https://developer.huawei.com/consumer/cn/doc/development/HMSCore-References/description-0000001064490273
                </td>
              </tr>
              <tr>
                <td>Glide</td>
                <td></td>
                <td>
                  快速高效的 Android
                  开源媒体管理和图像加载框架，它将媒体解码、内存和磁盘缓存以及资源池封装到一个简单易用的界面中。
                </td>
                <td>权限信息获取：读取联系人 个人信息获取：通讯录信息</td>
                <td>https://github.com/bumptech/glide</td>
              </tr>
              <tr>
                <td>FaceBookReact</td>
                <td></td>
                <td>React Native FBSDK 是 iOS Facebook SDK 和 Android Facebook SDK 的包装器</td>
                <td>读取WIFI的BSSID、读取设备IP、读取特定类型传感器列表、读取WIFI的SSID、监视传感器-加速度传感器</td>
                <td>https://developers.facebook.com/docs/react-native</td>
              </tr>
              <tr>
                <td>sentry日志监控</td>
                <td></td>
                <td></td>
                <td>读取特定类型传感器列表、监视传感器-温度传感器、读取已安装APP信息-加速度传感器</td>
                <td>https://sentry.io/privacy/</td>
              </tr>
              <tr>
                <td>阿里OSS</td>
                <td>阿里云计算有限公司</td>
                <td>
                  欢迎使用阿里云开发者工具包（SDK）。 您可以编写代码调用阿里云SDK来实现对阿里云的产品和服务的访问 。
                  我们为您准备了SDK使用说明，以 便您了解如何获取、安装和调用阿里云SDK。
                </td>
                <td>权限信息获取：读取外置存储卡、写入外部存储卡</td>
              </tr>
              <tr>
                <td>萤石云开放平台</td>
                <td>杭州萤石网络有限公司</td>
                <td>用于查看视频监控</td>
                <td>权限信息获取：查看WLAN连接、已配置WIFI信息、WIFI扫描结果</td>
              </tr>
              <tr>
                <td>React-native-image-picker</td>
                <td></td>
                <td>一个 React Native 模块，允许您使用本机 UI 从设备库或直接从相机中选择媒体。</td>
                <td>权限信息获取：读取外置存储卡、写入外部存储卡</td>
                <td>https://github.com/react-community/react-native-image-picker</td>
              </tr>
              <tr>
                <td>Fresco</td>
                <td></td>
                <td>用于在 Android 应用程序中显示图像。</td>
                <td>权限信息获取：读取外置存储卡、写入外部存储卡、 个人信息获取：通讯录信息</td>
                <td>http://frescolib.org/docs/</td>
              </tr>
              <tr>
                <td>AnetChannel</td>
                <td></td>
                <td></td>
                <td>权限信息获取：查看WLAN连接、检索正在运行的应用</td>
                <td></td>
              </tr>
            </tbody>
          </table>
        </div>
        <h4>2、我们如何使用Cookie技术</h4>
        <p class="text">
          在您未拒绝接受cookies的情况下,我们会在您的计算机上设定或取用cookies,以便您能登录或使用依赖于cookies的我们平台服务或功能。您有权选择接受或拒绝接受cookies。您可以通过修改浏览器设置的方式拒绝接受cookies。但如果您选择拒绝接受cookies,则您可能无法登录或使用依赖于cookies的我们网络服务或功能。通过我们所设cookies所取得的有关信息,将适用本隐私政策。
        </p>
        <h4>3、我们如何存储这些信息</h4>
        <p class="text">
          当企业用户开启消息推送服务功能,我们将存储因使用这些功能所需的必要信息。我们严格遵守法律法规的规定及与用户的约定,将收集的信息用于以下用途。若我们超出以下用途使用你的信息,我们将再次向你进行说明,并征得你的同意。
          <br />
          1.信息存储的地点
          <br />
          我们会按照法律法规规定,将在中国境内收集和产生的个人信息存储于中国境内。
          <br />
          2.信息存储的期限
          <br />
          一般而言,我们仅为实现目的所必需的时间保留你的个人信息。若相关法律法规对我们保存你的个人信息的期限有明确规定的(例如《个人信息保护法》要求个人信息保护影响评估报告和处理情况记录应当至少保存三年)，我们将首先遵守该等规定；没有明确规定的，企业用户可自主设定部分数据的留存期限,我们会根据企业用户的设置留存相关信息,我们不会查看或使用企业用户留存的聊天记录和文件图片。
          <br />
          当我们的产品或服务发生停止运营的情形时,我们将以推送通知、公告等形式通知你,在合理的期限内删除你的个人信息或进行匿名化处理,并立即停止收集个人信息的活动,以及关闭
          第三方应用服务接口,避免第三方服务收集及继续使用个人信息。
        </p>
      </div>
      <div>
        <h3>二、我们如何共享、转让、公开披露您以及您最终用户的个人信息</h3>
        <div>
          <h4>1、共享</h4>
          <p class="text">
            我们对您的信息承担保密义务,不会为满足第三方的营销或非法目的而向其出售或出租您以
            及最终用户的信息。除下列情况外,我们不会与任何第三方共享您最终用户的个人信息:
            <br />
            (1)事先获得个人信息权利主体的同意或授权。
            <br />
            (2)根据法律法规的规定或行政或司法机构的要求。
            <br />
            (3)如您最终用户是适格的知识产权投诉人并已提起投诉,应被投诉人要求,向被投诉人披露,
            以便双方处理可能的权利纠纷。
            <br />
            (4)只有共享您最终用户的信息,才能提供服务,或处理您最终用户与他人的纠纷或争议。我们
            会与共享的合作伙伴签署严格的保密协定,要求他们按照我们的隐私政策以及采取严格的数 据安全措施来处理和使用数据。
            <br />
            (5)与关联公司间共享:我们可能会与我们的关联公司和/或其指定的服务提供商共享必要的
            个人信息,且受本隐私政策中所声明目的的约束,如果我们共享您最终用户的个人敏感信息
            或关联公司改变个人信息的使用及处理目的,将再次征求您最终用户的授权同意。
          </p>
        </div>
        <div>
          <h4>2、转让</h4>
          <p class="text">
            我们不会向任何第三方转让您最终用户的个人信息,除非以下情形:
            <br />
            (1)获得您最终用户的同意。
            <br />
            (2)在涉及合并、收购或破产清算时,如涉及到个人信息转让,我们会在要求新的持有您最终用
            户个人信息的公司、组织继续受此隐私政策的约束,否则我们将要求该公司、组织重新向您 最终用户征求授权同意。
            <br />
            (3)根据法律法规的规定或行政或司法机构的要求。
          </p>
        </div>
        <div>
          <h4>3、公开披露</h4>
          <p class="text">
            我们仅会在以下情况下,公开披露您最终用户的个人信息:
            <br />
            (1)获得您或您最终用户明确同意;
            <br />
            (2)基于法律的披露:在法律、法律程序、诉讼或政府主管部门强制性要求的情况下,我们可能
            会公开披露您或您最终用户的个人信息。
            <br />
            (3)在紧急情况下,经合理判断是为了保护我们、我们的客户、最终用户或其他人的重要合法 权益。
          </p>
        </div>
      </div>
      <div>
        <h3>三、我们如何保护您以及您最终用户的个人信息</h3>
        <p class="text">
          我们努力为用户的信息安全提供保障,以防止信息的丢失、不当使用、未经授权访问或披露。我们将在合理的安全水平内使用各种安全保护措施以保障信息的安全。例如,我们会使用加密技术、匿名化处理等手段来保护你的个人信息。我们建立专门的管理制度、流程和组织以保障信息的安全。例如,我们严格限制访问信息的人员范围,要求他们遵守保密义务,并进行审计。若发生个人信息泄露等安全事件,阻止安全事件扩大,并以推送通知、公告等形式告知你安全事件的情况、事件可能对你的影响以及我们将采取的补救措施。我们还将按照法律法规和监管部门要求,上报个人信息安全事件的处置情况。
        </p>
      </div>
      <div>
        <h3>四、我们如何使用您以及您最终用户的个人信息</h3>
        <p class="text">
          1.我们在你使用我们服务过程中收集相关的信息是为了向我们用户打造和提供更好的服务。我们会将收集的信息用于以下用途:
          <br />
          1.1.安全保障:为保障你以及所有我们用户的安全,我们会利用相关信息协助提升我们服务的安全性和可靠性,包括检测、防范和应对可能危害我们、我们的用户或公众的欺诈行为、滥用行为、非法行为、安全风险和技术问题等;
          <br />
          1.2.为了遵从相关法律法规、部门规章、政府指令的相关要求。
          <br />
          目前,我们不会将你的个人信息用于用户画像分析、个性化推荐或广告用途。如我们使用你的个人信息,超出了与收集时所声称的目的及具有直接或合理关联的范围,我们将在使用你的个人信息前,再次向你告知并征得你的明示同意。
        </p>
        <p class="text">
          2.根据相关法律法规以及国家标准,在以下情况下我们可能会收集、使用你的个人信息而 无需征求你的授权同意:
          <br />
          2.1.与国家安全、国防安全等国家利益直接相关的;与公共安全、公共卫生、公众知情等重 大公共利益直接相关的;
          <br />
          2.2.与犯罪侦查、起诉、审判和判决执行等直接相关的;
          <br />
          2.3.出于维护你或其他个人的生命、财产等重大合法权益但又很难得到你本人同意的:
          <br />
          2.4.所收集的个人信息时你自行向社会公众公开的;
          <br />
          2.5.从合法公开披露的信息中收集的个人信息,如合法的新闻报道、政府信息公开等渠道;
          <br />
          2.6.根据你要求签订和履行合同所必需的;
          <br />
          2.7.用于维护所提供的产品或服务的安全稳定运行所必需的。如发现、处置产品或服务的故障;
          <br />
          2.8.法律法规规定的其他情形。
        </p>
      </div>
      <div>
        <h3>五、信息的分享及对外提供</h3>
        <p class="text">
          我们不会共享或转让你的个人信息至第三方,但以下情况除外:
          <br />
          1.获取你的明确同意:经你事先同意,我们可能与第三方分享你的个人信息;
          <br />
          2.为实现外部处理的目的,我们可能会与关联公司或其他第三方合作伙伴(第三方服务供应
          商、承包商、代理、应用开发者等)分享你的个人信息,让他们按照我们的说明、隐私政策以
          及其他相关的保密和安全措施来为我们处理上述信息,并用于向你提供我们的服务,实现"我
          们如何使用信息"部分所述目的。如我们与上述关联公司或第三方分享你的信息,我们将会采
          用加密、匿名化处理等手段保障你的信息安全。
        </p>
        <p class="text">
          我们不会对外公开披露所收集的个人信息,如必须公开披露时,我们会向你告知此次公
          开披露的目的、披露信息的类型及可能涉及的敏感信息,并征得你的明示同意。需要特别说
          明的是,对最终用户相关信息如何在其加入的企业用户工作平台中披露或对外共享,由企业
          用户决定并管理。随着我们业务的持续发展,我们有可能进行合并、收购、资产转让等交易,
          我们将告知你相关情形,按照法律法规及不低于本指引所要求的标准继续保护或要求新的控 制者继续保护你的个人信息。
        </p>
        <p class="text">我们可能基于法律要求或相关部门的执法要求披露你的个人信息。</p>
        <p class="text">
          你的帐号由你所在企业或组织的管理员分配和管理,如你希望注销帐号,可以通过邮件联系
          所在企业或组织的管理员(<EMAIL>)操作处理,将在七个工作日内完成 注销。
        </p>
      </div>
      <div>
        <h3>六、我们如何处理未成年人的信息</h3>
        <p class="text">
          1.人脸数据信息的收集、使用与共享
          <br />
          1.1 我们会收集您的脸部图像或视频、面容ID信息，用于打卡签到/签离人脸拍照/录像认证。
          <br />
          1.2
          除非适用的法律法规或监管规定明确要求，或存在本隐私政策中明确的披露场景，我们不会以任何形式、出于任何目的对除客户以外的外部第三方共享和披露您的人脸数据信息。
          <br />
        </p>
        <p class="text">
          2.人脸数据信息的存储
          <br />
          2.1 我们会按照法律法规规定,将在中国境内收集和产生的人脸数据信息存储于中国境内。
          <br />
          2.2
          若相关法律法规对我们保存您的人脸数据信息的期限有明确规定的（例如《个人信息保护法》要求个人信息保护影响评估报告和处理情况记录应当至少保存三年），我们将首先遵守该等规定；没有明确规定的，我们将保存您的人脸数据信息，直至：
          <br />
          （1）我们收集、使用、分享您人脸数据信息的目的全部实现；
          <br />
          （2）您终止使用我们的产品或服务；
          <br />
          （3）您明确要求我们删除您的个人信息。
        </p class="text">
        <p>2.3 人脸数据信息保存到期后，我们将删除或者采用法律法规认可的其他方式处理您的人脸数据信息。</p>
      </div>
      <div>
        <h3>七、我们如何处理未成年人的信息</h3>
        <p class="text">
          未成年人使用我们的产品或服务前应取得其监护人的同意。如您为未成年人，在使用我们的产品或服务前，应在监护人监护、指导下共同阅读本隐私政策，且应在监护人明确同意和指导下使用我们的产品或服务、提交个人信息。我们根据国家相关法律法规的规定保护未成年人的个人信息，只会在法律法规允许、监护人明确同意或保护您的权益所必要的情况下收集、使用或公开披露未成年人的个人信息。
        </p>
      </div>
      <div>
        <h3>八、本隐私政策如何更新</h3>
        <p class="text">
          本隐私政策于文首所示的更新日期更新并生效。本隐私政策为我们统一适用的一般性隐私条款。虽然如此，某些特定的服务将适用特定的隐私权协议，该等特定服务的隐私权协议构成本隐私政策的一部分。如任何特定服务的隐私权协议与本隐私政策有不一致之处，应当适用这些特定的服务的隐私权协议。
          <br />
          我们可能适时会对本隐私政策进行调整或变更,本隐私政策的任何更新将以标注更新时间的方式公布在我们网站上。未经您明确同意我们不会限制您按照本隐私政策所应享有的权利。对于重大变更,我们还会提供更为显著的通知(包括通过网站或客户端首页公示的方式进行通知甚至向您提供弹窗提示)。本隐私政策所指的重大变更包括但不限于:
          <br />
          1、我们的服务模式发生重大变化。如处理用户信息的目的、用户信息的使用方式等。
          <br />
          2、我们在控制权、组织架构等方面发生重大变化。如业务调整、破产并购等引起的所有者变更等。
          <br />
          3、个人信息共享、转让或公开披露的主要对象发生变化。
          <br />
          4、你参与个人信息处理方面的权利及其行使方式发生重大变化。
          <br />
          5、我们负责处理个人信息安全的团队、联络方式及投诉渠道发生变化。
          <br />
          6、个人信息安全影响评估报告表明存在高风险时。
        </p>
      </div>
    </div>
  </body>
</html>
