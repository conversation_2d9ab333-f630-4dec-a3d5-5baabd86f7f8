<!doctype html>
<html lang="zh">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>塔塔运营通 PDF 预览</title>
    <script src="https://tastientech.com/index.umd.js"></script>
    <style>
      body {
        margin: 0;
        padding: 0;
      }
      .pdf-container {
        width: 100%;
        height: 100vh;
        margin: 0;
        padding: 0;
      }
    </style>
    <script>
      window.onload = function () {
        const urlParams = new URLSearchParams(window.location.search);
        const pdfUrl = atob(urlParams.get('pdfUrl') || '');

        function isValidUrl(string) {
          try {
            new URL(string);
            return true;
          } catch (_) {
            return false;
          }
        }

        if (!isValidUrl(pdfUrl)) {
          document.getElementById('pdf-viewer').innerHTML =
            '<div style="text-align:center;padding:20px;">无效的PDF URL地址</div>';
          return;
        }

        if (pdfUrl) {
          const myPdfPreviewer = jsPreviewPdf.init(document.getElementById('pdf-viewer'), {
            onError: (e) => {
              console.log('发生错误', e);
            },
            onRendered: () => {
              console.log('渲染完成');
            },
          });

          myPdfPreviewer.preview(pdfUrl);
        }
      };
    </script>
  </head>

  <body>
    <div id="pdf-viewer" class="pdf-container"></div>
  </body>
</html>
